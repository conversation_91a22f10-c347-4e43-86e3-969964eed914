{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport { useState, useEffect, useRef } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Help from './pages/Help';\nimport MedicalRecords from './pages/MedicalRecords';\nimport Pharmacy from './pages/Pharmacy';\nimport Doctor from './pages/Doctor';\nimport Patients from './pages/Patients';\nimport HospitalTransfer from './pages/HospitalTransfer';\nimport Exams from './pages/Exams';\nimport Messages from './pages/Messages';\nimport Room from './pages/Room';\nimport Appointment from './pages/Appointment';\nimport AdminDashboard from './pages/AdminDashboard';\nimport BillingPage from './pages/BillingPage';\nimport PatientPortal from './pages/PatientPortal';\nimport './index.css';\n\n// Header component with navigation\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showLogin, setShowLogin] = useState(false);\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isAuthenticated,\n    isAdmin,\n    login\n  } = useAuth();\n  const profileDropdownRef = useRef(null);\n\n  // Handle successful login\n  const handleLogin = userData => {\n    login(userData);\n    setShowLogin(false);\n  };\n\n  // Close profile dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {\n        setShowProfileDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  const getLinkClasses = (path, isMobile = false) => {\n    const baseClasses = isMobile ? \"block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent\" : \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative\";\n    const activeClasses = isMobile ? \"text-blue-700 bg-blue-50 border-blue-600 shadow-sm\" : \"text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200\";\n    const inactiveClasses = isMobile ? \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300\" : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm\";\n    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl font-bold text-gray-800 tracking-wide\",\n                  children: [\"HEALTH\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600\",\n                    children: \"CARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 -mt-1\",\n                  children: \"Portal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden md:flex space-x-8\",\n          children: !user || user.role !== 'patient' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/home\",\n              className: getLinkClasses('/home'),\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: getLinkClasses('/about'),\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/appointment\",\n              className: getLinkClasses('/appointment'),\n              children: \"Appointment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/billing\",\n              className: getLinkClasses('/billing'),\n              children: \"Billing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/help\",\n              className: getLinkClasses('/help'),\n              children: \"Help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/help\",\n              className: getLinkClasses('/help'),\n              children: \"Help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: getLinkClasses('/about'),\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            ref: profileDropdownRef,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowProfileDropdown(!showProfileDropdown),\n              className: \"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-sm font-bold\",\n                  children: user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 capitalize\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: `w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`,\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M19 9l-7 7-7-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), showProfileDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white font-bold\",\n                      children: user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-900\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600 capitalize font-medium\",\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add profile page navigation here if needed\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add settings page navigation here if needed\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 188,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 23\n                }, this), isAdmin() && /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/admin-dashboard\",\n                  onClick: () => setShowProfileDropdown(false),\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Admin Dashboard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-100 pt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    logout();\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Logout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowLogin(true),\n            className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), showLogin && /*#__PURE__*/_jsxDEV(Login, {\n          onLogin: handleLogin,\n          onClose: () => setShowLogin(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"text-gray-700 hover:text-blue-600 p-2\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/home\",\n          className: getLinkClasses('/home', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/about\",\n          className: getLinkClasses('/about', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/appointment\",\n          className: getLinkClasses('/appointment', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Appointment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/billing\",\n          className: getLinkClasses('/billing', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Billing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/patient-portal\",\n          className: getLinkClasses('/patient-portal', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Patient Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/help\",\n          className: getLinkClasses('/help', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Help\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4 pb-3 border-t border-gray-200 mx-4\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 capitalize\",\n                children: user.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                logout();\n                setIsMobileMenuOpen(false);\n              },\n              className: \"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowLogin(true);\n              setIsMobileMenuOpen(false);\n            },\n            className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"+fqRH8n/8Iqyb7dFLHyAWjHrgjw=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = Header;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(PatientPortal, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/home\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/help\",\n              element: /*#__PURE__*/_jsxDEV(Help, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/messages\",\n              element: /*#__PURE__*/_jsxDEV(Messages, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/patients\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'nurse'],\n                children: /*#__PURE__*/_jsxDEV(Patients, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/medical-records\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(MedicalRecords, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/exams\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/pharmacy\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'nurse', 'staff'],\n                children: /*#__PURE__*/_jsxDEV(Pharmacy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/billing\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'staff'],\n                children: /*#__PURE__*/_jsxDEV(BillingPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/patient-portal\",\n              element: /*#__PURE__*/_jsxDEV(PatientPortal, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 28\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/appointment\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'staff'],\n                children: /*#__PURE__*/_jsxDEV(Appointment, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/room\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(Room, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/hospital-transfer\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(HospitalTransfer, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/doctor\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requireAdmin: true,\n                children: /*#__PURE__*/_jsxDEV(Doctor, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin-dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requireAdmin: true,\n                children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 345,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "useLocation", "useState", "useEffect", "useRef", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "ProtectedRoute", "Home", "About", "Help", "MedicalRecords", "Pharmacy", "Doctor", "Patients", "HospitalTransfer", "<PERSON><PERSON>", "Messages", "Room", "Appointment", "AdminDashboard", "BillingPage", "PatientPortal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "showLogin", "setShow<PERSON><PERSON>in", "showProfileDropdown", "setShowProfileDropdown", "location", "user", "logout", "isAuthenticated", "isAdmin", "login", "profileDropdownRef", "handleLogin", "userData", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "isActive", "path", "pathname", "getLinkClasses", "isMobile", "baseClasses", "activeClasses", "inactiveClasses", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "to", "ref", "onClick", "firstName", "char<PERSON>t", "toUpperCase", "name", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "email", "onLogin", "onClose", "_c", "App", "element", "allowedRoles", "requireAdmin", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport { useState, useEffect, useRef } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Help from './pages/Help';\nimport MedicalRecords from './pages/MedicalRecords';\nimport Pharmacy from './pages/Pharmacy';\nimport Doctor from './pages/Doctor';\nimport Patients from './pages/Patients';\nimport HospitalTransfer from './pages/HospitalTransfer';\nimport Exams from './pages/Exams';\nimport Messages from './pages/Messages';\nimport Room from './pages/Room';\nimport Appointment from './pages/Appointment';\nimport AdminDashboard from './pages/AdminDashboard';\nimport BillingPage from './pages/BillingPage';\nimport PatientPortal from './pages/PatientPortal';\nimport './index.css';\n\n// Header component with navigation\nfunction Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showLogin, setShowLogin] = useState(false);\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const location = useLocation();\n  const { user, logout, isAuthenticated, isAdmin, login } = useAuth();\n  const profileDropdownRef = useRef(null);\n\n  // Handle successful login\n  const handleLogin = (userData) => {\n    login(userData);\n    setShowLogin(false);\n  };\n\n  // Close profile dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {\n        setShowProfileDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  const getLinkClasses = (path, isMobile = false) => {\n    const baseClasses = isMobile\n      ? \"block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent\"\n      : \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative\";\n    const activeClasses = isMobile\n      ? \"text-blue-700 bg-blue-50 border-blue-600 shadow-sm\"\n      : \"text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200\";\n    const inactiveClasses = isMobile\n      ? \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300\"\n      : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm\";\n\n    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-800 tracking-wide\">\n                    HEALTH<span className=\"text-blue-600\">CARE</span>\n                  </h1>\n                  <p className=\"text-xs text-gray-500 -mt-1\">Portal</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {/* Show different navigation based on user role */}\n            {!user || user.role !== 'patient' ? (\n              <>\n                <Link to=\"/home\" className={getLinkClasses('/home')}>\n                  Home\n                </Link>\n                <Link to=\"/about\" className={getLinkClasses('/about')}>\n                  About\n                </Link>\n                <Link to=\"/appointment\" className={getLinkClasses('/appointment')}>\n                  Appointment\n                </Link>\n                <Link to=\"/billing\" className={getLinkClasses('/billing')}>\n                  Billing\n                </Link>\n                <Link to=\"/help\" className={getLinkClasses('/help')}>\n                  Help\n                </Link>\n              </>\n            ) : (\n              <>\n                <Link to=\"/help\" className={getLinkClasses('/help')}>\n                  Help\n                </Link>\n                <Link to=\"/about\" className={getLinkClasses('/about')}>\n                  About\n                </Link>\n              </>\n            )}\n          </nav>\n\n          {/* Profile/Login Section */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated() ? (\n              <div className=\"relative\" ref={profileDropdownRef}>\n                <button\n                  onClick={() => setShowProfileDropdown(!showProfileDropdown)}\n                  className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}\n                    </span>\n                  </div>\n                  <div className=\"text-left\">\n                    <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">{user.role}</div>\n                  </div>\n                  <svg className={`w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* Profile Dropdown */}\n                {showProfileDropdown && (\n                  <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\">\n                    <div className=\"px-4 py-3 border-b border-gray-100\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                          <span className=\"text-white font-bold\">\n                            {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}\n                          </span>\n                        </div>\n                        <div>\n                          <div className=\"font-medium text-gray-900\">{user.name}</div>\n                          <div className=\"text-sm text-gray-500\">{user.email}</div>\n                          <div className=\"text-xs text-blue-600 capitalize font-medium\">{user.role}</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"py-2\">\n                      <button\n                        onClick={() => {\n                          // Add profile page navigation here if needed\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                        <span>View Profile</span>\n                      </button>\n\n                      <button\n                        onClick={() => {\n                          // Add settings page navigation here if needed\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                        <span>Settings</span>\n                      </button>\n\n                      {isAdmin() && (\n                        <Link\n                          to=\"/admin-dashboard\"\n                          onClick={() => setShowProfileDropdown(false)}\n                          className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                          </svg>\n                          <span>Admin Dashboard</span>\n                        </Link>\n                      )}\n                    </div>\n\n                    <div className=\"border-t border-gray-100 pt-2\">\n                      <button\n                        onClick={() => {\n                          logout();\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        <span>Logout</span>\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <button\n                onClick={() => setShowLogin(true)}\n                className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\"\n              >\n                Login\n              </button>\n            )}\n          </div>\n\n          {/* Login Modal */}\n          {showLogin && (\n            <Login\n              onLogin={handleLogin}\n              onClose={() => setShowLogin(false)}\n            />\n          )}\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 p-2\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                {isMobileMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\">\n            <Link\n              to=\"/home\"\n              className={getLinkClasses('/home', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Home\n            </Link>\n            <Link\n              to=\"/about\"\n              className={getLinkClasses('/about', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              About\n            </Link>\n            <Link\n              to=\"/appointment\"\n              className={getLinkClasses('/appointment', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Appointment\n            </Link>\n            <Link\n              to=\"/billing\"\n              className={getLinkClasses('/billing', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Billing\n            </Link>\n            <Link\n              to=\"/patient-portal\"\n              className={getLinkClasses('/patient-portal', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Patient Portal\n            </Link>\n            <Link\n              to=\"/help\"\n              className={getLinkClasses('/help', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Help\n            </Link>\n            <div className=\"pt-4 pb-3 border-t border-gray-200 mx-4\">\n              {isAuthenticated() ? (\n                <div className=\"space-y-3\">\n                  <div className=\"text-center\">\n                    <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">{user.role}</div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      logout();\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className=\"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <button\n                  onClick={() => {\n                    setShowLogin(true);\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\"\n                >\n                  Login\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n\nfunction App() {\n  return(\n    <AuthProvider>\n      <Router>\n        <div className=\"min-h-screen bg-white\">\n          <Header />\n\n          {/* Main Content */}\n          <main>\n            <Routes>\n                {/* Default route - starts with patient portal */}\n                <Route path=\"/\" element={<PatientPortal />} />\n\n                {/* Other public routes */}\n                <Route path=\"/home\" element={<Home />} />\n                <Route path=\"/about\" element={<About />} />\n                <Route path=\"/help\" element={<Help />} />\n                <Route path=\"/messages\" element={<Messages />} />\n\n                {/* Patient management - accessible to admin, doctor, nurse */}\n                <Route\n                  path=\"/patients\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse']}>\n                      <Patients />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Medical records - accessible to admin, doctor only */}\n                <Route\n                  path=\"/medical-records\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <MedicalRecords />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Exams - accessible to admin, doctor only */}\n                <Route\n                  path=\"/exams\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <Exams />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Pharmacy - accessible to admin, doctor, nurse, staff */}\n                <Route\n                  path=\"/pharmacy\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse', 'staff']}>\n                      <Pharmacy />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Billing - accessible to admin, doctor, staff */}\n                <Route\n                  path=\"/billing\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>\n                      <BillingPage />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Patient Portal - accessible to everyone */}\n                <Route\n                  path=\"/patient-portal\"\n                  element={<PatientPortal />}\n                />\n\n                {/* Appointments - accessible to admin, doctor, staff only */}\n                <Route\n                  path=\"/appointment\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>\n                      <Appointment />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Room management - accessible to admin, doctor only */}\n                <Route\n                  path=\"/room\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <Room />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Hospital transfers - accessible to admin, doctor only */}\n                <Route\n                  path=\"/hospital-transfer\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <HospitalTransfer />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Doctor management - accessible to admin only */}\n                <Route\n                  path=\"/doctor\"\n                  element={\n                    <ProtectedRoute requireAdmin={true}>\n                      <Doctor />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Admin dashboard - accessible to admin only */}\n                <Route\n                  path=\"/admin-dashboard\"\n                  element={\n                    <ProtectedRoute requireAdmin={true}>\n                      <AdminDashboard />\n                    </ProtectedRoute>\n                  }\n                />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC5F,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAO,aAAa;;AAEpB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMkC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGnC,OAAO,CAAC,CAAC;EACnE,MAAMoC,kBAAkB,GAAGtC,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMuC,WAAW,GAAIC,QAAQ,IAAK;IAChCH,KAAK,CAACG,QAAQ,CAAC;IACfX,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA9B,SAAS,CAAC,MAAM;IACd,MAAM0C,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIJ,kBAAkB,CAACK,OAAO,IAAI,CAACL,kBAAkB,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpFd,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAEDe,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOlB,QAAQ,CAACmB,QAAQ,KAAKD,IAAI;EACnC,CAAC;EAED,MAAME,cAAc,GAAGA,CAACF,IAAI,EAAEG,QAAQ,GAAG,KAAK,KAAK;IACjD,MAAMC,WAAW,GAAGD,QAAQ,GACxB,iGAAiG,GACjG,iFAAiF;IACrF,MAAME,aAAa,GAAGF,QAAQ,GAC1B,oDAAoD,GACpD,yDAAyD;IAC7D,MAAMG,eAAe,GAAGH,QAAQ,GAC5B,0EAA0E,GAC1E,oEAAoE;IAExE,OAAO,GAAGC,WAAW,IAAIL,QAAQ,CAACC,IAAI,CAAC,GAAGK,aAAa,GAAGC,eAAe,EAAE;EAC7E,CAAC;EAED,oBACEnC,OAAA;IAAQoC,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAC/ErC,OAAA;MAAKoC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDrC,OAAA;QAAKoC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDrC,OAAA;UAAKoC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCrC,OAAA;YAAKoC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eAC9CrC,OAAA;cAAKoC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CrC,OAAA;gBAAKoC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,eAChHrC,OAAA;kBAAKoC,SAAS,EAAC,oBAAoB;kBAACE,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAF,QAAA,eACzErC,OAAA;oBAAMwC,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,qLAAqL;oBAACC,QAAQ,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA;gBAAAqC,QAAA,gBACErC,OAAA;kBAAIoC,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,GAAC,QACtD,eAAArC,OAAA;oBAAMoC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAI;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACL9C,OAAA;kBAAGoC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9C,OAAA;UAAKoC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAEtC,CAACzB,IAAI,IAAIA,IAAI,CAACmC,IAAI,KAAK,SAAS,gBAC/B/C,OAAA,CAAAE,SAAA;YAAAmC,QAAA,gBACErC,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,OAAO;cAACZ,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;cAAAM,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,QAAQ;cAACZ,SAAS,EAAEL,cAAc,CAAC,QAAQ,CAAE;cAAAM,QAAA,EAAC;YAEvD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,cAAc;cAACZ,SAAS,EAAEL,cAAc,CAAC,cAAc,CAAE;cAAAM,QAAA,EAAC;YAEnE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,UAAU;cAACZ,SAAS,EAAEL,cAAc,CAAC,UAAU,CAAE;cAAAM,QAAA,EAAC;YAE3D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,OAAO;cAACZ,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;cAAAM,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CAAC,gBAEH9C,OAAA,CAAAE,SAAA;YAAAmC,QAAA,gBACErC,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,OAAO;cAACZ,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;cAAAM,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;cAACyE,EAAE,EAAC,QAAQ;cAACZ,SAAS,EAAEL,cAAc,CAAC,QAAQ,CAAE;cAAAM,QAAA,EAAC;YAEvD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9C,OAAA;UAAKoC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDvB,eAAe,CAAC,CAAC,gBAChBd,OAAA;YAAKoC,SAAS,EAAC,UAAU;YAACa,GAAG,EAAEhC,kBAAmB;YAAAoB,QAAA,gBAChDrC,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAMxC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAC5D2B,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/LrC,OAAA;gBAAKoC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,eAChHrC,OAAA;kBAAMoC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC3CzB,IAAI,CAACuC,SAAS,GAAGvC,IAAI,CAACuC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;gBAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN9C,OAAA;gBAAKoC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrC,OAAA;kBAAKoC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEzB,IAAI,CAAC0C;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpE9C,OAAA;kBAAKoC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEzB,IAAI,CAACmC;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN9C,OAAA;gBAAKoC,SAAS,EAAE,8CAA8C3B,mBAAmB,GAAG,YAAY,GAAG,EAAE,EAAG;gBAAC6B,IAAI,EAAC,MAAM;gBAACiB,MAAM,EAAC,cAAc;gBAAChB,OAAO,EAAC,WAAW;gBAAAF,QAAA,eAC5JrC,OAAA;kBAAMwD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACjB,CAAC,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGRrC,mBAAmB,iBAClBT,OAAA;cAAKoC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBACxGrC,OAAA;gBAAKoC,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDrC,OAAA;kBAAKoC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CrC,OAAA;oBAAKoC,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,eAClHrC,OAAA;sBAAMoC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EACnCzB,IAAI,CAACuC,SAAS,GAAGvC,IAAI,CAACuC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN9C,OAAA;oBAAAqC,QAAA,gBACErC,OAAA;sBAAKoC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEzB,IAAI,CAAC0C;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5D9C,OAAA;sBAAKoC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEzB,IAAI,CAAC+C;oBAAK;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzD9C,OAAA;sBAAKoC,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAEzB,IAAI,CAACmC;oBAAI;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9C,OAAA;gBAAKoC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBrC,OAAA;kBACEkD,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACAxC,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF0B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzGrC,OAAA;oBAAKoC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5ErC,OAAA;sBAAMwD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAqE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1I,CAAC,eACN9C,OAAA;oBAAAqC,QAAA,EAAM;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAET9C,OAAA;kBACEkD,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACAxC,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF0B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzGrC,OAAA;oBAAKoC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,gBAC5ErC,OAAA;sBAAMwD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAqe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7iB9C,OAAA;sBAAMwD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC,eACN9C,OAAA;oBAAAqC,QAAA,EAAM;kBAAQ;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EAER/B,OAAO,CAAC,CAAC,iBACRf,OAAA,CAACzB,IAAI;kBACHyE,EAAE,EAAC,kBAAkB;kBACrBE,OAAO,EAAEA,CAAA,KAAMxC,sBAAsB,CAAC,KAAK,CAAE;kBAC7C0B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzGrC,OAAA;oBAAKoC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5ErC,OAAA;sBAAMwD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAgM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrQ,CAAC,eACN9C,OAAA;oBAAAqC,QAAA,EAAM;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9C,OAAA;gBAAKoC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,eAC5CrC,OAAA;kBACEkD,OAAO,EAAEA,CAAA,KAAM;oBACbrC,MAAM,CAAC,CAAC;oBACRH,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF0B,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,gBAEvGrC,OAAA;oBAAKoC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5ErC,OAAA;sBAAMwD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAA2F;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK,CAAC,eACN9C,OAAA;oBAAAqC,QAAA,EAAM;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN9C,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAAC,IAAI,CAAE;YAClC4B,SAAS,EAAC,+NAA+N;YAAAC,QAAA,EAC1O;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLvC,SAAS,iBACRP,OAAA,CAAClB,KAAK;UACJ8E,OAAO,EAAE1C,WAAY;UACrB2C,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,KAAK;QAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACF,eAGD9C,OAAA;UAAKoC,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBrC,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtD+B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAEjDrC,OAAA;cAAKoC,SAAS,EAAC,SAAS;cAACE,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACgB,MAAM,EAAC,cAAc;cAAAlB,QAAA,EAC3EhC,gBAAgB,gBACfL,OAAA;gBAAMwD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACjB,CAAC,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9F9C,OAAA;gBAAMwD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACjB,CAAC,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzC,gBAAgB,iBACfL,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBrC,OAAA;QAAKoC,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBACjFrC,OAAA,CAACzB,IAAI;UACHyE,EAAE,EAAC,OAAO;UACVZ,SAAS,EAAEL,cAAc,CAAC,OAAO,EAAE,IAAI,CAAE;UACzCmB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;UACHyE,EAAE,EAAC,QAAQ;UACXZ,SAAS,EAAEL,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAE;UAC1CmB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;UACHyE,EAAE,EAAC,cAAc;UACjBZ,SAAS,EAAEL,cAAc,CAAC,cAAc,EAAE,IAAI,CAAE;UAChDmB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;UACHyE,EAAE,EAAC,UAAU;UACbZ,SAAS,EAAEL,cAAc,CAAC,UAAU,EAAE,IAAI,CAAE;UAC5CmB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;UACHyE,EAAE,EAAC,iBAAiB;UACpBZ,SAAS,EAAEL,cAAc,CAAC,iBAAiB,EAAE,IAAI,CAAE;UACnDmB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA,CAACzB,IAAI;UACHyE,EAAE,EAAC,OAAO;UACVZ,SAAS,EAAEL,cAAc,CAAC,OAAO,EAAE,IAAI,CAAE;UACzCmB,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA;UAAKoC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrDvB,eAAe,CAAC,CAAC,gBAChBd,OAAA;YAAKoC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrC,OAAA;cAAKoC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrC,OAAA;gBAAKoC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEzB,IAAI,CAAC0C;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpE9C,OAAA;gBAAKoC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEzB,IAAI,CAACmC;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN9C,OAAA;cACEkD,OAAO,EAAEA,CAAA,KAAM;gBACbrC,MAAM,CAAC,CAAC;gBACRP,mBAAmB,CAAC,KAAK,CAAC;cAC5B,CAAE;cACF8B,SAAS,EAAC,iMAAiM;cAAAC,QAAA,EAC5M;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEN9C,OAAA;YACEkD,OAAO,EAAEA,CAAA,KAAM;cACb1C,YAAY,CAAC,IAAI,CAAC;cAClBF,mBAAmB,CAAC,KAAK,CAAC;YAC5B,CAAE;YACF8B,SAAS,EAAC,qMAAqM;YAAAC,QAAA,EAChN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb;AAAC1C,EAAA,CA7TQD,MAAM;EAAA,QAII3B,WAAW,EAC8BK,OAAO;AAAA;AAAAiF,EAAA,GAL1D3D,MAAM;AA+Tf,SAAS4D,GAAGA,CAAA,EAAG;EACb,oBACE/D,OAAA,CAACpB,YAAY;IAAAyD,QAAA,eACXrC,OAAA,CAAC5B,MAAM;MAAAiE,QAAA,eACLrC,OAAA;QAAKoC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCrC,OAAA,CAACG,MAAM;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGV9C,OAAA;UAAAqC,QAAA,eACErC,OAAA,CAAC3B,MAAM;YAAAgE,QAAA,gBAEHrC,OAAA,CAAC1B,KAAK;cAACuD,IAAI,EAAC,GAAG;cAACmC,OAAO,eAAEhE,OAAA,CAACF,aAAa;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG9C9C,OAAA,CAAC1B,KAAK;cAACuD,IAAI,EAAC,OAAO;cAACmC,OAAO,eAAEhE,OAAA,CAAChB,IAAI;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzC9C,OAAA,CAAC1B,KAAK;cAACuD,IAAI,EAAC,QAAQ;cAACmC,OAAO,eAAEhE,OAAA,CAACf,KAAK;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C9C,OAAA,CAAC1B,KAAK;cAACuD,IAAI,EAAC,OAAO;cAACmC,OAAO,eAAEhE,OAAA,CAACd,IAAI;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzC9C,OAAA,CAAC1B,KAAK;cAACuD,IAAI,EAAC,WAAW;cAACmC,OAAO,eAAEhE,OAAA,CAACP,QAAQ;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGjD9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,WAAW;cAChBmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eACzDrC,OAAA,CAACV,QAAQ;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,kBAAkB;cACvBmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChDrC,OAAA,CAACb,cAAc;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,QAAQ;cACbmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChDrC,OAAA,CAACR,KAAK;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,WAAW;cAChBmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eAClErC,OAAA,CAACZ,QAAQ;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,UAAU;cACfmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eACzDrC,OAAA,CAACH,WAAW;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,iBAAiB;cACtBmC,OAAO,eAAEhE,OAAA,CAACF,aAAa;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,cAAc;cACnBmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eACzDrC,OAAA,CAACL,WAAW;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,OAAO;cACZmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChDrC,OAAA,CAACN,IAAI;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,oBAAoB;cACzBmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACkF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChDrC,OAAA,CAACT,gBAAgB;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,SAAS;cACdmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACmF,YAAY,EAAE,IAAK;gBAAA7B,QAAA,eACjCrC,OAAA,CAACX,MAAM;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGF9C,OAAA,CAAC1B,KAAK;cACJuD,IAAI,EAAC,kBAAkB;cACvBmC,OAAO,eACLhE,OAAA,CAACjB,cAAc;gBAACmF,YAAY,EAAE,IAAK;gBAAA7B,QAAA,eACjCrC,OAAA,CAACJ,cAAc;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACqB,GAAA,GAlIQJ,GAAG;AAoIZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
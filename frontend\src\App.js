import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';
import { AuthProvider, useAuth } from './context/AuthContext';
import Login from './components/Login';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import About from './pages/About';
import Help from './pages/Help';
import MedicalRecords from './pages/MedicalRecords';
import Pharmacy from './pages/Pharmacy';
import Doctor from './pages/Doctor';
import Patients from './pages/Patients';
import HospitalTransfer from './pages/HospitalTransfer';
import Exams from './pages/Exams';
import Messages from './pages/Messages';
import Room from './pages/Room';
import Appointment from './pages/Appointment';
import AdminDashboard from './pages/AdminDashboard';
import BillingPage from './pages/BillingPage';
import PatientPortal from './pages/PatientPortal';
import './index.css';

// Header component with navigation
function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showLogin, setShowLogin] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const location = useLocation();
  const { user, logout, isAuthenticated, isAdmin, login } = useAuth();
  const profileDropdownRef = useRef(null);

  // Handle successful login
  const handleLogin = (userData) => {
    login(userData);
    setShowLogin(false);
  };

  // Close profile dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {
        setShowProfileDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const isActive = (path) => {
    return location.pathname === path;
  };

  const getLinkClasses = (path, isMobile = false) => {
    const baseClasses = isMobile
      ? "block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent"
      : "px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative";
    const activeClasses = isMobile
      ? "text-blue-700 bg-blue-50 border-blue-600 shadow-sm"
      : "text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200";
    const inactiveClasses = isMobile
      ? "text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300"
      : "text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm";

    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;
  };

  return (
    <header className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-800 tracking-wide">
                    HEALTH<span className="text-blue-600">CARE</span>
                  </h1>
                  <p className="text-xs text-gray-500 -mt-1">Portal</p>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link to="/home" className={getLinkClasses('/home')}>
              Home
            </Link>
            <Link to="/about" className={getLinkClasses('/about')}>
              About
            </Link>
            <Link to="/appointment" className={getLinkClasses('/appointment')}>
              Appointment
            </Link>
            <Link to="/billing" className={getLinkClasses('/billing')}>
              Billing
            </Link>
            <Link to="/patient-portal" className={getLinkClasses('/patient-portal')}>
              Patient Portal
            </Link>
            <Link to="/help" className={getLinkClasses('/help')}>
              Help
            </Link>
          </nav>

          {/* Profile/Login Section */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated() ? (
              <div className="relative" ref={profileDropdownRef}>
                <button
                  onClick={() => setShowProfileDropdown(!showProfileDropdown)}
                  className="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}
                    </span>
                  </div>
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{user.role}</div>
                  </div>
                  <svg className={`w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Profile Dropdown */}
                {showProfileDropdown && (
                  <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold">
                            {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="text-xs text-blue-600 capitalize font-medium">{user.role}</div>
                        </div>
                      </div>
                    </div>

                    <div className="py-2">
                      <button
                        onClick={() => {
                          // Add profile page navigation here if needed
                          setShowProfileDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span>View Profile</span>
                      </button>

                      <button
                        onClick={() => {
                          // Add settings page navigation here if needed
                          setShowProfileDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span>Settings</span>
                      </button>

                      {isAdmin() && (
                        <Link
                          to="/admin-dashboard"
                          onClick={() => setShowProfileDropdown(false)}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                          <span>Admin Dashboard</span>
                        </Link>
                      )}
                    </div>

                    <div className="border-t border-gray-100 pt-2">
                      <button
                        onClick={() => {
                          logout();
                          setShowProfileDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span>Logout</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={() => setShowLogin(true)}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                Login
              </button>
            )}
          </div>

          {/* Login Modal */}
          {showLogin && (
            <Login
              onLogin={handleLogin}
              onClose={() => setShowLogin(false)}
            />
          )}

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-gray-700 hover:text-blue-600 p-2"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
            <Link
              to="/home"
              className={getLinkClasses('/home', true)}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              to="/about"
              className={getLinkClasses('/about', true)}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
            </Link>
            <Link
              to="/appointment"
              className={getLinkClasses('/appointment', true)}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Appointment
            </Link>
            <Link
              to="/billing"
              className={getLinkClasses('/billing', true)}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Billing
            </Link>
            <Link
              to="/patient-portal"
              className={getLinkClasses('/patient-portal', true)}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Patient Portal
            </Link>
            <Link
              to="/help"
              className={getLinkClasses('/help', true)}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Help
            </Link>
            <div className="pt-4 pb-3 border-t border-gray-200 mx-4">
              {isAuthenticated() ? (
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{user.role}</div>
                  </div>
                  <button
                    onClick={() => {
                      logout();
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => {
                    setShowLogin(true);
                    setIsMobileMenuOpen(false);
                  }}
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  Login
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}

function App() {
  return(
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-white">
          <Header />

          {/* Main Content */}
          <main>
            <Routes>
                {/* Default route - starts with patient portal */}
                <Route path="/" element={<PatientPortal />} />

                {/* Other public routes */}
                <Route path="/home" element={<Home />} />
                <Route path="/about" element={<About />} />
                <Route path="/help" element={<Help />} />
                <Route path="/messages" element={<Messages />} />

                {/* Patient management - accessible to admin, doctor, nurse */}
                <Route
                  path="/patients"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse']}>
                      <Patients />
                    </ProtectedRoute>
                  }
                />

                {/* Medical records - accessible to admin, doctor only */}
                <Route
                  path="/medical-records"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
                      <MedicalRecords />
                    </ProtectedRoute>
                  }
                />

                {/* Exams - accessible to admin, doctor only */}
                <Route
                  path="/exams"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
                      <Exams />
                    </ProtectedRoute>
                  }
                />

                {/* Pharmacy - accessible to admin, doctor, nurse, staff */}
                <Route
                  path="/pharmacy"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse', 'staff']}>
                      <Pharmacy />
                    </ProtectedRoute>
                  }
                />

                {/* Billing - accessible to admin, doctor, staff */}
                <Route
                  path="/billing"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>
                      <BillingPage />
                    </ProtectedRoute>
                  }
                />

                {/* Patient Portal - accessible to everyone */}
                <Route
                  path="/patient-portal"
                  element={<PatientPortal />}
                />

                {/* Appointments - accessible to admin, doctor, staff only */}
                <Route
                  path="/appointment"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>
                      <Appointment />
                    </ProtectedRoute>
                  }
                />

                {/* Room management - accessible to admin, doctor only */}
                <Route
                  path="/room"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
                      <Room />
                    </ProtectedRoute>
                  }
                />

                {/* Hospital transfers - accessible to admin, doctor only */}
                <Route
                  path="/hospital-transfer"
                  element={
                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>
                      <HospitalTransfer />
                    </ProtectedRoute>
                  }
                />

                {/* Doctor management - accessible to admin only */}
                <Route
                  path="/doctor"
                  element={
                    <ProtectedRoute requireAdmin={true}>
                      <Doctor />
                    </ProtectedRoute>
                  }
                />

                {/* Admin dashboard - accessible to admin only */}
                <Route
                  path="/admin-dashboard"
                  element={
                    <ProtectedRoute requireAdmin={true}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  }
                />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;

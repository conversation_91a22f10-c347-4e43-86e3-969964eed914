{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\components\\\\BillingForm.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport FlutterwavePayment from './FlutterwavePayment';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BillingForm = ({\n  onBillCreated\n}) => {\n  _s();\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showFlutterwavePayment, setShowFlutterwavePayment] = useState(false);\n  const [createdBillId, setCreatedBillId] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    patientNationalId: '',\n    patientName: '',\n    patientEmail: '',\n    patientPhone: '',\n    patientAddress: '',\n    consultationFees: 0,\n    examFees: 0,\n    items: [{\n      description: '',\n      quantity: 1,\n      unitPrice: 0,\n      total: 0\n    }],\n    subtotal: 0,\n    insurancePercentage: 0,\n    insuranceAmount: 0,\n    totalAmountToBePaid: 0,\n    amountPaid: 0,\n    paymentMethod: 'Cash',\n    paymentStatus: 'Pending',\n    notes: ''\n  });\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patients on component mount\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  // Fetch all patients\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n    }\n  };\n\n  // Handle input changes\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle patient selection\n  const handlePatientSelect = e => {\n    const nationalId = e.target.value;\n    const patient = patients.find(p => p.nationalId === nationalId);\n    if (patient) {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: patient.nationalId,\n        patientName: `${patient.firstName} ${patient.lastName}`,\n        patientEmail: patient.email,\n        patientPhone: patient.phone,\n        patientAddress: patient.address\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: '',\n        patientName: '',\n        patientEmail: '',\n        patientPhone: '',\n        patientAddress: ''\n      }));\n    }\n  };\n\n  // Handle item changes\n  const handleItemChange = (index, field, value) => {\n    const newItems = [...formData.items];\n    newItems[index][field] = value;\n\n    // Calculate item total\n    if (field === 'quantity' || field === 'unitPrice') {\n      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;\n    }\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n\n    // Recalculate totals\n    calculateTotals(newItems);\n  };\n\n  // Add new item\n  const addItem = () => {\n    setFormData(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        description: '',\n        quantity: 1,\n        unitPrice: 0,\n        total: 0\n      }]\n    }));\n  };\n\n  // Remove item\n  const removeItem = index => {\n    const newItems = formData.items.filter((_, i) => i !== index);\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n    calculateTotals(newItems);\n  };\n\n  // Calculate totals\n  const calculateTotals = (items, consultationFees = formData.consultationFees, examFees = formData.examFees, insurancePercentage = formData.insurancePercentage) => {\n    const itemsTotal = items.reduce((sum, item) => sum + item.total, 0);\n    const subtotal = itemsTotal + consultationFees + examFees;\n    const insuranceAmount = subtotal * insurancePercentage / 100;\n    const totalAmountToBePaid = subtotal - insuranceAmount;\n    setFormData(prev => ({\n      ...prev,\n      subtotal: subtotal,\n      insuranceAmount: insuranceAmount,\n      totalAmountToBePaid: totalAmountToBePaid\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/billing`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        var _data$data;\n        setCreatedBillId(data.billId || ((_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.id));\n\n        // If payment method is Flutterwave, show payment modal\n        if (formData.paymentMethod === 'Flutterwave' && formData.totalAmountToBePaid > 0) {\n          setShowFlutterwavePayment(true);\n        } else {\n          alert('Bill created successfully!');\n          resetForm();\n          // Call the callback if provided\n          if (onBillCreated) {\n            onBillCreated();\n          }\n        }\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error creating bill:', error);\n      alert('Error creating bill. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle Flutterwave payment success\n  const handleFlutterwaveSuccess = async paymentData => {\n    try {\n      // Update the bill with payment information\n      const response = await fetch(`${API_BASE_URL}/billing/${createdBillId}/payment`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          amountPaid: formData.totalAmountToBePaid,\n          paymentStatus: 'Paid',\n          paymentMethod: 'Flutterwave',\n          transactionId: paymentData.transactionId,\n          flutterwaveRef: paymentData.flwRef,\n          paymentType: paymentData.paymentType\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Payment successful! Bill has been updated.');\n        setShowFlutterwavePayment(false);\n        resetForm();\n        if (onBillCreated) {\n          onBillCreated();\n        }\n      } else {\n        alert('Payment was successful but failed to update bill. Please contact support.');\n      }\n    } catch (error) {\n      console.error('Error updating bill with payment:', error);\n      alert('Payment was successful but failed to update bill. Please contact support.');\n    }\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      patientNationalId: '',\n      patientName: '',\n      patientEmail: '',\n      patientPhone: '',\n      patientAddress: '',\n      consultationFees: 0,\n      examFees: 0,\n      items: [{\n        description: '',\n        quantity: 1,\n        unitPrice: 0,\n        total: 0\n      }],\n      subtotal: 0,\n      insurancePercentage: 0,\n      insuranceAmount: 0,\n      totalAmountToBePaid: 0,\n      amountPaid: 0,\n      paymentMethod: 'Cash',\n      paymentStatus: 'Pending',\n      notes: ''\n    });\n    setCreatedBillId(null);\n    setShowFlutterwavePayment(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 rounded-xl p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"\\uD83D\\uDC64 Patient Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Select Patient\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.patientNationalId,\n            onChange: handlePatientSelect,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a patient or enter manually\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), patients.map(patient => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: patient.nationalId,\n              children: [patient.firstName, \" \", patient.lastName, \" - \", patient.nationalId]\n            }, patient.nationalId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Patient Name *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"patientName\",\n            value: formData.patientName,\n            onChange: handleInputChange,\n            required: true,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            name: \"patientEmail\",\n            value: formData.patientEmail,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Phone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            name: \"patientPhone\",\n            value: formData.patientPhone,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"patientAddress\",\n            value: formData.patientAddress,\n            onChange: handleInputChange,\n            rows: \"2\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-50 rounded-xl p-4 border border-green-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-green-900 mb-4\",\n        children: \"\\uD83E\\uDE7A Consultation & Examination Fees\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Consultation Fees (RWF)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"consultationFees\",\n            value: formData.consultationFees,\n            onChange: e => {\n              const fees = parseFloat(e.target.value) || 0;\n              setFormData(prev => ({\n                ...prev,\n                consultationFees: fees\n              }));\n              calculateTotals(formData.items, fees, formData.examFees, formData.insurancePercentage);\n            },\n            placeholder: \"Enter consultation fees\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Examination Fees (RWF)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"examFees\",\n            value: formData.examFees,\n            onChange: e => {\n              const fees = parseFloat(e.target.value) || 0;\n              setFormData(prev => ({\n                ...prev,\n                examFees: fees\n              }));\n              calculateTotals(formData.items, formData.consultationFees, fees, formData.insurancePercentage);\n            },\n            placeholder: \"Enter examination fees\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 rounded-xl p-4 border border-blue-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-blue-900\",\n          children: \"\\uD83C\\uDFE5 Medical Services & Charges\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: addItem,\n          className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 4v16m8-8H4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), \"Add Service\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: formData.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-5 gap-3 bg-white p-3 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Medical Service (e.g., Consultation, Lab Test, Surgery)\",\n              value: item.description,\n              onChange: e => handleItemChange(index, 'description', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              placeholder: \"Qty\",\n              value: item.quantity,\n              onChange: e => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              placeholder: \"Rate (RWF)\",\n              value: item.unitPrice,\n              onChange: e => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              placeholder: \"Amount (RWF)\",\n              value: item.total,\n              readOnly: true,\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-lg bg-blue-50 font-semibold\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), formData.items.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => removeItem(index),\n              className: \"text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-purple-50 rounded-xl p-4 border border-purple-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-purple-900 mb-4\",\n        children: \"\\uD83D\\uDCB3 Payment & Insurance Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Insurance Coverage (%)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"insurancePercentage\",\n            value: formData.insurancePercentage,\n            onChange: e => {\n              const percentage = parseFloat(e.target.value) || 0;\n              setFormData(prev => ({\n                ...prev,\n                insurancePercentage: percentage\n              }));\n              calculateTotals(formData.items, formData.consultationFees, formData.examFees, percentage);\n            },\n            placeholder: \"Enter insurance coverage percentage\",\n            min: \"0\",\n            max: \"100\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"Enter percentage (0-100%) that insurance will cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Payment Method\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"paymentMethod\",\n            value: formData.paymentMethod,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Cash\",\n              children: \"Cash Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Card\",\n              children: \"Credit/Debit Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Insurance\",\n              children: \"Health Insurance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Mobile Money\",\n              children: \"Mobile Money (MTN/Airtel)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Flutterwave\",\n              children: \"Flutterwave (Online Payment)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Amount Paid (RWF)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"amountPaid\",\n            value: formData.amountPaid,\n            onChange: e => {\n              const paid = parseFloat(e.target.value) || 0;\n              setFormData(prev => ({\n                ...prev,\n                amountPaid: paid,\n                paymentStatus: paid >= prev.totalAmountToBePaid ? 'Paid' : paid > 0 ? 'Partial' : 'Pending'\n              }));\n            },\n            placeholder: \"Enter amount paid\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Payment Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"paymentStatus\",\n            value: formData.paymentStatus,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Pending\",\n              children: \"Pending Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Paid\",\n              children: \"Fully Paid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Partial\",\n              children: \"Partially Paid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Cancelled\",\n              children: \"Cancelled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"Medical Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"notes\",\n            value: formData.notes,\n            onChange: handleInputChange,\n            rows: \"3\",\n            placeholder: \"Additional medical or billing notes...\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-50 rounded-xl p-4 border border-yellow-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-yellow-900 mb-4\",\n        children: \"\\uD83D\\uDCB0 Medical Bill Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Consultation Fees:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: [formData.consultationFees.toFixed(2), \" RWF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Examination Fees:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: [formData.examFees.toFixed(2), \" RWF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Other Services:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold\",\n            children: [formData.items.reduce((sum, item) => sum + item.total, 0).toFixed(2), \" RWF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-lg font-bold\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Subtotal:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-700\",\n              children: [formData.subtotal.toFixed(2), \" RWF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: [\"Insurance Coverage (\", formData.insurancePercentage, \"%):\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-green-600\",\n            children: [\"-\", formData.insuranceAmount.toFixed(2), \" RWF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xl font-bold\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Total Amount to be Paid:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-600\",\n              children: [formData.totalAmountToBePaid.toFixed(2), \" RWF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Amount Paid:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-green-600\",\n            children: [formData.amountPaid.toFixed(2), \" RWF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-lg font-bold\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Balance Due:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `${formData.totalAmountToBePaid - formData.amountPaid > 0 ? 'text-red-600' : 'text-green-600'}`,\n              children: [(formData.totalAmountToBePaid - formData.amountPaid).toFixed(2), \" RWF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), loading ? 'Generating Bill...' : 'Generate Medical Bill']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this), showFlutterwavePayment && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full\",\n        children: /*#__PURE__*/_jsxDEV(FlutterwavePayment, {\n          amount: formData.totalAmountToBePaid,\n          customerEmail: formData.patientEmail || '<EMAIL>',\n          customerPhone: formData.patientPhone || '+250700000000',\n          customerName: formData.patientName,\n          billId: createdBillId,\n          onSuccess: handleFlutterwaveSuccess,\n          onClose: () => {\n            setShowFlutterwavePayment(false);\n            alert('Payment cancelled. Bill has been created but payment is still pending.');\n            resetForm();\n            if (onBillCreated) {\n              onBillCreated();\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n};\n_s(BillingForm, \"aarVEggXe+tid5WbL6usyCPjmWQ=\");\n_c = BillingForm;\nexport default BillingForm;\nvar _c;\n$RefreshReg$(_c, \"BillingForm\");", "map": {"version": 3, "names": ["useState", "useEffect", "FlutterwavePayment", "jsxDEV", "_jsxDEV", "BillingForm", "onBillCreated", "_s", "patients", "setPatients", "loading", "setLoading", "showFlutterwavePayment", "setShowFlutterwavePayment", "createdBillId", "setCreatedBillId", "formData", "setFormData", "patientNationalId", "patientName", "patientEmail", "patientPhone", "patientAddress", "consultationFees", "examFees", "items", "description", "quantity", "unitPrice", "total", "subtotal", "insurancePercentage", "insuranceAmount", "totalAmountToBePaid", "amountPaid", "paymentMethod", "paymentStatus", "notes", "API_BASE_URL", "fetchPatients", "response", "fetch", "data", "json", "success", "error", "console", "handleInputChange", "e", "name", "value", "target", "prev", "handlePatientSelect", "nationalId", "patient", "find", "p", "firstName", "lastName", "email", "phone", "address", "handleItemChange", "index", "field", "newItems", "calculateTotals", "addItem", "removeItem", "filter", "_", "i", "itemsTotal", "reduce", "sum", "item", "handleSubmit", "preventDefault", "method", "headers", "body", "JSON", "stringify", "_data$data", "billId", "id", "alert", "resetForm", "message", "handleFlutterwaveSuccess", "paymentData", "transactionId", "flutterwaveRef", "flwRef", "paymentType", "onSubmit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "map", "type", "required", "rows", "fees", "parseFloat", "placeholder", "onClick", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "readOnly", "length", "percentage", "min", "max", "paid", "toFixed", "disabled", "amount", "customerEmail", "customerPhone", "customerName", "onSuccess", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/components/BillingForm.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport FlutterwavePayment from './FlutterwavePayment';\n\nconst BillingForm = ({ onBillCreated }) => {\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showFlutterwavePayment, setShowFlutterwavePayment] = useState(false);\n  const [createdBillId, setCreatedBillId] = useState(null);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    patientNationalId: '',\n    patientName: '',\n    patientEmail: '',\n    patientPhone: '',\n    patientAddress: '',\n    consultationFees: 0,\n    examFees: 0,\n    items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\n    subtotal: 0,\n    insurancePercentage: 0,\n    insuranceAmount: 0,\n    totalAmountToBePaid: 0,\n    amountPaid: 0,\n    paymentMethod: 'Cash',\n    paymentStatus: 'Pending',\n    notes: ''\n  });\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patients on component mount\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  // Fetch all patients\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n    }\n  };\n\n  // Handle input changes\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle patient selection\n  const handlePatientSelect = (e) => {\n    const nationalId = e.target.value;\n    const patient = patients.find(p => p.nationalId === nationalId);\n    \n    if (patient) {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: patient.nationalId,\n        patientName: `${patient.firstName} ${patient.lastName}`,\n        patientEmail: patient.email,\n        patientPhone: patient.phone,\n        patientAddress: patient.address\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: '',\n        patientName: '',\n        patientEmail: '',\n        patientPhone: '',\n        patientAddress: ''\n      }));\n    }\n  };\n\n  // Handle item changes\n  const handleItemChange = (index, field, value) => {\n    const newItems = [...formData.items];\n    newItems[index][field] = value;\n    \n    // Calculate item total\n    if (field === 'quantity' || field === 'unitPrice') {\n      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;\n    }\n    \n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n    \n    // Recalculate totals\n    calculateTotals(newItems);\n  };\n\n  // Add new item\n  const addItem = () => {\n    setFormData(prev => ({\n      ...prev,\n      items: [...prev.items, { description: '', quantity: 1, unitPrice: 0, total: 0 }]\n    }));\n  };\n\n  // Remove item\n  const removeItem = (index) => {\n    const newItems = formData.items.filter((_, i) => i !== index);\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n    calculateTotals(newItems);\n  };\n\n  // Calculate totals\n  const calculateTotals = (items, consultationFees = formData.consultationFees, examFees = formData.examFees, insurancePercentage = formData.insurancePercentage) => {\n    const itemsTotal = items.reduce((sum, item) => sum + item.total, 0);\n    const subtotal = itemsTotal + consultationFees + examFees;\n    const insuranceAmount = (subtotal * insurancePercentage) / 100;\n    const totalAmountToBePaid = subtotal - insuranceAmount;\n\n    setFormData(prev => ({\n      ...prev,\n      subtotal: subtotal,\n      insuranceAmount: insuranceAmount,\n      totalAmountToBePaid: totalAmountToBePaid\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await fetch(`${API_BASE_URL}/billing`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setCreatedBillId(data.billId || data.data?.id);\n\n        // If payment method is Flutterwave, show payment modal\n        if (formData.paymentMethod === 'Flutterwave' && formData.totalAmountToBePaid > 0) {\n          setShowFlutterwavePayment(true);\n        } else {\n          alert('Bill created successfully!');\n          resetForm();\n          // Call the callback if provided\n          if (onBillCreated) {\n            onBillCreated();\n          }\n        }\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error creating bill:', error);\n      alert('Error creating bill. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle Flutterwave payment success\n  const handleFlutterwaveSuccess = async (paymentData) => {\n    try {\n      // Update the bill with payment information\n      const response = await fetch(`${API_BASE_URL}/billing/${createdBillId}/payment`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          amountPaid: formData.totalAmountToBePaid,\n          paymentStatus: 'Paid',\n          paymentMethod: 'Flutterwave',\n          transactionId: paymentData.transactionId,\n          flutterwaveRef: paymentData.flwRef,\n          paymentType: paymentData.paymentType\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        alert('Payment successful! Bill has been updated.');\n        setShowFlutterwavePayment(false);\n        resetForm();\n        if (onBillCreated) {\n          onBillCreated();\n        }\n      } else {\n        alert('Payment was successful but failed to update bill. Please contact support.');\n      }\n    } catch (error) {\n      console.error('Error updating bill with payment:', error);\n      alert('Payment was successful but failed to update bill. Please contact support.');\n    }\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      patientNationalId: '',\n      patientName: '',\n      patientEmail: '',\n      patientPhone: '',\n      patientAddress: '',\n      consultationFees: 0,\n      examFees: 0,\n      items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\n      subtotal: 0,\n      insurancePercentage: 0,\n      insuranceAmount: 0,\n      totalAmountToBePaid: 0,\n      amountPaid: 0,\n      paymentMethod: 'Cash',\n      paymentStatus: 'Pending',\n      notes: ''\n    });\n    setCreatedBillId(null);\n    setShowFlutterwavePayment(false);\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* Patient Information */}\n      <div className=\"bg-gray-50 rounded-xl p-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">👤 Patient Information</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Select Patient</label>\n            <select\n              value={formData.patientNationalId}\n              onChange={handlePatientSelect}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Select a patient or enter manually</option>\n              {patients.map((patient) => (\n                <option key={patient.nationalId} value={patient.nationalId}>\n                  {patient.firstName} {patient.lastName} - {patient.nationalId}\n                </option>\n              ))}\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Patient Name *</label>\n            <input\n              type=\"text\"\n              name=\"patientName\"\n              value={formData.patientName}\n              onChange={handleInputChange}\n              required\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n            <input\n              type=\"email\"\n              name=\"patientEmail\"\n              value={formData.patientEmail}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone</label>\n            <input\n              type=\"tel\"\n              name=\"patientPhone\"\n              value={formData.patientPhone}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Address</label>\n            <textarea\n              name=\"patientAddress\"\n              value={formData.patientAddress}\n              onChange={handleInputChange}\n              rows=\"2\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Consultation & Exam Fees */}\n      <div className=\"bg-green-50 rounded-xl p-4 border border-green-100\">\n        <h4 className=\"text-lg font-semibold text-green-900 mb-4\">🩺 Consultation & Examination Fees</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Consultation Fees (RWF)</label>\n            <input\n              type=\"number\"\n              name=\"consultationFees\"\n              value={formData.consultationFees}\n              onChange={(e) => {\n                const fees = parseFloat(e.target.value) || 0;\n                setFormData(prev => ({ ...prev, consultationFees: fees }));\n                calculateTotals(formData.items, fees, formData.examFees, formData.insurancePercentage);\n              }}\n              placeholder=\"Enter consultation fees\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Examination Fees (RWF)</label>\n            <input\n              type=\"number\"\n              name=\"examFees\"\n              value={formData.examFees}\n              onChange={(e) => {\n                const fees = parseFloat(e.target.value) || 0;\n                setFormData(prev => ({ ...prev, examFees: fees }));\n                calculateTotals(formData.items, formData.consultationFees, fees, formData.insurancePercentage);\n              }}\n              placeholder=\"Enter examination fees\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Medical Services & Charges */}\n      <div className=\"bg-blue-50 rounded-xl p-4 border border-blue-100\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h4 className=\"text-lg font-semibold text-blue-900\">🏥 Medical Services & Charges</h4>\n          <button\n            type=\"button\"\n            onClick={addItem}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Add Service\n          </button>\n        </div>\n\n        <div className=\"space-y-3\">\n          {formData.items.map((item, index) => (\n            <div key={index} className=\"grid grid-cols-1 md:grid-cols-5 gap-3 bg-white p-3 rounded-lg\">\n              <div className=\"md:col-span-2\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Medical Service (e.g., Consultation, Lab Test, Surgery)\"\n                  value={item.description}\n                  onChange={(e) => handleItemChange(index, 'description', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              <div>\n                <input\n                  type=\"number\"\n                  placeholder=\"Qty\"\n                  value={item.quantity}\n                  onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              <div>\n                <input\n                  type=\"number\"\n                  placeholder=\"Rate (RWF)\"\n                  value={item.unitPrice}\n                  onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <input\n                  type=\"number\"\n                  placeholder=\"Amount (RWF)\"\n                  value={item.total}\n                  readOnly\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg bg-blue-50 font-semibold\"\n                />\n                {formData.items.length > 1 && (\n                  <button\n                    type=\"button\"\n                    onClick={() => removeItem(index)}\n                    className=\"text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                    </svg>\n                  </button>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Payment & Insurance Details */}\n      <div className=\"bg-purple-50 rounded-xl p-4 border border-purple-100\">\n        <h4 className=\"text-lg font-semibold text-purple-900 mb-4\">💳 Payment & Insurance Information</h4>\n        <div className=\"space-y-3\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Insurance Coverage (%)</label>\n            <input\n              type=\"number\"\n              name=\"insurancePercentage\"\n              value={formData.insurancePercentage}\n              onChange={(e) => {\n                const percentage = parseFloat(e.target.value) || 0;\n                setFormData(prev => ({ ...prev, insurancePercentage: percentage }));\n                calculateTotals(formData.items, formData.consultationFees, formData.examFees, percentage);\n              }}\n              placeholder=\"Enter insurance coverage percentage\"\n              min=\"0\"\n              max=\"100\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n            />\n            <p className=\"text-xs text-gray-500 mt-1\">Enter percentage (0-100%) that insurance will cover</p>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Payment Method</label>\n            <select\n              name=\"paymentMethod\"\n              value={formData.paymentMethod}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n            >\n              <option value=\"Cash\">Cash Payment</option>\n              <option value=\"Card\">Credit/Debit Card</option>\n              <option value=\"Insurance\">Health Insurance</option>\n              <option value=\"Mobile Money\">Mobile Money (MTN/Airtel)</option>\n              <option value=\"Flutterwave\">Flutterwave (Online Payment)</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Amount Paid (RWF)</label>\n            <input\n              type=\"number\"\n              name=\"amountPaid\"\n              value={formData.amountPaid}\n              onChange={(e) => {\n                const paid = parseFloat(e.target.value) || 0;\n                setFormData(prev => ({\n                  ...prev,\n                  amountPaid: paid,\n                  paymentStatus: paid >= prev.totalAmountToBePaid ? 'Paid' : paid > 0 ? 'Partial' : 'Pending'\n                }));\n              }}\n              placeholder=\"Enter amount paid\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Payment Status</label>\n            <select\n              name=\"paymentStatus\"\n              value={formData.paymentStatus}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n            >\n              <option value=\"Pending\">Pending Payment</option>\n              <option value=\"Paid\">Fully Paid</option>\n              <option value=\"Partial\">Partially Paid</option>\n              <option value=\"Cancelled\">Cancelled</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Medical Notes</label>\n            <textarea\n              name=\"notes\"\n              value={formData.notes}\n              onChange={handleInputChange}\n              rows=\"3\"\n              placeholder=\"Additional medical or billing notes...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Bill Summary */}\n      <div className=\"bg-yellow-50 rounded-xl p-4 border border-yellow-100\">\n        <h4 className=\"text-lg font-semibold text-yellow-900 mb-4\">💰 Medical Bill Summary</h4>\n        <div className=\"space-y-3\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Consultation Fees:</span>\n            <span className=\"font-semibold\">{formData.consultationFees.toFixed(2)} RWF</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Examination Fees:</span>\n            <span className=\"font-semibold\">{formData.examFees.toFixed(2)} RWF</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Other Services:</span>\n            <span className=\"font-semibold\">{formData.items.reduce((sum, item) => sum + item.total, 0).toFixed(2)} RWF</span>\n          </div>\n          <div className=\"border-t pt-2\">\n            <div className=\"flex justify-between text-lg font-bold\">\n              <span>Subtotal:</span>\n              <span className=\"text-blue-700\">{formData.subtotal.toFixed(2)} RWF</span>\n            </div>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Insurance Coverage ({formData.insurancePercentage}%):</span>\n            <span className=\"font-semibold text-green-600\">-{formData.insuranceAmount.toFixed(2)} RWF</span>\n          </div>\n          <div className=\"border-t pt-2\">\n            <div className=\"flex justify-between text-xl font-bold\">\n              <span>Total Amount to be Paid:</span>\n              <span className=\"text-red-600\">{formData.totalAmountToBePaid.toFixed(2)} RWF</span>\n            </div>\n          </div>\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">Amount Paid:</span>\n            <span className=\"font-semibold text-green-600\">{formData.amountPaid.toFixed(2)} RWF</span>\n          </div>\n          <div className=\"border-t pt-2\">\n            <div className=\"flex justify-between text-lg font-bold\">\n              <span>Balance Due:</span>\n              <span className={`${(formData.totalAmountToBePaid - formData.amountPaid) > 0 ? 'text-red-600' : 'text-green-600'}`}>\n                {(formData.totalAmountToBePaid - formData.amountPaid).toFixed(2)} RWF\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Submit Button */}\n      <div className=\"flex justify-end\">\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 flex items-center gap-2\"\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n          {loading ? 'Generating Bill...' : 'Generate Medical Bill'}\n        </button>\n      </div>\n\n      {/* Flutterwave Payment Modal */}\n      {showFlutterwavePayment && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"max-w-md w-full\">\n            <FlutterwavePayment\n              amount={formData.totalAmountToBePaid}\n              customerEmail={formData.patientEmail || '<EMAIL>'}\n              customerPhone={formData.patientPhone || '+250700000000'}\n              customerName={formData.patientName}\n              billId={createdBillId}\n              onSuccess={handleFlutterwaveSuccess}\n              onClose={() => {\n                setShowFlutterwavePayment(false);\n                alert('Payment cancelled. Bill has been created but payment is still pending.');\n                resetForm();\n                if (onBillCreated) {\n                  onBillCreated();\n                }\n              }}\n            />\n          </div>\n        </div>\n      )}\n    </form>\n  );\n};\n\nexport default BillingForm;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,iBAAiB,EAAE,EAAE;IACrBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IACjEC,QAAQ,EAAE,CAAC;IACXC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,CAAC;IAClBC,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,SAAS;IACxBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,2BAA2B;;EAEhD;EACArC,SAAS,CAAC,MAAM;IACdsC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,WAAW,CAAC;MACxD,MAAMI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBnC,WAAW,CAACiC,IAAI,CAACA,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClC,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIL,CAAC,IAAK;IACjC,MAAMM,UAAU,GAAGN,CAAC,CAACG,MAAM,CAACD,KAAK;IACjC,MAAMK,OAAO,GAAG/C,QAAQ,CAACgD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,UAAU,KAAKA,UAAU,CAAC;IAE/D,IAAIC,OAAO,EAAE;MACXtC,WAAW,CAACmC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPlC,iBAAiB,EAAEqC,OAAO,CAACD,UAAU;QACrCnC,WAAW,EAAE,GAAGoC,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,QAAQ,EAAE;QACvDvC,YAAY,EAAEmC,OAAO,CAACK,KAAK;QAC3BvC,YAAY,EAAEkC,OAAO,CAACM,KAAK;QAC3BvC,cAAc,EAAEiC,OAAO,CAACO;MAC1B,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL7C,WAAW,CAACmC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPlC,iBAAiB,EAAE,EAAE;QACrBC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEf,KAAK,KAAK;IAChD,MAAMgB,QAAQ,GAAG,CAAC,GAAGlD,QAAQ,CAACS,KAAK,CAAC;IACpCyC,QAAQ,CAACF,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGf,KAAK;;IAE9B;IACA,IAAIe,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,WAAW,EAAE;MACjDC,QAAQ,CAACF,KAAK,CAAC,CAACnC,KAAK,GAAGqC,QAAQ,CAACF,KAAK,CAAC,CAACrC,QAAQ,GAAGuC,QAAQ,CAACF,KAAK,CAAC,CAACpC,SAAS;IAC9E;IAEAX,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAEyC;IACT,CAAC,CAAC,CAAC;;IAEH;IACAC,eAAe,CAACD,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAME,OAAO,GAAGA,CAAA,KAAM;IACpBnD,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAE,CAAC,GAAG2B,IAAI,CAAC3B,KAAK,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC;IACjF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAIL,KAAK,IAAK;IAC5B,MAAME,QAAQ,GAAGlD,QAAQ,CAACS,KAAK,CAAC6C,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKR,KAAK,CAAC;IAC7D/C,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAEyC;IACT,CAAC,CAAC,CAAC;IACHC,eAAe,CAACD,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAC1C,KAAK,EAAEF,gBAAgB,GAAGP,QAAQ,CAACO,gBAAgB,EAAEC,QAAQ,GAAGR,QAAQ,CAACQ,QAAQ,EAAEO,mBAAmB,GAAGf,QAAQ,CAACe,mBAAmB,KAAK;IACjK,MAAM0C,UAAU,GAAGhD,KAAK,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAAC/C,KAAK,EAAE,CAAC,CAAC;IACnE,MAAMC,QAAQ,GAAG2C,UAAU,GAAGlD,gBAAgB,GAAGC,QAAQ;IACzD,MAAMQ,eAAe,GAAIF,QAAQ,GAAGC,mBAAmB,GAAI,GAAG;IAC9D,MAAME,mBAAmB,GAAGH,QAAQ,GAAGE,eAAe;IAEtDf,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtB,QAAQ,EAAEA,QAAQ;MAClBE,eAAe,EAAEA,eAAe;MAChCC,mBAAmB,EAAEA;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM4C,YAAY,GAAG,MAAO7B,CAAC,IAAK;IAChCA,CAAC,CAAC8B,cAAc,CAAC,CAAC;IAClBnE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,UAAU,EAAE;QACtDyC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACnE,QAAQ;MAC/B,CAAC,CAAC;MAEF,MAAM0B,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAAA,IAAAwC,UAAA;QAChBrE,gBAAgB,CAAC2B,IAAI,CAAC2C,MAAM,MAAAD,UAAA,GAAI1C,IAAI,CAACA,IAAI,cAAA0C,UAAA,uBAATA,UAAA,CAAWE,EAAE,EAAC;;QAE9C;QACA,IAAItE,QAAQ,CAACmB,aAAa,KAAK,aAAa,IAAInB,QAAQ,CAACiB,mBAAmB,GAAG,CAAC,EAAE;UAChFpB,yBAAyB,CAAC,IAAI,CAAC;QACjC,CAAC,MAAM;UACL0E,KAAK,CAAC,4BAA4B,CAAC;UACnCC,SAAS,CAAC,CAAC;UACX;UACA,IAAIlF,aAAa,EAAE;YACjBA,aAAa,CAAC,CAAC;UACjB;QACF;MACF,CAAC,MAAM;QACLiF,KAAK,CAAC,UAAU7C,IAAI,CAAC+C,OAAO,EAAE,CAAC;MACjC;IACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C0C,KAAK,CAAC,wCAAwC,CAAC;IACjD,CAAC,SAAS;MACR5E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,wBAAwB,GAAG,MAAOC,WAAW,IAAK;IACtD,IAAI;MACF;MACA,MAAMnD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,YAAY,YAAYxB,aAAa,UAAU,EAAE;QAC/EiE,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBjD,UAAU,EAAElB,QAAQ,CAACiB,mBAAmB;UACxCG,aAAa,EAAE,MAAM;UACrBD,aAAa,EAAE,aAAa;UAC5ByD,aAAa,EAAED,WAAW,CAACC,aAAa;UACxCC,cAAc,EAAEF,WAAW,CAACG,MAAM;UAClCC,WAAW,EAAEJ,WAAW,CAACI;QAC3B,CAAC;MACH,CAAC,CAAC;MAEF,MAAMrD,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB2C,KAAK,CAAC,4CAA4C,CAAC;QACnD1E,yBAAyB,CAAC,KAAK,CAAC;QAChC2E,SAAS,CAAC,CAAC;QACX,IAAIlF,aAAa,EAAE;UACjBA,aAAa,CAAC,CAAC;QACjB;MACF,CAAC,MAAM;QACLiF,KAAK,CAAC,2EAA2E,CAAC;MACpF;IACF,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD0C,KAAK,CAAC,2EAA2E,CAAC;IACpF;EACF,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtBvE,WAAW,CAAC;MACVC,iBAAiB,EAAE,EAAE;MACrBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,CAAC;MACnBC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;MACjEC,QAAQ,EAAE,CAAC;MACXC,mBAAmB,EAAE,CAAC;MACtBC,eAAe,EAAE,CAAC;MAClBC,mBAAmB,EAAE,CAAC;MACtBC,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,MAAM;MACrBC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFtB,gBAAgB,CAAC,IAAI,CAAC;IACtBF,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,oBACET,OAAA;IAAM4F,QAAQ,EAAEnB,YAAa;IAACoB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAEjD9F,OAAA;MAAK6F,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC9F,OAAA;QAAI6F,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpFlG,OAAA;QAAK6F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9F,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtFlG,OAAA;YACE8C,KAAK,EAAElC,QAAQ,CAACE,iBAAkB;YAClCqF,QAAQ,EAAElD,mBAAoB;YAC9B4C,SAAS,EAAC,8GAA8G;YAAAC,QAAA,gBAExH9F,OAAA;cAAQ8C,KAAK,EAAC,EAAE;cAAAgD,QAAA,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC3D9F,QAAQ,CAACgG,GAAG,CAAEjD,OAAO,iBACpBnD,OAAA;cAAiC8C,KAAK,EAAEK,OAAO,CAACD,UAAW;cAAA4C,QAAA,GACxD3C,OAAO,CAACG,SAAS,EAAC,GAAC,EAACH,OAAO,CAACI,QAAQ,EAAC,KAAG,EAACJ,OAAO,CAACD,UAAU;YAAA,GADjDC,OAAO,CAACD,UAAU;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEvB,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtFlG,OAAA;YACEqG,IAAI,EAAC,MAAM;YACXxD,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAElC,QAAQ,CAACG,WAAY;YAC5BoF,QAAQ,EAAExD,iBAAkB;YAC5B2D,QAAQ;YACRT,SAAS,EAAC;UAA8G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7ElG,OAAA;YACEqG,IAAI,EAAC,OAAO;YACZxD,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAElC,QAAQ,CAACI,YAAa;YAC7BmF,QAAQ,EAAExD,iBAAkB;YAC5BkD,SAAS,EAAC;UAA8G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7ElG,OAAA;YACEqG,IAAI,EAAC,KAAK;YACVxD,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAElC,QAAQ,CAACK,YAAa;YAC7BkF,QAAQ,EAAExD,iBAAkB;YAC5BkD,SAAS,EAAC;UAA8G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/ElG,OAAA;YACE6C,IAAI,EAAC,gBAAgB;YACrBC,KAAK,EAAElC,QAAQ,CAACM,cAAe;YAC/BiF,QAAQ,EAAExD,iBAAkB;YAC5B4D,IAAI,EAAC,GAAG;YACRV,SAAS,EAAC;UAA8G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjE9F,OAAA;QAAI6F,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjGlG,OAAA;QAAK6F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9F,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/FlG,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbxD,IAAI,EAAC,kBAAkB;YACvBC,KAAK,EAAElC,QAAQ,CAACO,gBAAiB;YACjCgF,QAAQ,EAAGvD,CAAC,IAAK;cACf,MAAM4D,IAAI,GAAGC,UAAU,CAAC7D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC;cAC5CjC,WAAW,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7B,gBAAgB,EAAEqF;cAAK,CAAC,CAAC,CAAC;cAC1DzC,eAAe,CAACnD,QAAQ,CAACS,KAAK,EAAEmF,IAAI,EAAE5F,QAAQ,CAACQ,QAAQ,EAAER,QAAQ,CAACe,mBAAmB,CAAC;YACxF,CAAE;YACF+E,WAAW,EAAC,yBAAyB;YACrCb,SAAS,EAAC;UAA+G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9FlG,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbxD,IAAI,EAAC,UAAU;YACfC,KAAK,EAAElC,QAAQ,CAACQ,QAAS;YACzB+E,QAAQ,EAAGvD,CAAC,IAAK;cACf,MAAM4D,IAAI,GAAGC,UAAU,CAAC7D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC;cAC5CjC,WAAW,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5B,QAAQ,EAAEoF;cAAK,CAAC,CAAC,CAAC;cAClDzC,eAAe,CAACnD,QAAQ,CAACS,KAAK,EAAET,QAAQ,CAACO,gBAAgB,EAAEqF,IAAI,EAAE5F,QAAQ,CAACe,mBAAmB,CAAC;YAChG,CAAE;YACF+E,WAAW,EAAC,wBAAwB;YACpCb,SAAS,EAAC;UAA+G;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/D9F,OAAA;QAAK6F,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9F,OAAA;UAAI6F,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFlG,OAAA;UACEqG,IAAI,EAAC,QAAQ;UACbM,OAAO,EAAE3C,OAAQ;UACjB6B,SAAS,EAAC,2GAA2G;UAAAC,QAAA,gBAErH9F,OAAA;YAAK6F,SAAS,EAAC,SAAS;YAACe,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAjB,QAAA,eAC5F9F,OAAA;cAAMgH,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAgB;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlG,OAAA;QAAK6F,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBlF,QAAQ,CAACS,KAAK,CAAC+E,GAAG,CAAC,CAAC5B,IAAI,EAAEZ,KAAK,kBAC9B5D,OAAA;UAAiB6F,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBACxF9F,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B9F,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXK,WAAW,EAAC,yDAAyD;cACrE5D,KAAK,EAAE0B,IAAI,CAAClD,WAAY;cACxB6E,QAAQ,EAAGvD,CAAC,IAAKe,gBAAgB,CAACC,KAAK,EAAE,aAAa,EAAEhB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cACxE+C,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlG,OAAA;YAAA8F,QAAA,eACE9F,OAAA;cACEqG,IAAI,EAAC,QAAQ;cACbK,WAAW,EAAC,KAAK;cACjB5D,KAAK,EAAE0B,IAAI,CAACjD,QAAS;cACrB4E,QAAQ,EAAGvD,CAAC,IAAKe,gBAAgB,CAACC,KAAK,EAAE,UAAU,EAAE6C,UAAU,CAAC7D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAE;cACtF+C,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlG,OAAA;YAAA8F,QAAA,eACE9F,OAAA;cACEqG,IAAI,EAAC,QAAQ;cACbK,WAAW,EAAC,YAAY;cACxB5D,KAAK,EAAE0B,IAAI,CAAChD,SAAU;cACtB2E,QAAQ,EAAGvD,CAAC,IAAKe,gBAAgB,CAACC,KAAK,EAAE,WAAW,EAAE6C,UAAU,CAAC7D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAE;cACvF+C,SAAS,EAAC;YAA8G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlG,OAAA;YAAK6F,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC9F,OAAA;cACEqG,IAAI,EAAC,QAAQ;cACbK,WAAW,EAAC,cAAc;cAC1B5D,KAAK,EAAE0B,IAAI,CAAC/C,KAAM;cAClB0F,QAAQ;cACRtB,SAAS,EAAC;YAA6E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,EACDtF,QAAQ,CAACS,KAAK,CAAC+F,MAAM,GAAG,CAAC,iBACxBpH,OAAA;cACEqG,IAAI,EAAC,QAAQ;cACbM,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAACL,KAAK,CAAE;cACjCiC,SAAS,EAAC,gEAAgE;cAAAC,QAAA,eAE1E9F,OAAA;gBAAK6F,SAAS,EAAC,SAAS;gBAACe,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAjB,QAAA,eAC5F9F,OAAA;kBAAMgH,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA8H;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA/CEtC,KAAK;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgDV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE9F,OAAA;QAAI6F,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClGlG,OAAA;QAAK6F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9F,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9FlG,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbxD,IAAI,EAAC,qBAAqB;YAC1BC,KAAK,EAAElC,QAAQ,CAACe,mBAAoB;YACpCwE,QAAQ,EAAGvD,CAAC,IAAK;cACf,MAAMyE,UAAU,GAAGZ,UAAU,CAAC7D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC;cAClDjC,WAAW,CAACmC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAErB,mBAAmB,EAAE0F;cAAW,CAAC,CAAC,CAAC;cACnEtD,eAAe,CAACnD,QAAQ,CAACS,KAAK,EAAET,QAAQ,CAACO,gBAAgB,EAAEP,QAAQ,CAACQ,QAAQ,EAAEiG,UAAU,CAAC;YAC3F,CAAE;YACFX,WAAW,EAAC,qCAAqC;YACjDY,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,KAAK;YACT1B,SAAS,EAAC;UAAgH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC,eACFlG,OAAA;YAAG6F,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtFlG,OAAA;YACE6C,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAElC,QAAQ,CAACmB,aAAc;YAC9BoE,QAAQ,EAAExD,iBAAkB;YAC5BkD,SAAS,EAAC,gHAAgH;YAAAC,QAAA,gBAE1H9F,OAAA;cAAQ8C,KAAK,EAAC,MAAM;cAAAgD,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1ClG,OAAA;cAAQ8C,KAAK,EAAC,MAAM;cAAAgD,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/ClG,OAAA;cAAQ8C,KAAK,EAAC,WAAW;cAAAgD,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnDlG,OAAA;cAAQ8C,KAAK,EAAC,cAAc;cAAAgD,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/DlG,OAAA;cAAQ8C,KAAK,EAAC,aAAa;cAAAgD,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzFlG,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbxD,IAAI,EAAC,YAAY;YACjBC,KAAK,EAAElC,QAAQ,CAACkB,UAAW;YAC3BqE,QAAQ,EAAGvD,CAAC,IAAK;cACf,MAAM4E,IAAI,GAAGf,UAAU,CAAC7D,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC;cAC5CjC,WAAW,CAACmC,IAAI,KAAK;gBACnB,GAAGA,IAAI;gBACPlB,UAAU,EAAE0F,IAAI;gBAChBxF,aAAa,EAAEwF,IAAI,IAAIxE,IAAI,CAACnB,mBAAmB,GAAG,MAAM,GAAG2F,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG;cACpF,CAAC,CAAC,CAAC;YACL,CAAE;YACFd,WAAW,EAAC,mBAAmB;YAC/Bb,SAAS,EAAC;UAAgH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtFlG,OAAA;YACE6C,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAElC,QAAQ,CAACoB,aAAc;YAC9BmE,QAAQ,EAAExD,iBAAkB;YAC5BkD,SAAS,EAAC,gHAAgH;YAAAC,QAAA,gBAE1H9F,OAAA;cAAQ8C,KAAK,EAAC,SAAS;cAAAgD,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChDlG,OAAA;cAAQ8C,KAAK,EAAC,MAAM;cAAAgD,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxClG,OAAA;cAAQ8C,KAAK,EAAC,SAAS;cAAAgD,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/ClG,OAAA;cAAQ8C,KAAK,EAAC,WAAW;cAAAgD,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlG,OAAA;UAAA8F,QAAA,gBACE9F,OAAA;YAAO6F,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrFlG,OAAA;YACE6C,IAAI,EAAC,OAAO;YACZC,KAAK,EAAElC,QAAQ,CAACqB,KAAM;YACtBkE,QAAQ,EAAExD,iBAAkB;YAC5B4D,IAAI,EAAC,GAAG;YACRG,WAAW,EAAC,wCAAwC;YACpDb,SAAS,EAAC;UAAgH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE9F,OAAA;QAAI6F,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvFlG,OAAA;QAAK6F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9F,OAAA;UAAK6F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC9F,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzDlG,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAElF,QAAQ,CAACO,gBAAgB,CAACsG,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC9F,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxDlG,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAElF,QAAQ,CAACQ,QAAQ,CAACqG,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC9F,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDlG,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAElF,QAAQ,CAACS,KAAK,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAAC/C,KAAK,EAAE,CAAC,CAAC,CAACgG,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9F,OAAA;YAAK6F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9F,OAAA;cAAA8F,QAAA,EAAM;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBlG,OAAA;cAAM6F,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAElF,QAAQ,CAACc,QAAQ,CAAC+F,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC9F,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,sBAAoB,EAAClF,QAAQ,CAACe,mBAAmB,EAAC,KAAG;UAAA;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5FlG,OAAA;YAAM6F,SAAS,EAAC,8BAA8B;YAAAC,QAAA,GAAC,GAAC,EAAClF,QAAQ,CAACgB,eAAe,CAAC6F,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9F,OAAA;YAAK6F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9F,OAAA;cAAA8F,QAAA,EAAM;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrClG,OAAA;cAAM6F,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAElF,QAAQ,CAACiB,mBAAmB,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C9F,OAAA;YAAM6F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDlG,OAAA;YAAM6F,SAAS,EAAC,8BAA8B;YAAAC,QAAA,GAAElF,QAAQ,CAACkB,UAAU,CAAC2F,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACNlG,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9F,OAAA;YAAK6F,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD9F,OAAA;cAAA8F,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzBlG,OAAA;cAAM6F,SAAS,EAAE,GAAIjF,QAAQ,CAACiB,mBAAmB,GAAGjB,QAAQ,CAACkB,UAAU,GAAI,CAAC,GAAG,cAAc,GAAG,gBAAgB,EAAG;cAAAgE,QAAA,GAChH,CAAClF,QAAQ,CAACiB,mBAAmB,GAAGjB,QAAQ,CAACkB,UAAU,EAAE2F,OAAO,CAAC,CAAC,CAAC,EAAC,MACnE;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA;MAAK6F,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9F,OAAA;QACEqG,IAAI,EAAC,QAAQ;QACbqB,QAAQ,EAAEpH,OAAQ;QAClBuF,SAAS,EAAC,8PAA8P;QAAAC,QAAA,gBAExQ9F,OAAA;UAAK6F,SAAS,EAAC,SAAS;UAACe,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,OAAO,EAAC,WAAW;UAAAjB,QAAA,eAC5F9F,OAAA;YAAMgH,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAAsH;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3K,CAAC,EACL5F,OAAO,GAAG,oBAAoB,GAAG,uBAAuB;MAAA;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL1F,sBAAsB,iBACrBR,OAAA;MAAK6F,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9F,OAAA;QAAK6F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B9F,OAAA,CAACF,kBAAkB;UACjB6H,MAAM,EAAE/G,QAAQ,CAACiB,mBAAoB;UACrC+F,aAAa,EAAEhH,QAAQ,CAACI,YAAY,IAAI,wBAAyB;UACjE6G,aAAa,EAAEjH,QAAQ,CAACK,YAAY,IAAI,eAAgB;UACxD6G,YAAY,EAAElH,QAAQ,CAACG,WAAY;UACnCkE,MAAM,EAAEvE,aAAc;UACtBqH,SAAS,EAAEzC,wBAAyB;UACpC0C,OAAO,EAAEA,CAAA,KAAM;YACbvH,yBAAyB,CAAC,KAAK,CAAC;YAChC0E,KAAK,CAAC,wEAAwE,CAAC;YAC/EC,SAAS,CAAC,CAAC;YACX,IAAIlF,aAAa,EAAE;cACjBA,aAAa,CAAC,CAAC;YACjB;UACF;QAAE;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC/F,EAAA,CAjkBIF,WAAW;AAAAgI,EAAA,GAAXhI,WAAW;AAmkBjB,eAAeA,WAAW;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
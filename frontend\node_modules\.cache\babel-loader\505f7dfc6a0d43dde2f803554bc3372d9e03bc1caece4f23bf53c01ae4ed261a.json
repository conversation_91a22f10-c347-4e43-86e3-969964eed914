{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\PatientPortal.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientPortal = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n      fetchMedicines();\n    }\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = medicine => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => item.id === medicine.id ? {\n        ...item,\n        quantity: item.quantity + 1\n      } : item));\n    } else {\n      setCart([...cart, {\n        ...medicine,\n        quantity: 1\n      }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = medicineId => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => item.id === medicineId ? {\n        ...item,\n        quantity: quantity\n      } : item));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + item.price * item.quantity, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-br from-blue-600 to-green-600 rounded-2xl flex items-center justify-center shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-8 h-8 text-white\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\",\n              children: \"Patient Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-lg\",\n              children: [\"Welcome back, \", (user === null || user === void 0 ? void 0 : user.name) || 'Patient']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-2 border border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('dashboard'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'dashboard' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('reports'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'reports' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this), \"Exam Reports\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('appointments'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'appointments' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), \"Appointments\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('pharmacy'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'pharmacy' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), \"Pharmacy\", cart.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                  children: cart.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-8\",\n        children: [activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(DashboardTab, {\n          user: user,\n          examReports: examReports,\n          appointments: appointments,\n          cartItems: cart.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), activeTab === 'reports' && /*#__PURE__*/_jsxDEV(ExamReportsTab, {\n          examReports: examReports,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), activeTab === 'appointments' && /*#__PURE__*/_jsxDEV(AppointmentsTab, {\n          appointments: appointments,\n          user: user,\n          onAppointmentCreated: fetchAppointments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), activeTab === 'pharmacy' && /*#__PURE__*/_jsxDEV(PharmacyTab, {\n          medicines: medicines,\n          cart: cart,\n          addToCart: addToCart,\n          removeFromCart: removeFromCart,\n          updateCartQuantity: updateCartQuantity,\n          cartTotal: cartTotal\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n\n// Dashboard Tab Component\n_s(PatientPortal, \"p7KAxN+MFu3TKk6+YM9C1Vv/ou0=\", false, function () {\n  return [useAuth];\n});\n_c = PatientPortal;\nconst DashboardTab = ({\n  user,\n  examReports,\n  appointments,\n  cartItems\n}) => {\n  const upcomingAppointments = appointments.filter(apt => new Date(apt.appointmentDate) > new Date()).slice(0, 3);\n  const recentReports = examReports.slice(0, 3);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl shadow-xl p-8 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold mb-2\",\n            children: \"Welcome to Your Health Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg\",\n            children: \"Manage your health records, appointments, and prescriptions all in one place\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-12 h-12 text-white\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-blue-600\",\n              children: examReports.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-blue-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-green-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-green-600\",\n              children: appointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-green-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-yellow-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Upcoming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-yellow-600\",\n              children: upcomingAppointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-yellow-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-purple-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Cart Items\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-purple-600\",\n              children: cartItems\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-purple-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-900 mb-4\",\n          children: \"Recent Exam Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), recentReports.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recentReports.map((report, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-900\",\n                children: report.examType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: new Date(report.examDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\",\n              children: report.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-center py-8\",\n          children: \"No exam reports available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-900 mb-4\",\n          children: \"Upcoming Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), upcomingAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: upcomingAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-900\",\n                children: [\"Dr. \", appointment.doctorName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: new Date(appointment.appointmentDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium\",\n              children: appointment.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-center py-8\",\n          children: \"No upcoming appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n\n// Exam Reports Tab Component\n_c2 = DashboardTab;\nconst ExamReportsTab = ({\n  examReports,\n  loading\n}) => {\n  _s2();\n  const [selectedReport, setSelectedReport] = useState(null);\n  const downloadReport = report => {\n    // Create a simple text report\n    const reportContent = `\nMEDICAL EXAM REPORT\n==================\n\nPatient: ${report.patientName}\nNational ID: ${report.nationalId}\nExam Type: ${report.examType}\nExam Date: ${new Date(report.examDate).toLocaleDateString()}\nDoctor: ${report.doctorName}\nStatus: ${report.status}\n\nRESULTS:\n${report.results}\n\nRECOMMENDATIONS:\n${report.recommendations || 'No specific recommendations'}\n\nReport generated on: ${new Date().toLocaleDateString()}\nHealthCarePro Medical Center\n    `;\n    const blob = new Blob([reportContent], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `exam-report-${report.examType}-${new Date(report.examDate).toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Your Exam Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), examReports.length, \" Reports Available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-3 text-gray-600\",\n          children: \"Loading reports...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 11\n      }, this) : examReports.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: examReports.map((report, index) => {\n          var _report$results;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-green-50 rounded-xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-blue-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-gray-900\",\n                    children: report.examType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: new Date(report.examDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${report.status === 'Completed' ? 'bg-green-100 text-green-800' : report.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                children: report.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Doctor:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 42\n                }, this), \" \", report.doctorName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Results:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 42\n                }, this), \" \", (_report$results = report.results) === null || _report$results === void 0 ? void 0 : _report$results.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedReport(report),\n                className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\",\n                children: \"View Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => downloadReport(report),\n                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-gray-400\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"No Exam Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"You don't have any exam reports yet. They will appear here once available.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), selectedReport && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Exam Report Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedReport(null),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Exam Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 font-semibold\",\n                  children: selectedReport.examType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: new Date(selectedReport.examDate).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Doctor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedReport.doctorName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block px-2 py-1 rounded-full text-xs font-medium ${selectedReport.status === 'Completed' ? 'bg-green-100 text-green-800' : selectedReport.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: selectedReport.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 whitespace-pre-wrap\",\n                  children: selectedReport.results\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this), selectedReport.recommendations && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Recommendations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 rounded-lg p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 whitespace-pre-wrap\",\n                  children: selectedReport.recommendations\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => downloadReport(selectedReport),\n                className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this), \"Download Report\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedReport(null),\n                className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\",\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 411,\n    columnNumber: 5\n  }, this);\n};\n\n// Appointments Tab Component\n_s2(ExamReportsTab, \"20BLP4QGphg0wQGhiT6pQGpcZQw=\");\n_c3 = ExamReportsTab;\nconst AppointmentsTab = ({\n  appointments,\n  user,\n  onAppointmentCreated\n}) => {\n  _s3();\n  const [showRequestForm, setShowRequestForm] = useState(false);\n  const [doctors, setDoctors] = useState([]);\n  const [formData, setFormData] = useState({\n    doctorId: '',\n    appointmentDate: '',\n    appointmentTime: '',\n    reason: '',\n    urgency: 'Normal'\n  });\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch doctors on component mount\n  useEffect(() => {\n    fetchDoctors();\n  }, []);\n  const fetchDoctors = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/doctors`);\n      const data = await response.json();\n      if (data.success) {\n        setDoctors(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching doctors:', error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const appointmentData = {\n        ...formData,\n        patientNationalId: user.nationalId,\n        patientName: user.name,\n        patientEmail: user.email,\n        patientPhone: user.phone,\n        status: 'Pending'\n      };\n      const response = await fetch(`${API_BASE_URL}/appointments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(appointmentData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Appointment request submitted successfully!');\n        setShowRequestForm(false);\n        setFormData({\n          doctorId: '',\n          appointmentDate: '',\n          appointmentTime: '',\n          reason: '',\n          urgency: 'Normal'\n        });\n        onAppointmentCreated();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error submitting appointment:', error);\n      alert('Error submitting appointment request');\n    }\n  };\n  const upcomingAppointments = appointments.filter(apt => new Date(apt.appointmentDate) > new Date());\n  const pastAppointments = appointments.filter(apt => new Date(apt.appointmentDate) <= new Date());\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Your Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowRequestForm(true),\n          className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 4v16m8-8H4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this), \"Request Appointment\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Upcoming Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), upcomingAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: upcomingAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-green-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900\",\n                    children: [\"Dr. \", appointment.doctorName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 692,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: appointment.specialty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${appointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' : appointment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                children: appointment.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 44\n                }, this), \" \", new Date(appointment.appointmentDate).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Time:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 44\n                }, this), \" \", appointment.appointmentTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Reason:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 44\n                }, this), \" \", appointment.reason]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 bg-gray-50 rounded-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"No upcoming appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Past Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this), pastAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: pastAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-gray-600\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: [\"Dr. \", appointment.doctorName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [new Date(appointment.appointmentDate).toLocaleDateString(), \" at \", appointment.appointmentTime]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${appointment.status === 'Completed' ? 'bg-blue-100 text-blue-800' : appointment.status === 'Cancelled' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`,\n              children: appointment.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 bg-gray-50 rounded-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"No past appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 7\n    }, this), showRequestForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Request Appointment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowRequestForm(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Select Doctor *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"doctorId\",\n                  value: formData.doctorId,\n                  onChange: handleInputChange,\n                  required: true,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Choose a doctor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 23\n                  }, this), doctors.map(doctor => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: doctor.id,\n                    children: [\"Dr. \", doctor.firstName, \" \", doctor.lastName, \" - \", doctor.specialty]\n                  }, doctor.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Urgency Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"urgency\",\n                  value: formData.urgency,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Normal\",\n                    children: \"Normal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Urgent\",\n                    children: \"Urgent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Emergency\",\n                    children: \"Emergency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Preferred Date *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"appointmentDate\",\n                  value: formData.appointmentDate,\n                  onChange: handleInputChange,\n                  min: new Date().toISOString().split('T')[0],\n                  required: true,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Preferred Time *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"time\",\n                  name: \"appointmentTime\",\n                  value: formData.appointmentTime,\n                  onChange: handleInputChange,\n                  required: true,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Reason for Visit *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"reason\",\n                value: formData.reason,\n                onChange: handleInputChange,\n                rows: \"4\",\n                required: true,\n                placeholder: \"Please describe your symptoms or reason for the appointment...\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 21\n                }, this), \"Submit Request\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowRequestForm(false),\n                className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 662,\n    columnNumber: 5\n  }, this);\n};\n\n// Pharmacy Tab Component\n_s3(AppointmentsTab, \"TLtSDVqQKl61zq2jQzbRSi+XwV8=\");\n_c4 = AppointmentsTab;\nconst PharmacyTab = ({\n  medicines,\n  cart,\n  addToCart,\n  removeFromCart,\n  updateCartQuantity,\n  cartTotal\n}) => {\n  _s4();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('All');\n  const [showCart, setShowCart] = useState(false);\n\n  // Filter medicines based on search and category\n  const filteredMedicines = medicines.filter(medicine => {\n    var _medicine$description;\n    const matchesSearch = medicine.name.toLowerCase().includes(searchQuery.toLowerCase()) || ((_medicine$description = medicine.description) === null || _medicine$description === void 0 ? void 0 : _medicine$description.toLowerCase().includes(searchQuery.toLowerCase()));\n    const matchesCategory = categoryFilter === 'All' || medicine.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories\n  const categories = ['All', ...new Set(medicines.map(med => med.category).filter(Boolean))];\n  const handleCheckout = async () => {\n    if (cart.length === 0) {\n      alert('Your cart is empty');\n      return;\n    }\n    try {\n      // Here you would typically process the order\n      alert(`Order placed successfully! Total: ${cartTotal.toFixed(2)} RWF`);\n      // Clear cart after successful order\n      cart.forEach(item => removeFromCart(item.id));\n      setShowCart(false);\n    } catch (error) {\n      console.error('Error processing order:', error);\n      alert('Error processing order. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Online Pharmacy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCart(true),\n          className: \"bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this), \"Cart (\", cart.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search medicines...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"absolute left-3 top-2.5 w-5 h-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: categoryFilter,\n            onChange: e => setCategoryFilter(e.target.value),\n            className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category,\n              children: category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this), filteredMedicines.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: filteredMedicines.map(medicine => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-6 border border-green-100 hover:shadow-lg transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6 text-green-600\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900\",\n                  children: medicine.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: medicine.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${medicine.stock > 10 ? 'bg-green-100 text-green-800' : medicine.stock > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n              children: medicine.stock > 0 ? `${medicine.stock} in stock` : 'Out of stock'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: medicine.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-bold text-green-600\",\n              children: [medicine.price, \" RWF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 19\n            }, this), medicine.dosage && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Dosage: \", medicine.dosage]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => addToCart(medicine),\n            disabled: medicine.stock === 0,\n            className: `w-full py-2 px-4 rounded-lg font-medium transition-colors ${medicine.stock > 0 ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n            children: medicine.stock > 0 ? 'Add to Cart' : 'Out of Stock'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 17\n          }, this)]\n        }, medicine.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 950,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-gray-400\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"No Medicines Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Try adjusting your search or filter criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 993,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 903,\n      columnNumber: 7\n    }, this), showCart && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Shopping Cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCart(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1010,\n            columnNumber: 15\n          }, this), cart.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-green-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1027,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [item.price, \" RWF each\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => updateCartQuantity(item.id, item.quantity - 1),\n                    className: \"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\",\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-8 text-center font-medium\",\n                    children: item.quantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => updateCartQuantity(item.id, item.quantity + 1),\n                    className: \"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1044,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-bold text-green-600 w-20 text-right\",\n                  children: [(item.price * item.quantity).toFixed(2), \" RWF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeFromCart(item.id),\n                  className: \"text-red-500 hover:text-red-700 p-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1057,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 23\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between text-xl font-bold mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Total:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: [cartTotal.toFixed(2), \" RWF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCheckout,\n                  className: \"flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300\",\n                  children: \"Proceed to Checkout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowCart(false),\n                  className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors\",\n                  children: \"Continue Shopping\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1076,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1021,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-12 h-12 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1089,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"Your cart is empty\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Add some medicines to get started.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1093,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1008,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1007,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 902,\n    columnNumber: 5\n  }, this);\n};\n_s4(PharmacyTab, \"IQvyvj4+vyoukcTENfghZo8Xh+M=\");\n_c5 = PharmacyTab;\nexport default PatientPortal;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PatientPortal\");\n$RefreshReg$(_c2, \"DashboardTab\");\n$RefreshReg$(_c3, \"ExamReportsTab\");\n$RefreshReg$(_c4, \"AppointmentsTab\");\n$RefreshReg$(_c5, \"PharmacyTab\");", "map": {"version": 3, "names": ["useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "PatientPortal", "_s", "user", "activeTab", "setActiveTab", "examReports", "setExamReports", "appointments", "setAppointments", "medicines", "setMedicines", "cart", "setCart", "loading", "setLoading", "API_BASE_URL", "nationalId", "fetchExamReports", "fetchAppointments", "fetchMedicines", "response", "fetch", "data", "json", "success", "error", "console", "addToCart", "medicine", "existingItem", "find", "item", "id", "map", "quantity", "removeFromCart", "medicineId", "filter", "updateCartQuantity", "cartTotal", "reduce", "total", "price", "className", "children", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "length", "DashboardTab", "cartItems", "ExamReportsTab", "AppointmentsTab", "onAppointmentCreated", "PharmacyTab", "_c", "upcomingAppointments", "apt", "Date", "appointmentDate", "slice", "recentReports", "report", "index", "examType", "examDate", "toLocaleDateString", "status", "appointment", "<PERSON><PERSON><PERSON>", "_c2", "_s2", "selectedReport", "setSelectedReport", "downloadReport", "reportContent", "patientName", "results", "recommendations", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_report$results", "substring", "_c3", "_s3", "showRequestForm", "setShowRequestForm", "doctors", "setDoctors", "formData", "setFormData", "doctorId", "appointmentTime", "reason", "urgency", "fetchDoctors", "handleInputChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "appointmentData", "patientNationalId", "patientEmail", "email", "patientPhone", "phone", "method", "headers", "JSON", "stringify", "alert", "message", "pastAppointments", "specialty", "onSubmit", "onChange", "required", "doctor", "firstName", "lastName", "min", "rows", "placeholder", "_c4", "_s4", "searchQuery", "setSearch<PERSON>uery", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "showCart", "setShowCart", "filteredMedicines", "_medicine$description", "matchesSearch", "toLowerCase", "includes", "description", "matchesCategory", "category", "categories", "Set", "med", "Boolean", "handleCheckout", "toFixed", "for<PERSON>ach", "stock", "dosage", "disabled", "_c5", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/pages/PatientPortal.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\n\nconst PatientPortal = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount\n  useEffect(() => {\n    if (user?.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n      fetchMedicines();\n    }\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = (medicine) => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => \n        item.id === medicine.id \n          ? { ...item, quantity: item.quantity + 1 }\n          : item\n      ));\n    } else {\n      setCart([...cart, { ...medicine, quantity: 1 }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = (medicineId) => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => \n        item.id === medicineId \n          ? { ...item, quantity: quantity }\n          : item\n      ));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\">\n      <div className=\"max-w-7xl mx-auto px-6 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center gap-4 mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-600 to-green-600 rounded-2xl flex items-center justify-center shadow-lg\">\n              <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n            <div>\n              <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\">\n                Patient Portal\n              </h1>\n              <p className=\"text-gray-600 text-lg\">Welcome back, {user?.name || 'Patient'}</p>\n            </div>\n          </div>\n\n          {/* Navigation Tabs */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-2 border border-gray-100\">\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'dashboard'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                  Dashboard\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('reports')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'reports'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                  Exam Reports\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('appointments')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'appointments'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                  Appointments\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('pharmacy')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'pharmacy'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                  </svg>\n                  Pharmacy\n                  {cart.length > 0 && (\n                    <span className=\"bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                      {cart.length}\n                    </span>\n                  )}\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"space-y-8\">\n          {activeTab === 'dashboard' && (\n            <DashboardTab \n              user={user}\n              examReports={examReports}\n              appointments={appointments}\n              cartItems={cart.length}\n            />\n          )}\n          \n          {activeTab === 'reports' && (\n            <ExamReportsTab \n              examReports={examReports}\n              loading={loading}\n            />\n          )}\n          \n          {activeTab === 'appointments' && (\n            <AppointmentsTab \n              appointments={appointments}\n              user={user}\n              onAppointmentCreated={fetchAppointments}\n            />\n          )}\n          \n          {activeTab === 'pharmacy' && (\n            <PharmacyTab \n              medicines={medicines}\n              cart={cart}\n              addToCart={addToCart}\n              removeFromCart={removeFromCart}\n              updateCartQuantity={updateCartQuantity}\n              cartTotal={cartTotal}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Dashboard Tab Component\nconst DashboardTab = ({ user, examReports, appointments, cartItems }) => {\n  const upcomingAppointments = appointments.filter(apt =>\n    new Date(apt.appointmentDate) > new Date()\n  ).slice(0, 3);\n\n  const recentReports = examReports.slice(0, 3);\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl shadow-xl p-8 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-3xl font-bold mb-2\">Welcome to Your Health Portal</h2>\n            <p className=\"text-blue-100 text-lg\">\n              Manage your health records, appointments, and prescriptions all in one place\n            </p>\n          </div>\n          <div className=\"hidden md:block\">\n            <div className=\"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-blue-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Reports</p>\n              <p className=\"text-3xl font-bold text-blue-600\">{examReports.length}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-green-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Appointments</p>\n              <p className=\"text-3xl font-bold text-green-600\">{appointments.length}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-yellow-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Upcoming</p>\n              <p className=\"text-3xl font-bold text-yellow-600\">{upcomingAppointments.length}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-purple-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Cart Items</p>\n              <p className=\"text-3xl font-bold text-purple-600\">{cartItems}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Recent Reports */}\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Recent Exam Reports</h3>\n          {recentReports.length > 0 ? (\n            <div className=\"space-y-3\">\n              {recentReports.map((report, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">{report.examType}</p>\n                    <p className=\"text-sm text-gray-600\">{new Date(report.examDate).toLocaleDateString()}</p>\n                  </div>\n                  <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                    {report.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500 text-center py-8\">No exam reports available</p>\n          )}\n        </div>\n\n        {/* Upcoming Appointments */}\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Upcoming Appointments</h3>\n          {upcomingAppointments.length > 0 ? (\n            <div className=\"space-y-3\">\n              {upcomingAppointments.map((appointment, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">Dr. {appointment.doctorName}</p>\n                    <p className=\"text-sm text-gray-600\">{new Date(appointment.appointmentDate).toLocaleDateString()}</p>\n                  </div>\n                  <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium\">\n                    {appointment.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500 text-center py-8\">No upcoming appointments</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Exam Reports Tab Component\nconst ExamReportsTab = ({ examReports, loading }) => {\n  const [selectedReport, setSelectedReport] = useState(null);\n\n  const downloadReport = (report) => {\n    // Create a simple text report\n    const reportContent = `\nMEDICAL EXAM REPORT\n==================\n\nPatient: ${report.patientName}\nNational ID: ${report.nationalId}\nExam Type: ${report.examType}\nExam Date: ${new Date(report.examDate).toLocaleDateString()}\nDoctor: ${report.doctorName}\nStatus: ${report.status}\n\nRESULTS:\n${report.results}\n\nRECOMMENDATIONS:\n${report.recommendations || 'No specific recommendations'}\n\nReport generated on: ${new Date().toLocaleDateString()}\nHealthCarePro Medical Center\n    `;\n\n    const blob = new Blob([reportContent], { type: 'text/plain' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `exam-report-${report.examType}-${new Date(report.examDate).toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Your Exam Reports</h2>\n          <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            {examReports.length} Reports Available\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <span className=\"ml-3 text-gray-600\">Loading reports...</span>\n          </div>\n        ) : examReports.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {examReports.map((report, index) => (\n              <div key={index} className=\"bg-gradient-to-br from-blue-50 to-green-50 rounded-xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-bold text-gray-900\">{report.examType}</h3>\n                      <p className=\"text-sm text-gray-600\">{new Date(report.examDate).toLocaleDateString()}</p>\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    report.status === 'Completed' ? 'bg-green-100 text-green-800' :\n                    report.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {report.status}\n                  </span>\n                </div>\n\n                <div className=\"space-y-2 mb-4\">\n                  <p className=\"text-sm\"><span className=\"font-medium\">Doctor:</span> {report.doctorName}</p>\n                  <p className=\"text-sm\"><span className=\"font-medium\">Results:</span> {report.results?.substring(0, 100)}...</p>\n                </div>\n\n                <div className=\"flex gap-2\">\n                  <button\n                    onClick={() => setSelectedReport(report)}\n                    className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                  >\n                    View Details\n                  </button>\n                  <button\n                    onClick={() => downloadReport(report)}\n                    className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Exam Reports</h3>\n            <p className=\"text-gray-600\">You don't have any exam reports yet. They will appear here once available.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Report Details Modal */}\n      {selectedReport && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Exam Report Details</h3>\n                <button\n                  onClick={() => setSelectedReport(null)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Exam Type</label>\n                    <p className=\"text-gray-900 font-semibold\">{selectedReport.examType}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Date</label>\n                    <p className=\"text-gray-900\">{new Date(selectedReport.examDate).toLocaleDateString()}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Doctor</label>\n                    <p className=\"text-gray-900\">{selectedReport.doctorName}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${\n                      selectedReport.status === 'Completed' ? 'bg-green-100 text-green-800' :\n                      selectedReport.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      {selectedReport.status}\n                    </span>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Results</label>\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <p className=\"text-gray-900 whitespace-pre-wrap\">{selectedReport.results}</p>\n                  </div>\n                </div>\n\n                {selectedReport.recommendations && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Recommendations</label>\n                    <div className=\"bg-blue-50 rounded-lg p-4\">\n                      <p className=\"text-gray-900 whitespace-pre-wrap\">{selectedReport.recommendations}</p>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    onClick={() => downloadReport(selectedReport)}\n                    className=\"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                    Download Report\n                  </button>\n                  <button\n                    onClick={() => setSelectedReport(null)}\n                    className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\"\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Appointments Tab Component\nconst AppointmentsTab = ({ appointments, user, onAppointmentCreated }) => {\n  const [showRequestForm, setShowRequestForm] = useState(false);\n  const [doctors, setDoctors] = useState([]);\n  const [formData, setFormData] = useState({\n    doctorId: '',\n    appointmentDate: '',\n    appointmentTime: '',\n    reason: '',\n    urgency: 'Normal'\n  });\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch doctors on component mount\n  useEffect(() => {\n    fetchDoctors();\n  }, []);\n\n  const fetchDoctors = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/doctors`);\n      const data = await response.json();\n      if (data.success) {\n        setDoctors(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching doctors:', error);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const appointmentData = {\n        ...formData,\n        patientNationalId: user.nationalId,\n        patientName: user.name,\n        patientEmail: user.email,\n        patientPhone: user.phone,\n        status: 'Pending'\n      };\n\n      const response = await fetch(`${API_BASE_URL}/appointments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(appointmentData),\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        alert('Appointment request submitted successfully!');\n        setShowRequestForm(false);\n        setFormData({\n          doctorId: '',\n          appointmentDate: '',\n          appointmentTime: '',\n          reason: '',\n          urgency: 'Normal'\n        });\n        onAppointmentCreated();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error submitting appointment:', error);\n      alert('Error submitting appointment request');\n    }\n  };\n\n  const upcomingAppointments = appointments.filter(apt =>\n    new Date(apt.appointmentDate) > new Date()\n  );\n\n  const pastAppointments = appointments.filter(apt =>\n    new Date(apt.appointmentDate) <= new Date()\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Your Appointments</h2>\n          <button\n            onClick={() => setShowRequestForm(true)}\n            className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Request Appointment\n          </button>\n        </div>\n\n        {/* Upcoming Appointments */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Upcoming Appointments</h3>\n          {upcomingAppointments.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {upcomingAppointments.map((appointment, index) => (\n                <div key={index} className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-100\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                        <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-bold text-gray-900\">Dr. {appointment.doctorName}</h4>\n                        <p className=\"text-sm text-gray-600\">{appointment.specialty}</p>\n                      </div>\n                    </div>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      appointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' :\n                      appointment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-red-100 text-red-800'\n                    }`}>\n                      {appointment.status}\n                    </span>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <p className=\"text-sm\"><span className=\"font-medium\">Date:</span> {new Date(appointment.appointmentDate).toLocaleDateString()}</p>\n                    <p className=\"text-sm\"><span className=\"font-medium\">Time:</span> {appointment.appointmentTime}</p>\n                    <p className=\"text-sm\"><span className=\"font-medium\">Reason:</span> {appointment.reason}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 bg-gray-50 rounded-xl\">\n              <p className=\"text-gray-500\">No upcoming appointments</p>\n            </div>\n          )}\n        </div>\n\n        {/* Past Appointments */}\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Past Appointments</h3>\n          {pastAppointments.length > 0 ? (\n            <div className=\"space-y-3\">\n              {pastAppointments.map((appointment, index) => (\n                <div key={index} className=\"bg-gray-50 rounded-lg p-4 flex items-center justify-between\">\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"font-semibold text-gray-900\">Dr. {appointment.doctorName}</p>\n                      <p className=\"text-sm text-gray-600\">{new Date(appointment.appointmentDate).toLocaleDateString()} at {appointment.appointmentTime}</p>\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    appointment.status === 'Completed' ? 'bg-blue-100 text-blue-800' :\n                    appointment.status === 'Cancelled' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {appointment.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 bg-gray-50 rounded-xl\">\n              <p className=\"text-gray-500\">No past appointments</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Request Appointment Modal */}\n      {showRequestForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Request Appointment</h3>\n                <button\n                  onClick={() => setShowRequestForm(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Select Doctor *</label>\n                    <select\n                      name=\"doctorId\"\n                      value={formData.doctorId}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"\">Choose a doctor</option>\n                      {doctors.map((doctor) => (\n                        <option key={doctor.id} value={doctor.id}>\n                          Dr. {doctor.firstName} {doctor.lastName} - {doctor.specialty}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Urgency Level</label>\n                    <select\n                      name=\"urgency\"\n                      value={formData.urgency}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"Normal\">Normal</option>\n                      <option value=\"Urgent\">Urgent</option>\n                      <option value=\"Emergency\">Emergency</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Preferred Date *</label>\n                    <input\n                      type=\"date\"\n                      name=\"appointmentDate\"\n                      value={formData.appointmentDate}\n                      onChange={handleInputChange}\n                      min={new Date().toISOString().split('T')[0]}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Preferred Time *</label>\n                    <input\n                      type=\"time\"\n                      name=\"appointmentTime\"\n                      value={formData.appointmentTime}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Reason for Visit *</label>\n                  <textarea\n                    name=\"reason\"\n                    value={formData.reason}\n                    onChange={handleInputChange}\n                    rows=\"4\"\n                    required\n                    placeholder=\"Please describe your symptoms or reason for the appointment...\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                    Submit Request\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowRequestForm(false)}\n                    className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Pharmacy Tab Component\nconst PharmacyTab = ({ medicines, cart, addToCart, removeFromCart, updateCartQuantity, cartTotal }) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('All');\n  const [showCart, setShowCart] = useState(false);\n\n  // Filter medicines based on search and category\n  const filteredMedicines = medicines.filter(medicine => {\n    const matchesSearch = medicine.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         medicine.description?.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = categoryFilter === 'All' || medicine.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories\n  const categories = ['All', ...new Set(medicines.map(med => med.category).filter(Boolean))];\n\n  const handleCheckout = async () => {\n    if (cart.length === 0) {\n      alert('Your cart is empty');\n      return;\n    }\n\n    try {\n      // Here you would typically process the order\n      alert(`Order placed successfully! Total: ${cartTotal.toFixed(2)} RWF`);\n      // Clear cart after successful order\n      cart.forEach(item => removeFromCart(item.id));\n      setShowCart(false);\n    } catch (error) {\n      console.error('Error processing order:', error);\n      alert('Error processing order. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Online Pharmacy</h2>\n          <button\n            onClick={() => setShowCart(true)}\n            className=\"bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\n            </svg>\n            Cart ({cart.length})\n          </button>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"flex flex-col md:flex-row gap-4 mb-6\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search medicines...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <svg className=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z\" />\n              </svg>\n            </div>\n          </div>\n          <div>\n            <select\n              value={categoryFilter}\n              onChange={(e) => setCategoryFilter(e.target.value)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {categories.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Medicines Grid */}\n        {filteredMedicines.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredMedicines.map((medicine) => (\n              <div key={medicine.id} className=\"bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-6 border border-green-100 hover:shadow-lg transition-all duration-300\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-bold text-gray-900\">{medicine.name}</h3>\n                      <p className=\"text-sm text-gray-600\">{medicine.category}</p>\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    medicine.stock > 10 ? 'bg-green-100 text-green-800' :\n                    medicine.stock > 0 ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-red-100 text-red-800'\n                  }`}>\n                    {medicine.stock > 0 ? `${medicine.stock} in stock` : 'Out of stock'}\n                  </span>\n                </div>\n\n                <div className=\"space-y-2 mb-4\">\n                  <p className=\"text-sm text-gray-700\">{medicine.description}</p>\n                  <p className=\"text-lg font-bold text-green-600\">{medicine.price} RWF</p>\n                  {medicine.dosage && <p className=\"text-sm text-gray-600\">Dosage: {medicine.dosage}</p>}\n                </div>\n\n                <button\n                  onClick={() => addToCart(medicine)}\n                  disabled={medicine.stock === 0}\n                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${\n                    medicine.stock > 0\n                      ? 'bg-green-600 hover:bg-green-700 text-white'\n                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  }`}\n                >\n                  {medicine.stock > 0 ? 'Add to Cart' : 'Out of Stock'}\n                </button>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Medicines Found</h3>\n            <p className=\"text-gray-600\">Try adjusting your search or filter criteria.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Shopping Cart Modal */}\n      {showCart && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Shopping Cart</h3>\n                <button\n                  onClick={() => setShowCart(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              {cart.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {cart.map((item) => (\n                    <div key={item.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                          <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <h4 className=\"font-semibold text-gray-900\">{item.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{item.price} RWF each</p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"flex items-center gap-2\">\n                          <button\n                            onClick={() => updateCartQuantity(item.id, item.quantity - 1)}\n                            className=\"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\"\n                          >\n                            -\n                          </button>\n                          <span className=\"w-8 text-center font-medium\">{item.quantity}</span>\n                          <button\n                            onClick={() => updateCartQuantity(item.id, item.quantity + 1)}\n                            className=\"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\"\n                          >\n                            +\n                          </button>\n                        </div>\n                        <p className=\"font-bold text-green-600 w-20 text-right\">{(item.price * item.quantity).toFixed(2)} RWF</p>\n                        <button\n                          onClick={() => removeFromCart(item.id)}\n                          className=\"text-red-500 hover:text-red-700 p-1\"\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n\n                  <div className=\"border-t pt-4\">\n                    <div className=\"flex justify-between text-xl font-bold mb-4\">\n                      <span>Total:</span>\n                      <span className=\"text-green-600\">{cartTotal.toFixed(2)} RWF</span>\n                    </div>\n                    <div className=\"flex gap-3\">\n                      <button\n                        onClick={handleCheckout}\n                        className=\"flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300\"\n                      >\n                        Proceed to Checkout\n                      </button>\n                      <button\n                        onClick={() => setShowCart(false)}\n                        className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors\"\n                      >\n                        Continue Shopping\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Your cart is empty</h3>\n                  <p className=\"text-gray-600\">Add some medicines to get started.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PatientPortal;\n"], "mappings": ";;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMoB,YAAY,GAAG,2BAA2B;;EAEhD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEc,UAAU,EAAE;MACpBC,gBAAgB,CAAC,CAAC;MAClBC,iBAAiB,CAAC,CAAC;MACnBC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACjB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMe,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,kBAAkBb,IAAI,CAACc,UAAU,EAAE,CAAC;MAChF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBlB,cAAc,CAACgB,IAAI,CAACA,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,yBAAyBb,IAAI,CAACc,UAAU,EAAE,CAAC;MACvF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBhB,eAAe,CAACc,IAAI,CAACA,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMN,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,WAAW,CAAC;MACxD,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBd,YAAY,CAACY,IAAI,CAACA,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAME,SAAS,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,YAAY,GAAGlB,IAAI,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,CAAC;IAC/D,IAAIH,YAAY,EAAE;MAChBjB,OAAO,CAACD,IAAI,CAACsB,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,GACnB;QAAE,GAAGD,IAAI;QAAEG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG;MAAE,CAAC,GACxCH,IACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACLnB,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGiB,QAAQ;QAAEM,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrCxB,OAAO,CAACD,IAAI,CAAC0B,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKI,UAAU,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACF,UAAU,EAAEF,QAAQ,KAAK;IACnD,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBC,cAAc,CAACC,UAAU,CAAC;IAC5B,CAAC,MAAM;MACLxB,OAAO,CAACD,IAAI,CAACsB,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKI,UAAU,GAClB;QAAE,GAAGL,IAAI;QAAEG,QAAQ,EAAEA;MAAS,CAAC,GAC/BH,IACN,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAG5B,IAAI,CAAC6B,MAAM,CAAC,CAACC,KAAK,EAAEV,IAAI,KAAKU,KAAK,GAAIV,IAAI,CAACW,KAAK,GAAGX,IAAI,CAACG,QAAS,EAAE,CAAC,CAAC;EAEvF,oBACEnC,OAAA;IAAK4C,SAAS,EAAC,mEAAmE;IAAAC,QAAA,eAChF7C,OAAA;MAAK4C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1C7C,OAAA;QAAK4C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB7C,OAAA;UAAK4C,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C7C,OAAA;YAAK4C,SAAS,EAAC,+GAA+G;YAAAC,QAAA,eAC5H7C,OAAA;cAAK4C,SAAS,EAAC,oBAAoB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACvG7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAqE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAI4C,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EAAC;YAE7G;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxD,OAAA;cAAG4C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,gBAAc,EAAC,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,KAAI,SAAS;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxD,OAAA;UAAK4C,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxE7C,OAAA;YAAK4C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7C,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,WAAW,CAAE;cACzCuC,SAAS,EAAE,kEACTxC,SAAS,KAAK,WAAW,GACrB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAyC,QAAA,eAEH7C,OAAA;gBAAM4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3P,CAAC,aAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACTxD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,SAAS,CAAE;cACvCuC,SAAS,EAAE,kEACTxC,SAAS,KAAK,SAAS,GACnB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAyC,QAAA,eAEH7C,OAAA;gBAAM4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3K,CAAC,gBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACTxD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,cAAc,CAAE;cAC5CuC,SAAS,EAAE,kEACTxC,SAAS,KAAK,cAAc,GACxB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAyC,QAAA,eAEH7C,OAAA;gBAAM4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAwF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC,gBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACTxD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,UAAU,CAAE;cACxCuC,SAAS,EAAE,kEACTxC,SAAS,KAAK,UAAU,GACpB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAyC,QAAA,eAEH7C,OAAA;gBAAM4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5T,CAAC,YAEN,EAAC5C,IAAI,CAAC+C,MAAM,GAAG,CAAC,iBACd3D,OAAA;kBAAM4C,SAAS,EAAC,qFAAqF;kBAAAC,QAAA,EAClGjC,IAAI,CAAC+C;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBzC,SAAS,KAAK,WAAW,iBACxBJ,OAAA,CAAC4D,YAAY;UACXzD,IAAI,EAAEA,IAAK;UACXG,WAAW,EAAEA,WAAY;UACzBE,YAAY,EAAEA,YAAa;UAC3BqD,SAAS,EAAEjD,IAAI,CAAC+C;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACF,EAEApD,SAAS,KAAK,SAAS,iBACtBJ,OAAA,CAAC8D,cAAc;UACbxD,WAAW,EAAEA,WAAY;UACzBQ,OAAO,EAAEA;QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACF,EAEApD,SAAS,KAAK,cAAc,iBAC3BJ,OAAA,CAAC+D,eAAe;UACdvD,YAAY,EAAEA,YAAa;UAC3BL,IAAI,EAAEA,IAAK;UACX6D,oBAAoB,EAAE7C;QAAkB;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CACF,EAEApD,SAAS,KAAK,UAAU,iBACvBJ,OAAA,CAACiE,WAAW;UACVvD,SAAS,EAAEA,SAAU;UACrBE,IAAI,EAAEA,IAAK;UACXgB,SAAS,EAAEA,SAAU;UACrBQ,cAAc,EAAEA,cAAe;UAC/BG,kBAAkB,EAAEA,kBAAmB;UACvCC,SAAS,EAAEA;QAAU;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAtD,EAAA,CAtOMD,aAAa;EAAA,QACAH,OAAO;AAAA;AAAAoE,EAAA,GADpBjE,aAAa;AAuOnB,MAAM2D,YAAY,GAAGA,CAAC;EAAEzD,IAAI;EAAEG,WAAW;EAAEE,YAAY;EAAEqD;AAAU,CAAC,KAAK;EACvE,MAAMM,oBAAoB,GAAG3D,YAAY,CAAC8B,MAAM,CAAC8B,GAAG,IAClD,IAAIC,IAAI,CAACD,GAAG,CAACE,eAAe,CAAC,GAAG,IAAID,IAAI,CAAC,CAC3C,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEb,MAAMC,aAAa,GAAGlE,WAAW,CAACiE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAE7C,oBACEvE,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7C,OAAA;MAAK4C,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/F7C,OAAA;QAAK4C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD7C,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAI4C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAA6B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1ExD,OAAA;YAAG4C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNxD,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7C,OAAA;YAAK4C,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF7C,OAAA;cAAK4C,SAAS,EAAC,sBAAsB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACzG7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA6H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAK4C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD7C,OAAA;QAAK4C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE7C,OAAA;UAAK4C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAG4C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClExD,OAAA;cAAG4C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEvC,WAAW,CAACqD;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACNxD,OAAA;YAAK4C,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF7C,OAAA;cAAK4C,SAAS,EAAC,uBAAuB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC1G7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAsH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAK4C,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzE7C,OAAA;UAAK4C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAG4C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjExD,OAAA;cAAG4C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAErC,YAAY,CAACmD;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNxD,OAAA;YAAK4C,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjF7C,OAAA;cAAK4C,SAAS,EAAC,wBAAwB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC3G7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAwF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAK4C,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1E7C,OAAA;UAAK4C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAG4C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7DxD,OAAA;cAAG4C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEsB,oBAAoB,CAACR;YAAM;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACNxD,OAAA;YAAK4C,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF7C,OAAA;cAAK4C,SAAS,EAAC,yBAAyB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5G7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAK4C,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1E7C,OAAA;UAAK4C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAG4C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/DxD,OAAA;cAAG4C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEgB;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNxD,OAAA;YAAK4C,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF7C,OAAA;cAAK4C,SAAS,EAAC,yBAAyB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5G7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAwI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7L;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAK4C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD7C,OAAA;QAAK4C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE7C,OAAA;UAAI4C,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5EgB,aAAa,CAACb,MAAM,GAAG,CAAC,gBACvB3D,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB2B,aAAa,CAACtC,GAAG,CAAC,CAACuC,MAAM,EAAEC,KAAK,kBAC/B1E,OAAA;YAAiB4C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBACtF7C,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAG4C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAE4B,MAAM,CAACE;cAAQ;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChExD,OAAA;gBAAG4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE,IAAIwB,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACNxD,OAAA;cAAM4C,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACnF4B,MAAM,CAACK;YAAM;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA,GAPCkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENxD,OAAA;UAAG4C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxD,OAAA;QAAK4C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE7C,OAAA;UAAI4C,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAqB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9EW,oBAAoB,CAACR,MAAM,GAAG,CAAC,gBAC9B3D,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBsB,oBAAoB,CAACjC,GAAG,CAAC,CAAC6C,WAAW,EAAEL,KAAK,kBAC3C1E,OAAA;YAAiB4C,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBACvF7C,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAG4C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,MAAI,EAACkC,WAAW,CAACC,UAAU;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ExD,OAAA;gBAAG4C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE,IAAIwB,IAAI,CAACU,WAAW,CAACT,eAAe,CAAC,CAACO,kBAAkB,CAAC;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNxD,OAAA;cAAM4C,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EACrFkC,WAAW,CAACD;YAAM;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA,GAPCkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENxD,OAAA;UAAG4C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAwB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC1E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAyB,GAAA,GAzIMrB,YAAY;AA0IlB,MAAME,cAAc,GAAGA,CAAC;EAAExD,WAAW;EAAEQ;AAAQ,CAAC,KAAK;EAAAoE,GAAA;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMyF,cAAc,GAAIZ,MAAM,IAAK;IACjC;IACA,MAAMa,aAAa,GAAG;AAC1B;AACA;AACA;AACA,WAAWb,MAAM,CAACc,WAAW;AAC7B,eAAed,MAAM,CAACxD,UAAU;AAChC,aAAawD,MAAM,CAACE,QAAQ;AAC5B,aAAa,IAAIN,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC3D,UAAUJ,MAAM,CAACO,UAAU;AAC3B,UAAUP,MAAM,CAACK,MAAM;AACvB;AACA;AACA,EAAEL,MAAM,CAACe,OAAO;AAChB;AACA;AACA,EAAEf,MAAM,CAACgB,eAAe,IAAI,6BAA6B;AACzD;AACA,uBAAuB,IAAIpB,IAAI,CAAC,CAAC,CAACQ,kBAAkB,CAAC,CAAC;AACtD;AACA,KAAK;IAED,MAAMa,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,aAAa,CAAC,EAAE;MAAEM,IAAI,EAAE;IAAa,CAAC,CAAC;IAC9D,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,eAAe5B,MAAM,CAACE,QAAQ,IAAI,IAAIN,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAAC0B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC1GL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,CAAC,CAAC;IAC5BA,CAAC,CAACS,KAAK,CAAC,CAAC;IACTR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,CAAC,CAAC;IAC5BH,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;EACjC,CAAC;EAED,oBACE7F,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB7C,OAAA;MAAK4C,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxE7C,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD7C,OAAA;UAAI4C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvExD,OAAA;UAAK4C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D7C,OAAA;YAAK4C,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5F7C,OAAA;cAAMkD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K,CAAC,EACLlD,WAAW,CAACqD,MAAM,EAAC,oBACtB;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL1C,OAAO,gBACNd,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD7C,OAAA;UAAK4C,SAAS,EAAC;QAA8D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFxD,OAAA;UAAM4C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GACJlD,WAAW,CAACqD,MAAM,GAAG,CAAC,gBACxB3D,OAAA;QAAK4C,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEvC,WAAW,CAAC4B,GAAG,CAAC,CAACuC,MAAM,EAAEC,KAAK;UAAA,IAAAmC,eAAA;UAAA,oBAC7B7G,OAAA;YAAiB4C,SAAS,EAAC,8HAA8H;YAAAC,QAAA,gBACvJ7C,OAAA;cAAK4C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7C,OAAA;gBAAK4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC7C,OAAA;kBAAK4C,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,eAChF7C,OAAA;oBAAK4C,SAAS,EAAC,uBAAuB;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC1G7C,OAAA;sBAAMkD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAsH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3K;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxD,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAI4C,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAE4B,MAAM,CAACE;kBAAQ;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9DxD,OAAA;oBAAG4C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE,IAAIwB,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAM4C,SAAS,EAAE,8CACf6B,MAAM,CAACK,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC7DL,MAAM,CAACK,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAC7D,2BAA2B,EAC1B;gBAAAjC,QAAA,EACA4B,MAAM,CAACK;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxD,OAAA;cAAK4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7C,OAAA;gBAAG4C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACiB,MAAM,CAACO,UAAU;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FxD,OAAA;gBAAG4C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,GAAAqD,eAAA,GAACpC,MAAM,CAACe,OAAO,cAAAqB,eAAA,uBAAdA,eAAA,CAAgBC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eAENxD,OAAA;cAAK4C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB7C,OAAA;gBACE0D,OAAO,EAAEA,CAAA,KAAM0B,iBAAiB,CAACX,MAAM,CAAE;gBACzC7B,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,EACvH;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxD,OAAA;gBACE0D,OAAO,EAAEA,CAAA,KAAM2B,cAAc,CAACZ,MAAM,CAAE;gBACtC7B,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,eAEjH7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAiI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GA1CEkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2CV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENxD,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UAAK4C,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/F7C,OAAA;YAAK4C,SAAS,EAAC,yBAAyB;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5G7C,OAAA;cAAMkD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxD,OAAA;UAAI4C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7ExD,OAAA;UAAG4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0E;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL2B,cAAc,iBACbnF,OAAA;MAAK4C,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F7C,OAAA;QAAK4C,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F7C,OAAA;UAAK4C,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB7C,OAAA;YAAK4C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF7C,OAAA;cAAI4C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzExD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAM0B,iBAAiB,CAAC,IAAI,CAAE;cACvCxC,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxD,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7C,OAAA;cAAK4C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5ExD,OAAA;kBAAG4C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEsC,cAAc,CAACR;gBAAQ;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvExD,OAAA;kBAAG4C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,IAAIwB,IAAI,CAACc,cAAc,CAACP,QAAQ,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzExD,OAAA;kBAAG4C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEsC,cAAc,CAACH;gBAAU;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzExD,OAAA;kBAAM4C,SAAS,EAAE,2DACfuC,cAAc,CAACL,MAAM,KAAK,WAAW,GAAG,6BAA6B,GACrEK,cAAc,CAACL,MAAM,KAAK,SAAS,GAAG,+BAA+B,GACrE,2BAA2B,EAC1B;kBAAAjC,QAAA,EACAsC,cAAc,CAACL;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxD,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAO4C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/ExD,OAAA;gBAAK4C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC7C,OAAA;kBAAG4C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEsC,cAAc,CAACK;gBAAO;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL2B,cAAc,CAACM,eAAe,iBAC7BzF,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAO4C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFxD,OAAA;gBAAK4C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC7C,OAAA;kBAAG4C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEsC,cAAc,CAACM;gBAAe;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAEDxD,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7C,OAAA;gBACE0D,OAAO,EAAEA,CAAA,KAAM2B,cAAc,CAACF,cAAc,CAAE;gBAC9CvC,SAAS,EAAC,uHAAuH;gBAAAC,QAAA,gBAEjI7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAiI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtL,CAAC,mBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxD,OAAA;gBACE0D,OAAO,EAAEA,CAAA,KAAM0B,iBAAiB,CAAC,IAAI,CAAE;gBACvCxC,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAC3G;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA0B,GAAA,CAxMMpB,cAAc;AAAAiD,GAAA,GAAdjD,cAAc;AAyMpB,MAAMC,eAAe,GAAGA,CAAC;EAAEvD,YAAY;EAAEL,IAAI;EAAE6D;AAAqB,CAAC,KAAK;EAAAgD,GAAA;EACxE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuH,OAAO,EAAEC,UAAU,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyH,QAAQ,EAAEC,WAAW,CAAC,GAAG1H,QAAQ,CAAC;IACvC2H,QAAQ,EAAE,EAAE;IACZjD,eAAe,EAAE,EAAE;IACnBkD,eAAe,EAAE,EAAE;IACnBC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM1G,YAAY,GAAG,2BAA2B;;EAEhD;EACAnB,SAAS,CAAC,MAAM;IACd8H,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMtG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,UAAU,CAAC;MACvD,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB2F,UAAU,CAAC7F,IAAI,CAACA,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMkG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEpE,IAAI;MAAEqE;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCT,WAAW,CAACU,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACvE,IAAI,GAAGqE;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,eAAe,GAAG;QACtB,GAAGd,QAAQ;QACXe,iBAAiB,EAAEjI,IAAI,CAACc,UAAU;QAClCsE,WAAW,EAAEpF,IAAI,CAACsD,IAAI;QACtB4E,YAAY,EAAElI,IAAI,CAACmI,KAAK;QACxBC,YAAY,EAAEpI,IAAI,CAACqI,KAAK;QACxB1D,MAAM,EAAE;MACV,CAAC;MAED,MAAMzD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,eAAe,EAAE;QAC3DyH,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDlC,IAAI,EAAEmC,IAAI,CAACC,SAAS,CAACT,eAAe;MACtC,CAAC,CAAC;MAEF,MAAM5G,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBoH,KAAK,CAAC,6CAA6C,CAAC;QACpD3B,kBAAkB,CAAC,KAAK,CAAC;QACzBI,WAAW,CAAC;UACVC,QAAQ,EAAE,EAAE;UACZjD,eAAe,EAAE,EAAE;UACnBkD,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE,EAAE;UACVC,OAAO,EAAE;QACX,CAAC,CAAC;QACF1D,oBAAoB,CAAC,CAAC;MACxB,CAAC,MAAM;QACL6E,KAAK,CAAC,UAAUtH,IAAI,CAACuH,OAAO,EAAE,CAAC;MACjC;IACF,CAAC,CAAC,OAAOpH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDmH,KAAK,CAAC,sCAAsC,CAAC;IAC/C;EACF,CAAC;EAED,MAAM1E,oBAAoB,GAAG3D,YAAY,CAAC8B,MAAM,CAAC8B,GAAG,IAClD,IAAIC,IAAI,CAACD,GAAG,CAACE,eAAe,CAAC,GAAG,IAAID,IAAI,CAAC,CAC3C,CAAC;EAED,MAAM0E,gBAAgB,GAAGvI,YAAY,CAAC8B,MAAM,CAAC8B,GAAG,IAC9C,IAAIC,IAAI,CAACD,GAAG,CAACE,eAAe,CAAC,IAAI,IAAID,IAAI,CAAC,CAC5C,CAAC;EAED,oBACErE,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB7C,OAAA;MAAK4C,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxE7C,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD7C,OAAA;UAAI4C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvExD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMwD,kBAAkB,CAAC,IAAI,CAAE;UACxCtE,SAAS,EAAC,0OAA0O;UAAAC,QAAA,gBAEpP7C,OAAA;YAAK4C,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5F7C,OAAA;cAAMkD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,uBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxD,OAAA;QAAK4C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB7C,OAAA;UAAI4C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClFW,oBAAoB,CAACR,MAAM,GAAG,CAAC,gBAC9B3D,OAAA;UAAK4C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDsB,oBAAoB,CAACjC,GAAG,CAAC,CAAC6C,WAAW,EAAEL,KAAK,kBAC3C1E,OAAA;YAAiB4C,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAC3G7C,OAAA;cAAK4C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7C,OAAA;gBAAK4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC7C,OAAA;kBAAK4C,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,eACjF7C,OAAA;oBAAK4C,SAAS,EAAC,wBAAwB;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC3G7C,OAAA;sBAAMkD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAqE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxD,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAI4C,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,GAAC,MAAI,EAACkC,WAAW,CAACC,UAAU;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzExD,OAAA;oBAAG4C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEkC,WAAW,CAACiE;kBAAS;oBAAA3F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAM4C,SAAS,EAAE,8CACfmC,WAAW,CAACD,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAClEC,WAAW,CAACD,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAClE,yBAAyB,EACxB;gBAAAjC,QAAA,EACAkC,WAAW,CAACD;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNxD,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7C,OAAA;gBAAG4C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC,IAAIa,IAAI,CAACU,WAAW,CAACT,eAAe,CAAC,CAACO,kBAAkB,CAAC,CAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClIxD,OAAA;gBAAG4C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACuB,WAAW,CAACyC,eAAe;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnGxD,OAAA;gBAAG4C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACuB,WAAW,CAAC0C,MAAM;cAAA;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA,GAzBEkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENxD,OAAA;UAAK4C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD7C,OAAA;YAAG4C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxD,OAAA;QAAA6C,QAAA,gBACE7C,OAAA;UAAI4C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9EuF,gBAAgB,CAACpF,MAAM,GAAG,CAAC,gBAC1B3D,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBkG,gBAAgB,CAAC7G,GAAG,CAAC,CAAC6C,WAAW,EAAEL,KAAK,kBACvC1E,OAAA;YAAiB4C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBACtF7C,OAAA;cAAK4C,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC7C,OAAA;gBAAK4C,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChF7C,OAAA;kBAAK4C,SAAS,EAAC,uBAAuB;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC1G7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAG4C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GAAC,MAAI,EAACkC,WAAW,CAACC,UAAU;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3ExD,OAAA;kBAAG4C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAE,IAAIwB,IAAI,CAACU,WAAW,CAACT,eAAe,CAAC,CAACO,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAACE,WAAW,CAACyC,eAAe;gBAAA;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAM4C,SAAS,EAAE,8CACfmC,WAAW,CAACD,MAAM,KAAK,WAAW,GAAG,2BAA2B,GAChEC,WAAW,CAACD,MAAM,KAAK,WAAW,GAAG,yBAAyB,GAC9D,2BAA2B,EAC1B;cAAAjC,QAAA,EACAkC,WAAW,CAACD;YAAM;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA,GAlBCkB,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENxD,OAAA;UAAK4C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD7C,OAAA;YAAG4C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLyD,eAAe,iBACdjH,OAAA;MAAK4C,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F7C,OAAA;QAAK4C,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F7C,OAAA;UAAK4C,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB7C,OAAA;YAAK4C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF7C,OAAA;cAAI4C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzExD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMwD,kBAAkB,CAAC,KAAK,CAAE;cACzCtE,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENxD,OAAA;YAAMiJ,QAAQ,EAAEhB,YAAa;YAACrF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjD7C,OAAA;cAAK4C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvFxD,OAAA;kBACEyD,IAAI,EAAC,UAAU;kBACfqE,KAAK,EAAET,QAAQ,CAACE,QAAS;kBACzB2B,QAAQ,EAAEtB,iBAAkB;kBAC5BuB,QAAQ;kBACRvG,SAAS,EAAC,8GAA8G;kBAAAC,QAAA,gBAExH7C,OAAA;oBAAQ8H,KAAK,EAAC,EAAE;oBAAAjF,QAAA,EAAC;kBAAe;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxC2D,OAAO,CAACjF,GAAG,CAAEkH,MAAM,iBAClBpJ,OAAA;oBAAwB8H,KAAK,EAAEsB,MAAM,CAACnH,EAAG;oBAAAY,QAAA,GAAC,MACpC,EAACuG,MAAM,CAACC,SAAS,EAAC,GAAC,EAACD,MAAM,CAACE,QAAQ,EAAC,KAAG,EAACF,MAAM,CAACJ,SAAS;kBAAA,GADjDI,MAAM,CAACnH,EAAE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrFxD,OAAA;kBACEyD,IAAI,EAAC,SAAS;kBACdqE,KAAK,EAAET,QAAQ,CAACK,OAAQ;kBACxBwB,QAAQ,EAAEtB,iBAAkB;kBAC5BhF,SAAS,EAAC,8GAA8G;kBAAAC,QAAA,gBAExH7C,OAAA;oBAAQ8H,KAAK,EAAC,QAAQ;oBAAAjF,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxD,OAAA;oBAAQ8H,KAAK,EAAC,QAAQ;oBAAAjF,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxD,OAAA;oBAAQ8H,KAAK,EAAC,WAAW;oBAAAjF,QAAA,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFxD,OAAA;kBACE4F,IAAI,EAAC,MAAM;kBACXnC,IAAI,EAAC,iBAAiB;kBACtBqE,KAAK,EAAET,QAAQ,CAAC/C,eAAgB;kBAChC4E,QAAQ,EAAEtB,iBAAkB;kBAC5B2B,GAAG,EAAE,IAAIlF,IAAI,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;kBAC5C4C,QAAQ;kBACRvG,SAAS,EAAC;gBAA8G;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAO4C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFxD,OAAA;kBACE4F,IAAI,EAAC,MAAM;kBACXnC,IAAI,EAAC,iBAAiB;kBACtBqE,KAAK,EAAET,QAAQ,CAACG,eAAgB;kBAChC0B,QAAQ,EAAEtB,iBAAkB;kBAC5BuB,QAAQ;kBACRvG,SAAS,EAAC;gBAA8G;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAO4C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1FxD,OAAA;gBACEyD,IAAI,EAAC,QAAQ;gBACbqE,KAAK,EAAET,QAAQ,CAACI,MAAO;gBACvByB,QAAQ,EAAEtB,iBAAkB;gBAC5B4B,IAAI,EAAC,GAAG;gBACRL,QAAQ;gBACRM,WAAW,EAAC,gEAAgE;gBAC5E7G,SAAS,EAAC;cAA8G;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxD,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7C,OAAA;gBACE4F,IAAI,EAAC,QAAQ;gBACbhD,SAAS,EAAC,oLAAoL;gBAAAC,QAAA,gBAE9L7C,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAwF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC,kBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxD,OAAA;gBACE4F,IAAI,EAAC,QAAQ;gBACblC,OAAO,EAAEA,CAAA,KAAMwD,kBAAkB,CAAC,KAAK,CAAE;gBACzCtE,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAC3G;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAwD,GAAA,CApSMjD,eAAe;AAAA2F,GAAA,GAAf3F,eAAe;AAqSrB,MAAME,WAAW,GAAGA,CAAC;EAAEvD,SAAS;EAAEE,IAAI;EAAEgB,SAAS;EAAEQ,cAAc;EAAEG,kBAAkB;EAAEC;AAAU,CAAC,KAAK;EAAAmH,GAAA;EACrG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjK,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkK,cAAc,EAAEC,iBAAiB,CAAC,GAAGnK,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoK,QAAQ,EAAEC,WAAW,CAAC,GAAGrK,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAMsK,iBAAiB,GAAGxJ,SAAS,CAAC4B,MAAM,CAACT,QAAQ,IAAI;IAAA,IAAAsI,qBAAA;IACrD,MAAMC,aAAa,GAAGvI,QAAQ,CAAC4B,IAAI,CAAC4G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,WAAW,CAACS,WAAW,CAAC,CAAC,CAAC,MAAAF,qBAAA,GAChEtI,QAAQ,CAAC0I,WAAW,cAAAJ,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,WAAW,CAACS,WAAW,CAAC,CAAC,CAAC;IAC5F,MAAMG,eAAe,GAAGV,cAAc,KAAK,KAAK,IAAIjI,QAAQ,CAAC4I,QAAQ,KAAKX,cAAc;IACxF,OAAOM,aAAa,IAAII,eAAe;EACzC,CAAC,CAAC;;EAEF;EACA,MAAME,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,IAAIC,GAAG,CAACjK,SAAS,CAACwB,GAAG,CAAC0I,GAAG,IAAIA,GAAG,CAACH,QAAQ,CAAC,CAACnI,MAAM,CAACuI,OAAO,CAAC,CAAC,CAAC;EAE1F,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAIlK,IAAI,CAAC+C,MAAM,KAAK,CAAC,EAAE;MACrBkF,KAAK,CAAC,oBAAoB,CAAC;MAC3B;IACF;IAEA,IAAI;MACF;MACAA,KAAK,CAAC,qCAAqCrG,SAAS,CAACuI,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;MACtE;MACAnK,IAAI,CAACoK,OAAO,CAAChJ,IAAI,IAAII,cAAc,CAACJ,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7CgI,WAAW,CAAC,KAAK,CAAC;IACpB,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CmH,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;EAED,oBACE7I,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB7C,OAAA;MAAK4C,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxE7C,OAAA;QAAK4C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD7C,OAAA;UAAI4C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrExD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMuG,WAAW,CAAC,IAAI,CAAE;UACjCrH,SAAS,EAAC,0OAA0O;UAAAC,QAAA,gBAEpP7C,OAAA;YAAK4C,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5F7C,OAAA;cAAMkD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAwI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7L,CAAC,UACA,EAAC5C,IAAI,CAAC+C,MAAM,EAAC,GACrB;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNxD,OAAA;QAAK4C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD7C,OAAA;UAAK4C,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrB7C,OAAA;YAAK4C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB7C,OAAA;cACE4F,IAAI,EAAC,MAAM;cACX6D,WAAW,EAAC,qBAAqB;cACjC3B,KAAK,EAAE8B,WAAY;cACnBV,QAAQ,EAAGrB,CAAC,IAAKgC,cAAc,CAAChC,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;cAChDlF,SAAS,EAAC;YAAoH;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC,eACFxD,OAAA;cAAK4C,SAAS,EAAC,+CAA+C;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAClI7C,OAAA;gBAAMkD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAoD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxD,OAAA;UAAA6C,QAAA,eACE7C,OAAA;YACE8H,KAAK,EAAEgC,cAAe;YACtBZ,QAAQ,EAAGrB,CAAC,IAAKkC,iBAAiB,CAAClC,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;YACnDlF,SAAS,EAAC,uGAAuG;YAAAC,QAAA,EAEhH6H,UAAU,CAACxI,GAAG,CAACuI,QAAQ,iBACtBzK,OAAA;cAAuB8H,KAAK,EAAE2C,QAAS;cAAA5H,QAAA,EAAE4H;YAAQ,GAApCA,QAAQ;cAAApH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL0G,iBAAiB,CAACvG,MAAM,GAAG,CAAC,gBAC3B3D,OAAA;QAAK4C,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFqH,iBAAiB,CAAChI,GAAG,CAAEL,QAAQ,iBAC9B7B,OAAA;UAAuB4C,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAC9J7C,OAAA;YAAK4C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD7C,OAAA;cAAK4C,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC7C,OAAA;gBAAK4C,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,eACjF7C,OAAA;kBAAK4C,SAAS,EAAC,wBAAwB;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC3G7C,OAAA;oBAAMkD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5T;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAI4C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAEhB,QAAQ,CAAC4B;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5DxD,OAAA;kBAAG4C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEhB,QAAQ,CAAC4I;gBAAQ;kBAAApH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAM4C,SAAS,EAAE,8CACff,QAAQ,CAACoJ,KAAK,GAAG,EAAE,GAAG,6BAA6B,GACnDpJ,QAAQ,CAACoJ,KAAK,GAAG,CAAC,GAAG,+BAA+B,GACpD,yBAAyB,EACxB;cAAApI,QAAA,EACAhB,QAAQ,CAACoJ,KAAK,GAAG,CAAC,GAAG,GAAGpJ,QAAQ,CAACoJ,KAAK,WAAW,GAAG;YAAc;cAAA5H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENxD,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7C,OAAA;cAAG4C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEhB,QAAQ,CAAC0I;YAAW;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DxD,OAAA;cAAG4C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAEhB,QAAQ,CAACc,KAAK,EAAC,MAAI;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACvE3B,QAAQ,CAACqJ,MAAM,iBAAIlL,OAAA;cAAG4C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UAAQ,EAAChB,QAAQ,CAACqJ,MAAM;YAAA;cAAA7H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAENxD,OAAA;YACE0D,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAACC,QAAQ,CAAE;YACnCsJ,QAAQ,EAAEtJ,QAAQ,CAACoJ,KAAK,KAAK,CAAE;YAC/BrI,SAAS,EAAE,6DACTf,QAAQ,CAACoJ,KAAK,GAAG,CAAC,GACd,4CAA4C,GAC5C,8CAA8C,EACjD;YAAApI,QAAA,EAEFhB,QAAQ,CAACoJ,KAAK,GAAG,CAAC,GAAG,aAAa,GAAG;UAAc;YAAA5H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA,GAtCD3B,QAAQ,CAACI,EAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuChB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENxD,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UAAK4C,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/F7C,OAAA;YAAK4C,SAAS,EAAC,yBAAyB;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5G7C,OAAA;cAAMkD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAuQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5T;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxD,OAAA;UAAI4C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFxD,OAAA;UAAG4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLwG,QAAQ,iBACPhK,OAAA;MAAK4C,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F7C,OAAA;QAAK4C,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F7C,OAAA;UAAK4C,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB7C,OAAA;YAAK4C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF7C,OAAA;cAAI4C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnExD,OAAA;cACE0D,OAAO,EAAEA,CAAA,KAAMuG,WAAW,CAAC,KAAK,CAAE;cAClCrH,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL5C,IAAI,CAAC+C,MAAM,GAAG,CAAC,gBACd3D,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvBjC,IAAI,CAACsB,GAAG,CAAEF,IAAI,iBACbhC,OAAA;cAAmB4C,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACxF7C,OAAA;gBAAK4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC7C,OAAA;kBAAK4C,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,eACjF7C,OAAA;oBAAK4C,SAAS,EAAC,wBAAwB;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC3G7C,OAAA;sBAAMkD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAuQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5T;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxD,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAI4C,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEb,IAAI,CAACyB;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DxD,OAAA;oBAAG4C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAEb,IAAI,CAACW,KAAK,EAAC,WAAS;kBAAA;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAK4C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC7C,OAAA;kBAAK4C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC7C,OAAA;oBACE0D,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,IAAI,CAACC,EAAE,EAAED,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAE;oBAC9DS,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,EAC9F;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxD,OAAA;oBAAM4C,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEb,IAAI,CAACG;kBAAQ;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpExD,OAAA;oBACE0D,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,IAAI,CAACC,EAAE,EAAED,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAE;oBAC9DS,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,EAC9F;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNxD,OAAA;kBAAG4C,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAE,CAACb,IAAI,CAACW,KAAK,GAAGX,IAAI,CAACG,QAAQ,EAAE4I,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzGxD,OAAA;kBACE0D,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAACJ,IAAI,CAACC,EAAE,CAAE;kBACvCW,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAE/C7C,OAAA;oBAAK4C,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC5F7C,OAAA;sBAAMkD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA8H;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GArCExB,IAAI,CAACC,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCZ,CACN,CAAC,eAEFxD,OAAA;cAAK4C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7C,OAAA;gBAAK4C,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D7C,OAAA;kBAAA6C,QAAA,EAAM;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBxD,OAAA;kBAAM4C,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAEL,SAAS,CAACuI,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNxD,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7C,OAAA;kBACE0D,OAAO,EAAEoH,cAAe;kBACxBlI,SAAS,EAAC,mKAAmK;kBAAAC,QAAA,EAC9K;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxD,OAAA;kBACE0D,OAAO,EAAEA,CAAA,KAAMuG,WAAW,CAAC,KAAK,CAAE;kBAClCrH,SAAS,EAAC,gGAAgG;kBAAAC,QAAA,EAC3G;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENxD,OAAA;YAAK4C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7C,OAAA;cAAK4C,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F7C,OAAA;gBAAK4C,SAAS,EAAC,yBAAyB;gBAACE,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAJ,QAAA,eAC5G7C,OAAA;kBAAMkD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAwI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAI4C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFxD,OAAA;cAAG4C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAkC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACmG,GAAA,CA3OI1F,WAAW;AAAAmH,GAAA,GAAXnH,WAAW;AA6OjB,eAAehE,aAAa;AAAC,IAAAiE,EAAA,EAAAe,GAAA,EAAA8B,GAAA,EAAA2C,GAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
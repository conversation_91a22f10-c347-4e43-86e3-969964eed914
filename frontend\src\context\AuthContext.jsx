import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Remove session timeout - sessions should only end when browser closes
  // const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // Disabled

  // Check for existing authentication on app load
  useEffect(() => {
    const checkAuth = () => {
      try {
        // Use sessionStorage for session-based authentication
        // This will clear when browser/tab is closed but persist on refresh
        const token = sessionStorage.getItem('authToken');
        const userData = sessionStorage.getItem('userData');
        const loginTime = sessionStorage.getItem('loginTime');

        if (token && userData && loginTime) {
          // No timeout check - session persists until browser closes
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          console.log('Session restored from sessionStorage:', parsedUser.name);
        } else {
          console.log('No valid session found');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // Clear invalid data
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('userData');
        sessionStorage.removeItem('loginTime');
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Session management - sessionStorage automatically clears when browser/tab closes
    // sessionStorage will persist across page refreshes but clear when tab/browser closes

    // Add debugging to track session behavior
    const handleBeforeUnload = (event) => {
      // This fires on both refresh and browser close
      // We don't want to clear session on refresh, only on browser close
      // sessionStorage will handle this automatically, but let's log for debugging
      console.log('Page unloading - session will persist if it\'s a refresh, clear if browser closes');
    };

    // Add event listener for debugging
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Login function
  const login = (userData) => {
    setUser(userData);
    // Store authentication data in sessionStorage for session-based auth
    const loginTime = new Date().getTime();
    sessionStorage.setItem('authToken', userData.token || 'authenticated');
    sessionStorage.setItem('userData', JSON.stringify(userData));
    sessionStorage.setItem('loginTime', loginTime.toString());
    console.log('User logged in and session stored:', userData.name);
    console.log('Session will persist until browser/tab is closed');
  };

  // Logout function
  const logout = (redirectCallback) => {
    // Clear session storage for session-based authentication
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('loginTime');
    setUser(null);
    console.log('User logged out and session cleared');

    // Call redirect callback if provided
    if (redirectCallback && typeof redirectCallback === 'function') {
      redirectCallback();
    }
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return user && user.role === role;
  };

  // Check if user is admin
  const isAdmin = () => {
    return user && (user.role === 'admin' || user.userType === 'admin');
  };

  // Check if user is staff (doctor/nurse)
  const isStaff = () => {
    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');
  };

  const value = {
    user,
    login,
    logout,
    isAuthenticated,
    hasRole,
    isAdmin,
    isStaff,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;

import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Session timeout (optional - 8 hours)
  const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

  // Check for existing authentication on app load
  useEffect(() => {
    const checkAuth = () => {
      try {
        // Use sessionStorage for session-based authentication
        // This will clear when browser/tab is closed but persist on refresh
        const token = sessionStorage.getItem('authToken');
        const userData = sessionStorage.getItem('userData');
        const loginTime = sessionStorage.getItem('loginTime');

        if (token && userData && loginTime) {
          const currentTime = new Date().getTime();
          const sessionAge = currentTime - parseInt(loginTime);

          // Check if session has expired (optional timeout check)
          if (sessionAge > SESSION_TIMEOUT) {
            console.log('Session expired, logging out...');
            sessionStorage.clear();
            setUser(null);
          } else {
            const parsedUser = JSON.parse(userData);
            setUser(parsedUser);
          }
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        // Clear invalid data
        sessionStorage.removeItem('authToken');
        sessionStorage.removeItem('userData');
        sessionStorage.removeItem('loginTime');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Add session management for browser close detection
    const handleBeforeUnload = (event) => {
      // This will clear session when browser/tab is closed
      // Note: sessionStorage automatically clears on browser close,
      // but this ensures immediate cleanup
      if (event.type === 'beforeunload') {
        sessionStorage.clear();
      }
    };

    // Add event listener for browser close
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup event listener
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Login function
  const login = (userData) => {
    setUser(userData);
    // Store authentication data in sessionStorage for session-based auth
    const loginTime = new Date().getTime();
    sessionStorage.setItem('authToken', userData.token || 'authenticated');
    sessionStorage.setItem('userData', JSON.stringify(userData));
    sessionStorage.setItem('loginTime', loginTime.toString());
    console.log('User logged in:', userData);
  };

  // Logout function
  const logout = (redirectCallback) => {
    // Clear session storage for session-based authentication
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('loginTime');
    setUser(null);

    // Call redirect callback if provided
    if (redirectCallback && typeof redirectCallback === 'function') {
      redirectCallback();
    }
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return user && user.role === role;
  };

  // Check if user is admin
  const isAdmin = () => {
    return user && (user.role === 'admin' || user.userType === 'admin');
  };

  // Check if user is staff (doctor/nurse)
  const isStaff = () => {
    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');
  };

  const value = {
    user,
    login,
    logout,
    isAuthenticated,
    hasRole,
    isAdmin,
    isStaff,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;

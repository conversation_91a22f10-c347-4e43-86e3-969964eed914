import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

function AdminDashboard() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showAddDoctorModal, setShowAddDoctorModal] = useState(false);
  
  // System overview stats
  const [systemStats, setSystemStats] = useState({
    patients: 0,
    doctors: 0,
    users: 0,
    appointments: 0,
    rooms: 0,
    exams: 0,
    messages: 0,
    transfers: 0
  });

  // Add user form state
  const [userForm, setUserForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: 'staff',
    userType: 'staff',
    department: '',
    specialization: '',
    phone: ''
  });

  // Add doctor form state
  const [doctorForm, setDoctorForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    specialization: '',
    department: '',
    licenseNumber: '',
    experience: '',
    education: '',
    consultationFee: ''
  });

  // SMS testing state
  const [showSMSTestModal, setShowSMSTestModal] = useState(false);
  const [smsTestForm, setSmsTestForm] = useState({
    phoneNumber: '',
    patientName: '',
    subject: '',
    message: ''
  });
  const [smsStatus, setSmsStatus] = useState(null);

  // Appointment management state
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);
  const [appointments, setAppointments] = useState([]);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [appointmentFilter, setAppointmentFilter] = useState('all');
  const [appointmentAction, setAppointmentAction] = useState(null);

  // Hospital transfer management state
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [transfers, setTransfers] = useState([]);
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [transferFilter, setTransferFilter] = useState('all');
  const [transferAction, setTransferAction] = useState(null);

  // Utility management state
  const [showUtilityModal, setShowUtilityModal] = useState(false);
  const [utilities, setUtilities] = useState([]);
  const [selectedUtility, setSelectedUtility] = useState(null);
  const [utilityFilter, setUtilityFilter] = useState('all');
  const [utilityAction, setUtilityAction] = useState(null);

  // Stock management state
  const [showStockInModal, setShowStockInModal] = useState(false);
  const [showStockOutModal, setShowStockOutModal] = useState(false);
  const [stockInData, setStockInData] = useState({
    supplyId: '',
    quantity: '',
    unitCost: '',
    supplier: '',
    batchNumber: '',
    expiryDate: '',
    invoiceNumber: '',
    deliveryDate: '',
    receivedBy: '',
    notes: ''
  });
  const [stockOutData, setStockOutData] = useState({
    supplyId: '',
    quantity: '',
    reason: '',
    usedBy: '',
    department: '',
    patientId: '',
    notes: ''
  });

  // Expiry notifications state
  const [expiringSupplies, setExpiringSupplies] = useState([]);
  const [showExpiryNotifications, setShowExpiryNotifications] = useState(false);

  // Fetch system stats on component mount
  useEffect(() => {
    fetchSystemStats();
  }, []);

  // Fetch system overview statistics
  const fetchSystemStats = async () => {
    setLoading(true);
    try {
      // Fetch all system data in parallel
      const [
        patientsRes,
        doctorsRes,
        usersRes,
        appointmentsRes,
        roomsRes,
        examsRes,
        messagesRes,
        transfersRes
      ] = await Promise.all([
        fetch('http://localhost:5000/api/patients'),
        fetch('http://localhost:5000/api/doctors'),
        fetch('http://localhost:5000/api/users'),
        fetch('http://localhost:5000/api/appointments'),
        fetch('http://localhost:5000/api/rooms'),
        fetch('http://localhost:5000/api/exams'),
        fetch('http://localhost:5000/api/messages/inbox/1'),
        fetch('http://localhost:5000/api/hospital-transfers')
      ]);

      const [
        patientsData,
        doctorsData,
        usersData,
        appointmentsData,
        roomsData,
        examsData,
        messagesData,
        transfersData
      ] = await Promise.all([
        patientsRes.json(),
        doctorsRes.json(),
        usersRes.json(),
        appointmentsRes.json(),
        roomsRes.json(),
        examsRes.json(),
        messagesRes.json(),
        transfersRes.json()
      ]);

      setSystemStats({
        patients: patientsData.success ? patientsData.count || patientsData.data?.length || 0 : 0,
        doctors: doctorsData.success ? doctorsData.count || doctorsData.data?.length || 0 : 0,
        users: usersData.success ? usersData.count || usersData.data?.length || 0 : 0,
        appointments: appointmentsData.success ? appointmentsData.count || appointmentsData.data?.length || 0 : 0,
        rooms: roomsData.success ? roomsData.count || roomsData.data?.length || 0 : 0,
        exams: examsData.success ? examsData.count || examsData.data?.length || 0 : 0,
        messages: messagesData.success ? messagesData.count || messagesData.data?.length || 0 : 0,
        transfers: transfersData.success ? transfersData.count || transfersData.data?.length || 0 : 0
      });

    } catch (err) {
      console.error('Error fetching system stats:', err);
      setError('Error loading system statistics');
    } finally {
      setLoading(false);
    }
  };

  // Handle add user form changes
  const handleUserFormChange = (e) => {
    const { name, value } = e.target;
    setUserForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle add doctor form changes
  const handleDoctorFormChange = (e) => {
    const { name, value } = e.target;
    setDoctorForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Add new user
  const handleAddUser = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('http://localhost:5000/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userForm),
      });

      const data = await response.json();

      if (data.success) {
        alert('User added successfully!');
        setShowAddUserModal(false);
        setUserForm({
          firstName: '',
          lastName: '',
          email: '',
          password: '',
          role: 'staff',
          userType: 'staff',
          department: '',
          specialization: '',
          phone: ''
        });
        fetchSystemStats();
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (err) {
      console.error('Error adding user:', err);
      alert('Error adding user');
    } finally {
      setLoading(false);
    }
  };

  // Add new doctor
  const handleAddDoctor = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('http://localhost:5000/api/doctors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(doctorForm),
      });

      const data = await response.json();

      if (data.success) {
        alert('Doctor added successfully!');
        setShowAddDoctorModal(false);
        setDoctorForm({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          specialization: '',
          department: '',
          licenseNumber: '',
          experience: '',
          education: '',
          consultationFee: ''
        });
        fetchSystemStats();
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (err) {
      console.error('Error adding doctor:', err);
      alert('Error adding doctor');
    } finally {
      setLoading(false);
    }
  };

  // Handle SMS test form changes
  const handleSmsTestFormChange = (e) => {
    const { name, value } = e.target;
    setSmsTestForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Test SMS functionality
  const handleTestSMS = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('http://localhost:5000/api/sms/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber: smsTestForm.phoneNumber }),
      });

      const result = await response.json();
      setSmsStatus(result);

      if (result.success) {
        alert('Test SMS sent successfully!');
      } else {
        alert(`SMS test failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Error testing SMS:', error);
      alert('Error testing SMS service');
    } finally {
      setLoading(false);
    }
  };

  // Send custom SMS
  const handleSendCustomSMS = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('http://localhost:5000/api/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(smsTestForm),
      });

      const result = await response.json();
      setSmsStatus(result);

      if (result.success) {
        alert('Custom SMS sent successfully!');
        setSmsTestForm({
          phoneNumber: '',
          patientName: '',
          subject: '',
          message: ''
        });
      } else {
        alert(`SMS failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Error sending custom SMS:', error);
      alert('Error sending SMS');
    } finally {
      setLoading(false);
    }
  };

  // Check SMS service status
  const checkSMSStatus = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/sms/status');
      const result = await response.json();
      setSmsStatus(result);
    } catch (error) {
      console.error('Error checking SMS status:', error);
    }
  };

  // Check SMS status on component mount
  useEffect(() => {
    checkSMSStatus();
    fetchAppointments();
    fetchTransfers();
    fetchUtilities();
    fetchExpiringSupplies();
  }, []);

  // Fetch appointments
  const fetchAppointments = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/appointments');
      const result = await response.json();

      if (result.success) {
        setAppointments(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    }
  };

  // Handle appointment approval
  const handleAppointmentApproval = async (appointmentId, action, notes = '') => {
    setLoading(true);

    try {
      const endpoint = action === 'approve'
        ? `http://localhost:5000/api/appointments/${appointmentId}/approve`
        : `http://localhost:5000/api/appointments/${appointmentId}/reject`;

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          adminNotes: notes,
          processedBy: 'Admin User'
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(`Appointment ${action}d successfully!`);
        fetchAppointments(); // Refresh appointments
        fetchSystemStats(); // Refresh stats
        setSelectedAppointment(null);
        setAppointmentAction(null);
      } else {
        alert(`Failed to ${action} appointment: ${result.message}`);
      }
    } catch (error) {
      console.error(`Error ${action}ing appointment:`, error);
      alert(`Error ${action}ing appointment`);
    } finally {
      setLoading(false);
    }
  };

  // Filter appointments based on status
  const filteredAppointments = appointments.filter(appointment => {
    if (appointmentFilter === 'all') return true;
    return appointment.status === appointmentFilter;
  });

  // Get appointment counts by status
  const appointmentCounts = {
    total: appointments.length,
    scheduled: appointments.filter(a => a.status === 'scheduled').length,
    confirmed: appointments.filter(a => a.status === 'confirmed').length,
    completed: appointments.filter(a => a.status === 'completed').length,
    cancelled: appointments.filter(a => a.status === 'cancelled').length,
    pending: appointments.filter(a => a.status === 'scheduled' || a.status === 'confirmed').length
  };

  // Fetch hospital transfers
  const fetchTransfers = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/hospital-transfers');
      const result = await response.json();

      if (result.success) {
        setTransfers(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching hospital transfers:', error);
    }
  };

  // Handle hospital transfer approval
  const handleTransferApproval = async (transferId, action, notes = '') => {
    setLoading(true);

    try {
      const endpoint = action === 'approve'
        ? `http://localhost:5000/api/hospital-transfers/${transferId}/approve`
        : `http://localhost:5000/api/hospital-transfers/${transferId}/reject`;

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(
          action === 'approve'
            ? { approvedBy: 'Admin User', adminNotes: notes }
            : { rejectedBy: 'Admin User', rejectionReason: notes }
        ),
      });

      const result = await response.json();

      if (result.success) {
        alert(`Hospital transfer ${action}d successfully!`);
        fetchTransfers(); // Refresh transfers
        fetchSystemStats(); // Refresh stats
        setSelectedTransfer(null);
        setTransferAction(null);
      } else {
        alert(`Failed to ${action} hospital transfer: ${result.message}`);
      }
    } catch (error) {
      console.error(`Error ${action}ing hospital transfer:`, error);
      alert(`Error ${action}ing hospital transfer`);
    } finally {
      setLoading(false);
    }
  };

  // Filter transfers based on status
  const filteredTransfers = transfers.filter(transfer => {
    if (transferFilter === 'all') return true;
    return transfer.status === transferFilter;
  });

  // Get transfer counts by status
  const transferCounts = {
    total: transfers.length,
    pending: transfers.filter(t => t.status === 'pending').length,
    approved: transfers.filter(t => t.status === 'approved').length,
    rejected: transfers.filter(t => t.status === 'rejected').length,
    in_progress: transfers.filter(t => t.status === 'in_progress').length,
    completed: transfers.filter(t => t.status === 'completed').length,
    cancelled: transfers.filter(t => t.status === 'cancelled').length
  };

  // Fetch utilities
  const fetchUtilities = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/utilities');
      const result = await response.json();

      if (result.success) {
        setUtilities(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching utilities:', error);
    }
  };

  // Fetch expiring supplies
  const fetchExpiringSupplies = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/supplies/expiring?days=30');
      const data = await response.json();

      if (data.success) {
        setExpiringSupplies(data.data.all);
      }
    } catch (error) {
      console.error('Error fetching expiring supplies:', error);
    }
  };

  // Handle stock in submission
  const handleStockInSubmit = async () => {
    if (!stockInData.supplyId || !stockInData.quantity || stockInData.quantity <= 0) {
      alert('Please select a supply and enter a valid quantity');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/stock/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(stockInData),
      });

      const data = await response.json();

      if (data.success) {
        alert(data.message);
        // Reset form
        setStockInData({
          supplyId: '',
          quantity: '',
          unitCost: '',
          supplier: '',
          batchNumber: '',
          expiryDate: '',
          invoiceNumber: '',
          deliveryDate: '',
          receivedBy: '',
          notes: ''
        });
        setShowStockInModal(false);
        // Refresh utilities data
        fetchUtilities();
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error adding stock:', error);
      alert('Error adding stock. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle stock out submission
  const handleStockOutSubmit = async () => {
    if (!stockOutData.supplyId || !stockOutData.quantity || stockOutData.quantity <= 0) {
      alert('Please select a supply and enter a valid quantity');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/stock/remove', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(stockOutData),
      });

      const data = await response.json();

      if (data.success) {
        alert(data.message);
        // Reset form
        setStockOutData({
          supplyId: '',
          quantity: '',
          reason: '',
          usedBy: '',
          department: '',
          patientId: '',
          notes: ''
        });
        setShowStockOutModal(false);
        // Refresh utilities data
        fetchUtilities();
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error removing stock:', error);
      alert('Error removing stock. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle utility status update
  const handleUtilityStatusUpdate = async (utilityId, status, notes = '') => {
    setLoading(true);

    try {
      const response = await fetch(`http://localhost:5000/api/utilities/${utilityId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: status,
          notes: notes,
          updatedBy: 'Admin User'
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(`Utility status updated to ${status} successfully!`);
        fetchUtilities(); // Refresh utilities
        fetchSystemStats(); // Refresh stats
        setSelectedUtility(null);
        setUtilityAction(null);
      } else {
        alert(`Failed to update utility status: ${result.message}`);
      }
    } catch (error) {
      console.error('Error updating utility status:', error);
      alert('Error updating utility status');
    } finally {
      setLoading(false);
    }
  };

  // Filter utilities based on type/status
  const filteredUtilities = utilities.filter(utility => {
    if (utilityFilter === 'all') return true;
    if (utilityFilter === 'maintenance_due') {
      if (!utility.expiryDate) return false;
      const expiryDate = new Date(utility.expiryDate);
      const today = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
      return daysUntilExpiry <= 30 && daysUntilExpiry > 0; // Expiring within 30 days
    }
    return utility.supplyType === utilityFilter || utility.status === utilityFilter;
  });

  // Get utility counts by status and type
  const utilityCounts = {
    total: utilities.length,
    operational: utilities.filter(u => u.status === 'in_stock').length,
    maintenance: utilities.filter(u => u.status === 'low_stock').length,
    offline: utilities.filter(u => u.status === 'out_of_stock').length,
    emergency: utilities.filter(u => u.status === 'expired').length,
    critical: utilities.filter(u => u.priority === 'critical').length,
    maintenance_due: utilities.filter(u => {
      if (!u.expiryDate) return false;
      const expiryDate = new Date(u.expiryDate);
      const today = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
      return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
    }).length
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 shadow-2xl">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="w-20 h-20 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-xl border border-white/30">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">
                  Admin Dashboard
                </h1>
                <p className="text-blue-100 text-lg">Comprehensive healthcare system management & analytics</p>
              </div>
            </div>
            <div className="flex flex-col lg:flex-row items-end lg:items-center gap-4">
              <div className="bg-white/20 backdrop-blur-md text-white px-6 py-3 rounded-xl flex-shrink-0 border border-white/30 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-lg">👨‍💼</span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-blue-100">Administrator</div>
                    <div className="text-white font-semibold">{user?.name || 'Admin User'}</div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Quick Action Panel */}
      <div className="max-w-7xl mx-auto px-4 -mt-8 relative z-10">
        <div className="bg-white rounded-2xl shadow-2xl border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
              <p className="text-gray-600">Manage your healthcare system efficiently</p>
            </div>
            <button
              onClick={fetchSystemStats}
              disabled={loading}
              className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-xl transition-all transform hover:scale-105 flex items-center gap-2 shadow-lg"
            >
              <svg className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {loading ? 'Refreshing...' : 'Refresh Data'}
            </button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {/* User Management */}
            <button
              onClick={() => setShowAddUserModal(true)}
              className="bg-gradient-to-br from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Add User</span>
              </div>
            </button>

            <button
              onClick={() => setShowAddDoctorModal(true)}
              className="bg-gradient-to-br from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Add Doctor</span>
              </div>
            </button>

            <button
              onClick={() => setShowAppointmentModal(true)}
              className="bg-gradient-to-br from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Appointments</span>
              </div>
            </button>

            <button
              onClick={() => setShowTransferModal(true)}
              className="bg-gradient-to-br from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Transfers</span>
              </div>
            </button>

            <button
              onClick={() => setShowUtilityModal(true)}
              className="bg-gradient-to-br from-teal-500 to-green-600 hover:from-teal-600 hover:to-green-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Supplies</span>
              </div>
            </button>

            <button
              onClick={() => setShowStockInModal(true)}
              className="bg-gradient-to-br from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Stock In</span>
              </div>
            </button>

            <button
              onClick={() => setShowStockOutModal(true)}
              className="bg-gradient-to-br from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Stock Out</span>
              </div>
            </button>

            <button
              onClick={() => setShowExpiryNotifications(true)}
              className="bg-gradient-to-br from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group relative"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">Expiry Alerts</span>
              </div>
              {expiringSupplies.length > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg">
                  {expiringSupplies.length}
                </span>
              )}
            </button>

            <button
              onClick={() => setShowSMSTestModal(true)}
              className="bg-gradient-to-br from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white p-4 rounded-xl transition-all transform hover:scale-105 shadow-lg hover:shadow-xl group"
            >
              <div className="flex flex-col items-center gap-2">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:bg-white/30 transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </div>
                <span className="text-sm font-semibold">SMS Test</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-700 font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* Enhanced System Overview Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6 mb-8">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.patients}</div>
              <div className="text-blue-100 text-sm font-medium">Patients</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.doctors}</div>
              <div className="text-green-100 text-sm font-medium">Doctors</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.users}</div>
              <div className="text-purple-100 text-sm font-medium">Users</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.appointments}</div>
              <div className="text-yellow-100 text-sm font-medium">Appointments</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.rooms}</div>
              <div className="text-indigo-100 text-sm font-medium">Rooms</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.exams}</div>
              <div className="text-red-100 text-sm font-medium">Exams</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.messages}</div>
              <div className="text-pink-100 text-sm font-medium">Messages</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-all transform hover:scale-105 text-white">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center mx-auto mb-3 border border-white/30">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
              </div>
              <div className="text-3xl font-bold mb-1">{systemStats.transfers}</div>
              <div className="text-orange-100 text-sm font-medium">Transfers</div>
            </div>
          </div>
        </div>

        {/* Enhanced System Management Section */}
        <div className="bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-2xl border border-gray-200 p-8 mb-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Healthcare System Analytics
                </h2>
                <p className="text-gray-600 text-lg">
                  Real-time insights and comprehensive system management
                </p>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-16">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200"></div>
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
              </div>
              <span className="ml-4 text-gray-600 text-lg font-medium">Loading comprehensive system data...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-gradient-to-br from-blue-500 to-cyan-600 p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105 text-white">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-14 h-14 bg-white/20 backdrop-blur-md rounded-xl flex items-center justify-center border border-white/30">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">{systemStats.patients}</div>
                    <div className="text-blue-100 text-sm">Total</div>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2">Patient Management</h3>
                <p className="text-blue-100 text-sm">Comprehensive patient records and healthcare data management</p>
              </div>

              <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105 text-white">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-14 h-14 bg-white/20 backdrop-blur-md rounded-xl flex items-center justify-center border border-white/30">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">{systemStats.doctors}</div>
                    <div className="text-green-100 text-sm">Active</div>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2">Medical Staff</h3>
                <p className="text-green-100 text-sm">Professional healthcare providers and medical specialists</p>
              </div>

              <div className="bg-gradient-to-br from-purple-500 to-indigo-600 p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105 text-white">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-14 h-14 bg-white/20 backdrop-blur-md rounded-xl flex items-center justify-center border border-white/30">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">{systemStats.rooms}</div>
                    <div className="text-purple-100 text-sm">Available</div>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2">Infrastructure</h3>
                <p className="text-purple-100 text-sm">Hospital facilities, rooms, and medical equipment</p>
              </div>

              <div className="bg-gradient-to-br from-orange-500 to-red-500 p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105 text-white">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-14 h-14 bg-white/20 backdrop-blur-md rounded-xl flex items-center justify-center border border-white/30">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">{systemStats.appointments + systemStats.exams}</div>
                    <div className="text-orange-100 text-sm">Active</div>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-2">Operations</h3>
                <p className="text-orange-100 text-sm">Daily appointments, examinations, and medical procedures</p>
              </div>
            </div>
          )}
        </div>

        {/* Add User Modal */}
        {showAddUserModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">👤 Add New User</h3>
                  <button
                    onClick={() => setShowAddUserModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleAddUser} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                      <input
                        type="text"
                        name="firstName"
                        value={userForm.firstName}
                        onChange={handleUserFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                      <input
                        type="text"
                        name="lastName"
                        value={userForm.lastName}
                        onChange={handleUserFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                    <input
                      type="email"
                      name="email"
                      value={userForm.email}
                      onChange={handleUserFormChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                    <input
                      type="password"
                      name="password"
                      value={userForm.password}
                      onChange={handleUserFormChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Role *</label>
                      <select
                        name="role"
                        value={userForm.role}
                        onChange={handleUserFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="staff">Staff</option>
                        <option value="nurse">Nurse</option>
                        <option value="doctor">Doctor</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                      <input
                        type="tel"
                        name="phone"
                        value={userForm.phone}
                        onChange={handleUserFormChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                      <input
                        type="text"
                        name="department"
                        value={userForm.department}
                        onChange={handleUserFormChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                      <input
                        type="text"
                        name="specialization"
                        value={userForm.specialization}
                        onChange={handleUserFormChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowAddUserModal(false)}
                      className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                    >
                      {loading ? '🔄 Adding...' : '👤 Add User'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Add Doctor Modal */}
        {showAddDoctorModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">👨‍⚕️ Add New Doctor</h3>
                  <button
                    onClick={() => setShowAddDoctorModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleAddDoctor} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                      <input
                        type="text"
                        name="firstName"
                        value={doctorForm.firstName}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                      <input
                        type="text"
                        name="lastName"
                        value={doctorForm.lastName}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                      <input
                        type="email"
                        name="email"
                        value={doctorForm.email}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone *</label>
                      <input
                        type="tel"
                        name="phone"
                        value={doctorForm.phone}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Specialization *</label>
                      <input
                        type="text"
                        name="specialization"
                        value={doctorForm.specialization}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Department *</label>
                      <input
                        type="text"
                        name="department"
                        value={doctorForm.department}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">License Number *</label>
                      <input
                        type="text"
                        name="licenseNumber"
                        value={doctorForm.licenseNumber}
                        onChange={handleDoctorFormChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Experience (years)</label>
                      <input
                        type="number"
                        name="experience"
                        value={doctorForm.experience}
                        onChange={handleDoctorFormChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Education</label>
                    <textarea
                      name="education"
                      value={doctorForm.education}
                      onChange={handleDoctorFormChange}
                      rows="3"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Consultation Fee</label>
                    <input
                      type="number"
                      name="consultationFee"
                      value={doctorForm.consultationFee}
                      onChange={handleDoctorFormChange}
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowAddDoctorModal(false)}
                      className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                    >
                      {loading ? '🔄 Adding...' : '👨‍⚕️ Add Doctor'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* SMS Test Modal */}
        {showSMSTestModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">📱 SMS Service Testing</h3>
                  <button
                    onClick={() => setShowSMSTestModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* SMS Status */}
                {smsStatus && (
                  <div className={`p-4 rounded-lg mb-6 ${smsStatus.configured ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'}`}>
                    <div className="flex items-center">
                      <span className={`mr-2 ${smsStatus.configured ? 'text-green-600' : 'text-yellow-600'}`}>
                        {smsStatus.configured ? '✅' : '⚠️'}
                      </span>
                      <span className={`font-medium ${smsStatus.configured ? 'text-green-700' : 'text-yellow-700'}`}>
                        {smsStatus.message}
                      </span>
                    </div>
                  </div>
                )}

                {/* Test SMS Form */}
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick SMS Test</h4>
                    <form onSubmit={handleTestSMS} className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                        <input
                          type="tel"
                          name="phoneNumber"
                          value={smsTestForm.phoneNumber}
                          onChange={handleSmsTestFormChange}
                          required
                          placeholder="+**********"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                      <button
                        type="submit"
                        disabled={loading}
                        className="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                      >
                        {loading ? '🔄 Sending...' : '📱 Send Test SMS'}
                      </button>
                    </form>
                  </div>

                  <div className="border-t border-gray-200 pt-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Custom SMS Message</h4>
                    <form onSubmit={handleSendCustomSMS} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                          <input
                            type="tel"
                            name="phoneNumber"
                            value={smsTestForm.phoneNumber}
                            onChange={handleSmsTestFormChange}
                            required
                            placeholder="+**********"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Patient Name *</label>
                          <input
                            type="text"
                            name="patientName"
                            value={smsTestForm.patientName}
                            onChange={handleSmsTestFormChange}
                            required
                            placeholder="John Doe"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                        <input
                          type="text"
                          name="subject"
                          value={smsTestForm.subject}
                          onChange={handleSmsTestFormChange}
                          required
                          placeholder="Appointment Reminder"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                        <textarea
                          name="message"
                          value={smsTestForm.message}
                          onChange={handleSmsTestFormChange}
                          required
                          rows="4"
                          placeholder="Your custom message here..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>

                      <button
                        type="submit"
                        disabled={loading}
                        className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                      >
                        {loading ? '🔄 Sending...' : '📤 Send Custom SMS'}
                      </button>
                    </form>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h5 className="font-medium text-gray-900 mb-2">📋 SMS Configuration Instructions:</h5>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Create a Twilio account at <a href="https://console.twilio.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">console.twilio.com</a></li>
                      <li>• Get your Account SID and Auth Token from the dashboard</li>
                      <li>• Purchase a phone number for sending SMS</li>
                      <li>• Add credentials to backend/.env file</li>
                      <li>• Restart the backend server</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Appointment Management Modal */}
        {showAppointmentModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">📅 Appointment Management</h3>
                  <button
                    onClick={() => setShowAppointmentModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* Appointment Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-600">{appointmentCounts.total}</div>
                    <div className="text-sm text-blue-700">Total</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-yellow-600">{appointmentCounts.pending}</div>
                    <div className="text-sm text-yellow-700">Pending</div>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-orange-600">{appointmentCounts.scheduled}</div>
                    <div className="text-sm text-orange-700">Scheduled</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{appointmentCounts.confirmed}</div>
                    <div className="text-sm text-green-700">Confirmed</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-purple-600">{appointmentCounts.completed}</div>
                    <div className="text-sm text-purple-700">Completed</div>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-red-600">{appointmentCounts.cancelled}</div>
                    <div className="text-sm text-red-700">Cancelled</div>
                  </div>
                </div>

                {/* Filter Controls */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Status:</label>
                  <select
                    value={appointmentFilter}
                    onChange={(e) => setAppointmentFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Appointments</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                {/* Appointments List */}
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {filteredAppointments.length === 0 ? (
                    <div className="text-center py-8">
                      <span className="text-4xl mb-4 block">📅</span>
                      <h3 className="text-lg font-semibold text-gray-700 mb-2">No Appointments Found</h3>
                      <p className="text-gray-500">No appointments match the selected filter.</p>
                    </div>
                  ) : (
                    filteredAppointments.map((appointment) => (
                      <div key={appointment.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                              <div>
                                <h4 className="font-semibold text-gray-900">
                                  {appointment.patientFirstName} {appointment.patientLastName}
                                </h4>
                                <p className="text-sm text-gray-600">Patient</p>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">
                                  Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                                </p>
                                <p className="text-sm text-gray-600">{appointment.specialization}</p>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">
                                  {new Date(appointment.appointmentDate).toLocaleDateString()}
                                </p>
                                <p className="text-sm text-gray-600">{appointment.appointmentTime}</p>
                              </div>
                              <div>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  appointment.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800' :
                                  appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                  appointment.status === 'completed' ? 'bg-purple-100 text-purple-800' :
                                  appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                </span>
                              </div>
                            </div>

                            {appointment.reason && (
                              <div className="mt-2">
                                <p className="text-sm text-gray-700">
                                  <span className="font-medium">Reason:</span> {appointment.reason}
                                </p>
                              </div>
                            )}

                            {appointment.symptoms && (
                              <div className="mt-1">
                                <p className="text-sm text-gray-700">
                                  <span className="font-medium">Symptoms:</span> {appointment.symptoms}
                                </p>
                              </div>
                            )}
                          </div>

                          {/* Action Buttons */}
                          {(appointment.status === 'scheduled' || appointment.status === 'confirmed' || !appointment.status || appointment.status === 'pending') && (
                            <div className="flex space-x-2 ml-4">
                              <button
                                onClick={() => {
                                  setSelectedAppointment(appointment);
                                  setAppointmentAction('approve');
                                }}
                                className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ✅ Approve
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedAppointment(appointment);
                                  setAppointmentAction('reject');
                                }}
                                className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ❌ Reject
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hospital Transfer Management Modal */}
        {showTransferModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">🚑 Hospital Transfer Management</h3>
                  <button
                    onClick={() => setShowTransferModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* Transfer Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-600">{transferCounts.total}</div>
                    <div className="text-sm text-blue-700">Total</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-yellow-600">{transferCounts.pending}</div>
                    <div className="text-sm text-yellow-700">Pending</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{transferCounts.approved}</div>
                    <div className="text-sm text-green-700">Approved</div>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-red-600">{transferCounts.rejected}</div>
                    <div className="text-sm text-red-700">Rejected</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-purple-600">{transferCounts.in_progress}</div>
                    <div className="text-sm text-purple-700">In Progress</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-gray-600">{transferCounts.completed}</div>
                    <div className="text-sm text-gray-700">Completed</div>
                  </div>
                </div>

                {/* Filter Controls */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Status:</label>
                  <select
                    value={transferFilter}
                    onChange={(e) => setTransferFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Transfers</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                {/* Transfers List */}
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {filteredTransfers.length === 0 ? (
                    <div className="text-center py-8">
                      <span className="text-4xl mb-4 block">🚑</span>
                      <h3 className="text-lg font-semibold text-gray-700 mb-2">No Hospital Transfers Found</h3>
                      <p className="text-gray-500">No hospital transfers match the selected filter.</p>
                    </div>
                  ) : (
                    filteredTransfers.map((transfer) => (
                      <div key={transfer.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                              <div>
                                <h4 className="font-semibold text-gray-900">
                                  {transfer.patientFirstName} {transfer.patientLastName}
                                </h4>
                                <p className="text-sm text-gray-600">Patient</p>
                                <p className="text-xs text-gray-500">ID: {transfer.transferId}</p>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{transfer.currentHospital}</p>
                                <p className="text-sm text-gray-600">→ {transfer.targetHospital}</p>
                                <p className="text-xs text-gray-500">{transfer.transportMethod}</p>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">
                                  {transfer.preferredDate ? new Date(transfer.preferredDate).toLocaleDateString() : 'No date'}
                                </p>
                                <p className="text-sm text-gray-600">{transfer.preferredTime || 'No time'}</p>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  transfer.urgencyLevel === 'critical' ? 'bg-red-100 text-red-800' :
                                  transfer.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                                  transfer.urgencyLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                }`}>
                                  {transfer.urgencyLevel}
                                </span>
                              </div>
                              <div>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  transfer.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                  transfer.status === 'approved' ? 'bg-green-100 text-green-800' :
                                  transfer.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                  transfer.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                  transfer.status === 'completed' ? 'bg-purple-100 text-purple-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {transfer.status.charAt(0).toUpperCase() + transfer.status.slice(1)}
                                </span>
                              </div>
                            </div>

                            <div className="mt-2">
                              <p className="text-sm text-gray-700">
                                <span className="font-medium">Reason:</span> {transfer.transferReason}
                              </p>
                              <p className="text-sm text-gray-700">
                                <span className="font-medium">Condition:</span> {transfer.medicalCondition}
                              </p>
                              <p className="text-sm text-gray-700">
                                <span className="font-medium">Requested by:</span> {transfer.requestedBy} ({transfer.requestedByRole})
                              </p>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          {(transfer.status === 'pending' || !transfer.status) && (
                            <div className="flex space-x-2 ml-4">
                              <button
                                onClick={() => {
                                  setSelectedTransfer(transfer);
                                  setTransferAction('approve');
                                }}
                                className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ✅ Approve
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedTransfer(transfer);
                                  setTransferAction('reject');
                                }}
                                className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ❌ Reject
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Utility Management Modal */}
        {showUtilityModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-7xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">💊 Medical Supply Management</h3>
                  <button
                    onClick={() => setShowUtilityModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* Utility Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-blue-600">{utilityCounts.total}</div>
                    <div className="text-sm text-blue-700">Total</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-green-600">{utilityCounts.operational}</div>
                    <div className="text-sm text-green-700">In Stock</div>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-yellow-600">{utilityCounts.maintenance}</div>
                    <div className="text-sm text-yellow-700">Low Stock</div>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-red-600">{utilityCounts.offline}</div>
                    <div className="text-sm text-red-700">Out of Stock</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-purple-600">{utilityCounts.critical}</div>
                    <div className="text-sm text-purple-700">Critical</div>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg text-center">
                    <div className="text-2xl font-bold text-orange-600">{utilityCounts.maintenance_due}</div>
                    <div className="text-sm text-orange-700">Expiring Soon</div>
                  </div>
                </div>

                {/* Filter Controls */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Type/Status:</label>
                  <select
                    value={utilityFilter}
                    onChange={(e) => setUtilityFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
                  >
                    <option value="all">All Medical Supplies</option>
                    <option value="in_stock">In Stock</option>
                    <option value="low_stock">Low Stock</option>
                    <option value="out_of_stock">Out of Stock</option>
                    <option value="expired">Expired</option>
                    <option value="recalled">Recalled</option>
                    <option value="maintenance_due">Expiring Soon</option>
                    <optgroup label="By Supply Type">
                      <option value="medication">Medications</option>
                      <option value="oxygen_tanks">Oxygen Tanks</option>
                      <option value="surgical_supplies">Surgical Supplies</option>
                      <option value="ppe">PPE (Personal Protective Equipment)</option>
                      <option value="diagnostic_supplies">Diagnostic Supplies</option>
                      <option value="emergency_supplies">Emergency Supplies</option>
                      <option value="iv_fluids">IV Fluids</option>
                      <option value="blood_products">Blood Products</option>
                      <option value="medical_devices">Medical Devices</option>
                      <option value="other">Other</option>
                    </optgroup>
                  </select>
                </div>

                {/* Utilities List */}
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {filteredUtilities.length === 0 ? (
                    <div className="text-center py-8">
                      <span className="text-4xl mb-4 block">💊</span>
                      <h3 className="text-lg font-semibold text-gray-700 mb-2">No Medical Supplies Found</h3>
                      <p className="text-gray-500">No medical supplies match the selected filter.</p>
                    </div>
                  ) : (
                    filteredUtilities.map((utility) => (
                      <div key={utility.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                              <div>
                                <h4 className="font-semibold text-gray-900">{utility.supplyName}</h4>
                                <p className="text-sm text-gray-600 capitalize">{utility.supplyType.replace('_', ' ')}</p>
                                <p className="text-xs text-gray-500">ID: {utility.supplyId}</p>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{utility.location}</p>
                                <p className="text-sm text-gray-600">{utility.department || 'N/A'}</p>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  utility.priority === 'critical' ? 'bg-red-100 text-red-800' :
                                  utility.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                                  utility.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                }`}>
                                  {utility.priority}
                                </span>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">Stock: {utility.currentStock} {utility.unitOfMeasure}</p>
                                <p className="text-sm text-gray-600">Min: {utility.minimumStock} | Max: {utility.maximumStock}</p>
                                <p className="text-xs text-gray-500">Supplier: {utility.supplier || 'N/A'}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-700">
                                  <span className="font-medium">Expiry Date:</span>
                                </p>
                                <p className="text-sm text-gray-600">
                                  {utility.expiryDate ? new Date(utility.expiryDate).toLocaleDateString() : 'No expiry'}
                                </p>
                                {utility.expiryDate && (() => {
                                  const expiryDate = new Date(utility.expiryDate);
                                  const today = new Date();
                                  const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
                                  return (
                                    <span className={`text-xs ${
                                      daysUntilExpiry <= 0 ? 'text-red-600 font-semibold' :
                                      daysUntilExpiry <= 30 ? 'text-orange-600 font-semibold' :
                                      'text-gray-500'
                                    }`}>
                                      {daysUntilExpiry <= 0 ? 'Expired!' :
                                       daysUntilExpiry <= 30 ? `Expires in ${daysUntilExpiry} days` :
                                       `${daysUntilExpiry} days`}
                                    </span>
                                  );
                                })()}
                                <p className="text-xs text-gray-500 mt-1">Batch: {utility.batchNumber || 'N/A'}</p>
                              </div>
                              <div>
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  utility.status === 'in_stock' ? 'bg-green-100 text-green-800' :
                                  utility.status === 'low_stock' ? 'bg-yellow-100 text-yellow-800' :
                                  utility.status === 'out_of_stock' ? 'bg-red-100 text-red-800' :
                                  utility.status === 'expired' ? 'bg-red-100 text-red-800' :
                                  utility.status === 'recalled' ? 'bg-purple-100 text-purple-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {utility.status.replace('_', ' ').charAt(0).toUpperCase() + utility.status.replace('_', ' ').slice(1)}
                                </span>
                              </div>
                            </div>

                            {utility.description && (
                              <div className="mt-2">
                                <p className="text-sm text-gray-700">
                                  <span className="font-medium">Description:</span> {utility.description}
                                </p>
                              </div>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col space-y-2 ml-4">
                            {utility.status !== 'low_stock' && (
                              <button
                                onClick={() => {
                                  setSelectedUtility(utility);
                                  setUtilityAction('low_stock');
                                }}
                                className="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ⚠️ Low Stock
                              </button>
                            )}
                            {utility.status !== 'in_stock' && (
                              <button
                                onClick={() => {
                                  setSelectedUtility(utility);
                                  setUtilityAction('in_stock');
                                }}
                                className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ✅ In Stock
                              </button>
                            )}
                            {utility.status !== 'out_of_stock' && (
                              <button
                                onClick={() => {
                                  setSelectedUtility(utility);
                                  setUtilityAction('out_of_stock');
                                }}
                                className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                ❌ Out of Stock
                              </button>
                            )}
                            {utility.status !== 'expired' && (
                              <button
                                onClick={() => {
                                  setSelectedUtility(utility);
                                  setUtilityAction('expired');
                                }}
                                className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-sm transition-colors"
                              >
                                🚫 Expired
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Appointment Action Confirmation Modal */}
        {selectedAppointment && appointmentAction && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center" style={{ zIndex: 9999 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 border-4 border-blue-200">
              <div className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  {appointmentAction === 'approve' ? '✅ Approve Appointment' : '❌ Reject Appointment'}
                </h3>

                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Patient:</span> {selectedAppointment.patientFirstName} {selectedAppointment.patientLastName}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Doctor:</span> Dr. {selectedAppointment.doctorFirstName} {selectedAppointment.doctorLastName}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Date:</span> {new Date(selectedAppointment.appointmentDate).toLocaleDateString()} at {selectedAppointment.appointmentTime}
                  </p>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Notes {appointmentAction === 'reject' ? '(Required)' : '(Optional)'}:
                  </label>
                  <textarea
                    id="adminNotes"
                    rows="3"
                    placeholder={appointmentAction === 'approve' ? 'Optional notes for approval...' : 'Please provide reason for rejection...'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setSelectedAppointment(null);
                      setAppointmentAction(null);
                    }}
                    className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      const notes = document.getElementById('adminNotes').value;
                      if (appointmentAction === 'reject' && !notes.trim()) {
                        alert('Please provide a reason for rejection');
                        return;
                      }
                      handleAppointmentApproval(selectedAppointment.appointmentId, appointmentAction, notes);
                    }}
                    disabled={loading}
                    className={`flex-1 py-2 px-4 rounded-lg text-white transition-colors disabled:opacity-50 ${
                      appointmentAction === 'approve'
                        ? 'bg-green-500 hover:bg-green-600'
                        : 'bg-red-500 hover:bg-red-600'
                    }`}
                  >
                    {loading ? '🔄 Processing...' : (appointmentAction === 'approve' ? '✅ Approve' : '❌ Reject')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hospital Transfer Action Confirmation Modal */}
        {selectedTransfer && transferAction && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center" style={{ zIndex: 9999 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 border-4 border-orange-200">
              <div className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  {transferAction === 'approve' ? '✅ Approve Hospital Transfer' : '❌ Reject Hospital Transfer'}
                </h3>

                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Patient:</span> {selectedTransfer.patientFirstName} {selectedTransfer.patientLastName}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Transfer:</span> {selectedTransfer.currentHospital} → {selectedTransfer.targetHospital}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Urgency:</span> {selectedTransfer.urgencyLevel}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Reason:</span> {selectedTransfer.transferReason}
                  </p>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Notes {transferAction === 'reject' ? '(Required)' : '(Optional)'}:
                  </label>
                  <textarea
                    id="transferAdminNotes"
                    rows="3"
                    placeholder={transferAction === 'approve' ? 'Optional notes for approval...' : 'Please provide reason for rejection...'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setSelectedTransfer(null);
                      setTransferAction(null);
                    }}
                    className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      const notes = document.getElementById('transferAdminNotes').value;
                      if (transferAction === 'reject' && !notes.trim()) {
                        alert('Please provide a reason for rejection');
                        return;
                      }
                      handleTransferApproval(selectedTransfer.transferId, transferAction, notes);
                    }}
                    disabled={loading}
                    className={`flex-1 py-2 px-4 rounded-lg text-white transition-colors disabled:opacity-50 ${
                      transferAction === 'approve'
                        ? 'bg-green-500 hover:bg-green-600'
                        : 'bg-red-500 hover:bg-red-600'
                    }`}
                  >
                    {loading ? '🔄 Processing...' : (transferAction === 'approve' ? '✅ Approve' : '❌ Reject')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Utility Status Update Confirmation Modal */}
        {selectedUtility && utilityAction && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center" style={{ zIndex: 9999 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 border-4 border-teal-200">
              <div className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  💊 Update Medical Supply Status
                </h3>

                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Supply:</span> {selectedUtility.supplyName}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Type:</span> {selectedUtility.supplyType.replace('_', ' ')}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Location:</span> {selectedUtility.location}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Current Stock:</span> {selectedUtility.currentStock} {selectedUtility.unitOfMeasure}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Current Status:</span> {selectedUtility.status.replace('_', ' ')}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">New Status:</span>
                    <span className={`ml-1 font-semibold ${
                      utilityAction === 'in_stock' ? 'text-green-600' :
                      utilityAction === 'low_stock' ? 'text-yellow-600' :
                      utilityAction === 'out_of_stock' ? 'text-red-600' :
                      utilityAction === 'expired' ? 'text-purple-600' :
                      'text-gray-600'
                    }`}>
                      {utilityAction.replace('_', ' ').charAt(0).toUpperCase() + utilityAction.replace('_', ' ').slice(1)}
                    </span>
                  </p>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Optional):
                  </label>
                  <textarea
                    id="utilityStatusNotes"
                    rows="3"
                    placeholder="Add notes about this status change..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setSelectedUtility(null);
                      setUtilityAction(null);
                    }}
                    className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      const notes = document.getElementById('utilityStatusNotes').value;
                      handleUtilityStatusUpdate(selectedUtility.supplyId, utilityAction, notes);
                    }}
                    disabled={loading}
                    className={`flex-1 py-2 px-4 rounded-lg text-white transition-colors disabled:opacity-50 ${
                      utilityAction === 'in_stock' ? 'bg-green-500 hover:bg-green-600' :
                      utilityAction === 'low_stock' ? 'bg-yellow-500 hover:bg-yellow-600' :
                      utilityAction === 'out_of_stock' ? 'bg-red-500 hover:bg-red-600' :
                      utilityAction === 'expired' ? 'bg-purple-500 hover:bg-purple-600' :
                      'bg-teal-500 hover:bg-teal-600'
                    }`}
                  >
                    {loading ? '🔄 Updating...' : `💊 Update to ${utilityAction.replace('_', ' ').charAt(0).toUpperCase() + utilityAction.replace('_', ' ').slice(1)}`}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stock In Modal */}
        {showStockInModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">📦 Add Stock In</h3>
                  <button
                    onClick={() => setShowStockInModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Supply Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Medical Supply *
                    </label>
                    <select
                      value={stockInData.supplyId}
                      onChange={(e) => setStockInData({...stockInData, supplyId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      required
                    >
                      <option value="">Select a medical supply</option>
                      {utilities.map((supply) => (
                        <option key={supply.supplyId} value={supply.supplyId}>
                          {supply.supplyName} - {supply.supplyType.replace('_', ' ')} (Current: {supply.currentStock} {supply.unitOfMeasure})
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantity *
                    </label>
                    <input
                      type="number"
                      value={stockInData.quantity}
                      onChange={(e) => setStockInData({...stockInData, quantity: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Enter quantity to add"
                      min="1"
                      required
                    />
                  </div>

                  {/* Unit Cost */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Unit Cost
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={stockInData.unitCost}
                      onChange={(e) => setStockInData({...stockInData, unitCost: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Cost per unit"
                    />
                  </div>

                  {/* Supplier */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Supplier
                    </label>
                    <input
                      type="text"
                      value={stockInData.supplier}
                      onChange={(e) => setStockInData({...stockInData, supplier: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Supplier name"
                    />
                  </div>

                  {/* Batch Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Batch Number
                    </label>
                    <input
                      type="text"
                      value={stockInData.batchNumber}
                      onChange={(e) => setStockInData({...stockInData, batchNumber: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Batch/Lot number"
                    />
                  </div>

                  {/* Expiry Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Expiry Date
                    </label>
                    <input
                      type="date"
                      value={stockInData.expiryDate}
                      onChange={(e) => setStockInData({...stockInData, expiryDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>

                  {/* Invoice Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Invoice Number
                    </label>
                    <input
                      type="text"
                      value={stockInData.invoiceNumber}
                      onChange={(e) => setStockInData({...stockInData, invoiceNumber: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Invoice/PO number"
                    />
                  </div>

                  {/* Delivery Date */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Delivery Date
                    </label>
                    <input
                      type="date"
                      value={stockInData.deliveryDate}
                      onChange={(e) => setStockInData({...stockInData, deliveryDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    />
                  </div>

                  {/* Received By */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Received By
                    </label>
                    <input
                      type="text"
                      value={stockInData.receivedBy}
                      onChange={(e) => setStockInData({...stockInData, receivedBy: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="Person who received the stock"
                    />
                  </div>

                  {/* Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notes
                    </label>
                    <textarea
                      value={stockInData.notes}
                      onChange={(e) => setStockInData({...stockInData, notes: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      rows="3"
                      placeholder="Additional notes about this stock receipt"
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 mt-6">
                  <button
                    onClick={() => setShowStockInModal(false)}
                    className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleStockInSubmit}
                    disabled={loading || !stockInData.supplyId || !stockInData.quantity}
                    className="flex-1 py-2 px-4 rounded-lg text-white transition-colors disabled:opacity-50 bg-green-500 hover:bg-green-600"
                  >
                    {loading ? '📦 Adding Stock...' : '📦 Add Stock In'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stock Out Modal */}
        {showStockOutModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">📤 Stock Out</h3>
                  <button
                    onClick={() => setShowStockOutModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Supply Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Medical Supply *
                    </label>
                    <select
                      value={stockOutData.supplyId}
                      onChange={(e) => setStockOutData({...stockOutData, supplyId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    >
                      <option value="">Select a medical supply</option>
                      {utilities.filter(supply => supply.currentStock > 0).map((supply) => (
                        <option key={supply.supplyId} value={supply.supplyId}>
                          {supply.supplyName} - {supply.supplyType.replace('_', ' ')} (Available: {supply.currentStock} {supply.unitOfMeasure})
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantity *
                    </label>
                    <input
                      type="number"
                      value={stockOutData.quantity}
                      onChange={(e) => setStockOutData({...stockOutData, quantity: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="Enter quantity to remove"
                      min="1"
                      max={utilities.find(s => s.supplyId === stockOutData.supplyId)?.currentStock || 999}
                      required
                    />
                    {stockOutData.supplyId && (
                      <p className="text-sm text-gray-500 mt-1">
                        Available: {utilities.find(s => s.supplyId === stockOutData.supplyId)?.currentStock || 0} {utilities.find(s => s.supplyId === stockOutData.supplyId)?.unitOfMeasure || 'units'}
                      </p>
                    )}
                  </div>

                  {/* Reason */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reason for Stock Out *
                    </label>
                    <select
                      value={stockOutData.reason}
                      onChange={(e) => setStockOutData({...stockOutData, reason: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    >
                      <option value="">Select reason</option>
                      <option value="Patient Treatment">Patient Treatment</option>
                      <option value="Surgery">Surgery</option>
                      <option value="Emergency Use">Emergency Use</option>
                      <option value="Routine Care">Routine Care</option>
                      <option value="Expired">Expired</option>
                      <option value="Damaged">Damaged</option>
                      <option value="Transfer">Transfer to Another Department</option>
                      <option value="Waste">Waste/Disposal</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  {/* Used By */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Used By
                    </label>
                    <input
                      type="text"
                      value={stockOutData.usedBy}
                      onChange={(e) => setStockOutData({...stockOutData, usedBy: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="Person/department using the supply"
                    />
                  </div>

                  {/* Department */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Department
                    </label>
                    <select
                      value={stockOutData.department}
                      onChange={(e) => setStockOutData({...stockOutData, department: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      <option value="">Select department</option>
                      <option value="Emergency">Emergency</option>
                      <option value="ICU">ICU</option>
                      <option value="Surgery">Surgery</option>
                      <option value="Cardiology">Cardiology</option>
                      <option value="Pediatrics">Pediatrics</option>
                      <option value="Pharmacy">Pharmacy</option>
                      <option value="Radiology">Radiology</option>
                      <option value="Laboratory">Laboratory</option>
                      <option value="Nursing">Nursing</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  {/* Patient ID */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Patient ID (if applicable)
                    </label>
                    <input
                      type="text"
                      value={stockOutData.patientId}
                      onChange={(e) => setStockOutData({...stockOutData, patientId: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                      placeholder="Patient national ID or medical record number"
                    />
                  </div>

                  {/* Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notes
                    </label>
                    <textarea
                      value={stockOutData.notes}
                      onChange={(e) => setStockOutData({...stockOutData, notes: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                      rows="3"
                      placeholder="Additional notes about this stock usage"
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 mt-6">
                  <button
                    onClick={() => setShowStockOutModal(false)}
                    className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleStockOutSubmit}
                    disabled={loading || !stockOutData.supplyId || !stockOutData.quantity || !stockOutData.reason}
                    className="flex-1 py-2 px-4 rounded-lg text-white transition-colors disabled:opacity-50 bg-red-500 hover:bg-red-600"
                  >
                    {loading ? '📤 Removing Stock...' : '📤 Remove Stock'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Expiry Notifications Modal */}
        {showExpiryNotifications && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 1000 }}>
            <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">⏰ Expiry Date Notifications</h3>
                  <button
                    onClick={() => setShowExpiryNotifications(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {expiringSupplies.length === 0 ? (
                  <div className="text-center py-8">
                    <span className="text-4xl mb-4 block">✅</span>
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Expiring Supplies</h3>
                    <p className="text-gray-500">All medical supplies are within safe expiry dates.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                      <div className="flex items-center">
                        <span className="text-orange-500 text-xl mr-2">⚠️</span>
                        <div>
                          <h4 className="font-semibold text-orange-800">Attention Required</h4>
                          <p className="text-orange-700 text-sm">
                            {expiringSupplies.length} medical supplies are expiring within the next 30 days.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-4">
                      {expiringSupplies.map((supply) => {
                        const expiryDate = new Date(supply.expiryDate);
                        const today = new Date();
                        const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

                        return (
                          <div
                            key={supply.supplyId}
                            className={`border rounded-lg p-4 ${
                              daysUntilExpiry <= 0 ? 'border-red-300 bg-red-50' :
                              daysUntilExpiry <= 7 ? 'border-orange-300 bg-orange-50' :
                              'border-yellow-300 bg-yellow-50'
                            }`}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center mb-2">
                                  <span className={`text-lg mr-2 ${
                                    daysUntilExpiry <= 0 ? 'text-red-500' :
                                    daysUntilExpiry <= 7 ? 'text-orange-500' :
                                    'text-yellow-500'
                                  }`}>
                                    {daysUntilExpiry <= 0 ? '🚫' : daysUntilExpiry <= 7 ? '⚠️' : '⏰'}
                                  </span>
                                  <h4 className="font-semibold text-gray-900">{supply.supplyName}</h4>
                                </div>

                                <div className="grid grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <p className="text-gray-600">
                                      <span className="font-medium">Type:</span> {supply.supplyType.replace('_', ' ')}
                                    </p>
                                    <p className="text-gray-600">
                                      <span className="font-medium">Location:</span> {supply.location}
                                    </p>
                                    <p className="text-gray-600">
                                      <span className="font-medium">Stock:</span> {supply.currentStock} {supply.unitOfMeasure}
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">
                                      <span className="font-medium">Batch:</span> {supply.batchNumber || 'N/A'}
                                    </p>
                                    <p className="text-gray-600">
                                      <span className="font-medium">Supplier:</span> {supply.supplier || 'N/A'}
                                    </p>
                                    <p className="text-gray-600">
                                      <span className="font-medium">Expiry Date:</span> {expiryDate.toLocaleDateString()}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              <div className="ml-4 text-right">
                                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                                  daysUntilExpiry <= 0 ? 'bg-red-100 text-red-800' :
                                  daysUntilExpiry <= 7 ? 'bg-orange-100 text-orange-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {daysUntilExpiry <= 0 ? 'EXPIRED' :
                                   daysUntilExpiry === 1 ? '1 day left' :
                                   `${daysUntilExpiry} days left`}
                                </span>

                                {daysUntilExpiry <= 0 && (
                                  <div className="mt-2">
                                    <button
                                      onClick={() => {
                                        setStockOutData({
                                          supplyId: supply.supplyId,
                                          quantity: supply.currentStock,
                                          reason: 'Expired',
                                          usedBy: 'Admin User',
                                          department: supply.department,
                                          patientId: '',
                                          notes: `Expired on ${expiryDate.toLocaleDateString()}`
                                        });
                                        setShowExpiryNotifications(false);
                                        setShowStockOutModal(true);
                                      }}
                                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs transition-colors"
                                    >
                                      Remove Expired Stock
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-between mt-6">
                  <button
                    onClick={() => {
                      fetchExpiringSupplies();
                      fetchUtilities();
                    }}
                    className="py-2 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
                  >
                    🔄 Refresh
                  </button>
                  <button
                    onClick={() => setShowExpiryNotifications(false)}
                    className="py-2 px-4 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default AdminDashboard;

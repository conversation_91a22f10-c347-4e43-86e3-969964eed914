{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\context\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n        }\n      } catch (error) {\n        console.error('Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = userData => {\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    sessionStorage.setItem('authToken', userData.token || 'authenticated');\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    console.log('User logged in:', userData);\n  };\n\n  // Logout function\n  const logout = () => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    setUser(null);\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = role => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "checkAuth", "token", "sessionStorage", "getItem", "userData", "parsedUser", "JSON", "parse", "error", "console", "removeItem", "login", "setItem", "stringify", "log", "logout", "isAuthenticated", "hasRole", "role", "isAdmin", "userType", "isStaff", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/context/AuthContext.jsx"], "sourcesContent": ["import { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n\n        if (token && userData) {\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n        }\n      } catch (error) {\n        console.error('Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = (userData) => {\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    sessionStorage.setItem('authToken', userData.token || 'authenticated');\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    console.log('User logged in:', userData);\n  };\n\n  // Logout function\n  const logout = () => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    setUser(null);\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = (role) => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMe,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF;QACA;QACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QACjD,MAAMC,QAAQ,GAAGF,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC;QAEnD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;UACrB,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;UACvCP,OAAO,CAACQ,UAAU,CAAC;QACrB;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACAN,cAAc,CAACQ,UAAU,CAAC,WAAW,CAAC;QACtCR,cAAc,CAACQ,UAAU,CAAC,UAAU,CAAC;MACvC,CAAC,SAAS;QACRX,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,KAAK,GAAIP,QAAQ,IAAK;IAC1BP,OAAO,CAACO,QAAQ,CAAC;IACjB;IACAF,cAAc,CAACU,OAAO,CAAC,WAAW,EAAER,QAAQ,CAACH,KAAK,IAAI,eAAe,CAAC;IACtEC,cAAc,CAACU,OAAO,CAAC,UAAU,EAAEN,IAAI,CAACO,SAAS,CAACT,QAAQ,CAAC,CAAC;IAC5DK,OAAO,CAACK,GAAG,CAAC,iBAAiB,EAAEV,QAAQ,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMW,MAAM,GAAGA,CAAA,KAAM;IACnB;IACAb,cAAc,CAACQ,UAAU,CAAC,WAAW,CAAC;IACtCR,cAAc,CAACQ,UAAU,CAAC,UAAU,CAAC;IACrCb,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAACpB,IAAI;EACf,CAAC;;EAED;EACA,MAAMqB,OAAO,GAAIC,IAAI,IAAK;IACxB,OAAOtB,IAAI,IAAIA,IAAI,CAACsB,IAAI,KAAKA,IAAI;EACnC,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOvB,IAAI,KAAKA,IAAI,CAACsB,IAAI,KAAK,OAAO,IAAItB,IAAI,CAACwB,QAAQ,KAAK,OAAO,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOzB,IAAI,KAAKA,IAAI,CAACsB,IAAI,KAAK,QAAQ,IAAItB,IAAI,CAACsB,IAAI,KAAK,OAAO,IAAItB,IAAI,CAACwB,QAAQ,KAAK,OAAO,CAAC;EAC/F,CAAC;EAED,MAAME,KAAK,GAAG;IACZ1B,IAAI;IACJe,KAAK;IACLI,MAAM;IACNC,eAAe;IACfC,OAAO;IACPE,OAAO;IACPE,OAAO;IACPvB;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5B,QAAA,EAChCA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAChC,GAAA,CAnFWF,YAAY;AAAAmC,EAAA,GAAZnC,YAAY;AAqFzB,eAAeL,WAAW;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
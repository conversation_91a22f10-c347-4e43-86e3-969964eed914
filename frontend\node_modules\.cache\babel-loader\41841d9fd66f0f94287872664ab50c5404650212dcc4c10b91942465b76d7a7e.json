{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport { useState, useEffect, useRef } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Help from './pages/Help';\nimport MedicalRecords from './pages/MedicalRecords';\nimport Pharmacy from './pages/Pharmacy';\nimport Doctor from './pages/Doctor';\nimport Patients from './pages/Patients';\nimport HospitalTransfer from './pages/HospitalTransfer';\nimport Exams from './pages/Exams';\nimport Messages from './pages/Messages';\nimport Room from './pages/Room';\nimport Appointment from './pages/Appointment';\nimport AdminDashboard from './pages/AdminDashboard';\nimport BillingPage from './pages/BillingPage';\nimport PatientPortal from './pages/PatientPortal';\nimport './index.css';\n\n// Header component with navigation\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showLogin, setShowLogin] = useState(false);\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isAuthenticated,\n    isAdmin,\n    login\n  } = useAuth();\n  const profileDropdownRef = useRef(null);\n\n  // Handle successful login\n  const handleLogin = userData => {\n    login(userData);\n    setShowLogin(false);\n  };\n\n  // Close profile dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {\n        setShowProfileDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  const getLinkClasses = (path, isMobile = false) => {\n    const baseClasses = isMobile ? \"block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent\" : \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative\";\n    const activeClasses = isMobile ? \"text-blue-700 bg-blue-50 border-blue-600 shadow-sm\" : \"text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200\";\n    const inactiveClasses = isMobile ? \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300\" : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm\";\n    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl font-bold text-gray-800 tracking-wide\",\n                  children: [\"HEALTH\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600\",\n                    children: \"CARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 -mt-1\",\n                  children: \"Portal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden md:flex space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: getLinkClasses('/'),\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: getLinkClasses('/about'),\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/appointment\",\n            className: getLinkClasses('/appointment'),\n            children: \"Appointment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/billing\",\n            className: getLinkClasses('/billing'),\n            children: \"Billing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/help\",\n            className: getLinkClasses('/help'),\n            children: \"Help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            ref: profileDropdownRef,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowProfileDropdown(!showProfileDropdown),\n              className: \"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-sm font-bold\",\n                  children: user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 capitalize\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: `w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`,\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M19 9l-7 7-7-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), showProfileDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white font-bold\",\n                      children: user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-900\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600 capitalize font-medium\",\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add profile page navigation here if needed\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add settings page navigation here if needed\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 23\n                }, this), isAdmin() && /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/admin-dashboard\",\n                  onClick: () => setShowProfileDropdown(false),\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Admin Dashboard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-100 pt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    logout();\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Logout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowLogin(true),\n            className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), showLogin && /*#__PURE__*/_jsxDEV(Login, {\n          onLogin: handleLogin,\n          onClose: () => setShowLogin(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"text-gray-700 hover:text-blue-600 p-2\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: getLinkClasses('/', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/about\",\n          className: getLinkClasses('/about', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/appointment\",\n          className: getLinkClasses('/appointment', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Appointment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/billing\",\n          className: getLinkClasses('/billing', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Billing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/help\",\n          className: getLinkClasses('/help', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Help\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4 pb-3 border-t border-gray-200 mx-4\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 capitalize\",\n                children: user.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                logout();\n                setIsMobileMenuOpen(false);\n              },\n              className: \"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowLogin(true);\n              setIsMobileMenuOpen(false);\n            },\n            className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"+fqRH8n/8Iqyb7dFLHyAWjHrgjw=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = Header;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-h-screen bg-white\",\n          children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/about\",\n                element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/help\",\n                element: /*#__PURE__*/_jsxDEV(Help, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 46\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/messages\",\n                element: /*#__PURE__*/_jsxDEV(Messages, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 50\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/patients\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor', 'nurse'],\n                  children: /*#__PURE__*/_jsxDEV(Patients, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/medical-records\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor'],\n                  children: /*#__PURE__*/_jsxDEV(MedicalRecords, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/exams\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor'],\n                  children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/pharmacy\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor', 'nurse', 'staff'],\n                  children: /*#__PURE__*/_jsxDEV(Pharmacy, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/billing\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor', 'staff'],\n                  children: /*#__PURE__*/_jsxDEV(BillingPage, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/appointment\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor', 'staff'],\n                  children: /*#__PURE__*/_jsxDEV(Appointment, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/room\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor'],\n                  children: /*#__PURE__*/_jsxDEV(Room, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/hospital-transfer\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  allowedRoles: ['admin', 'doctor'],\n                  children: /*#__PURE__*/_jsxDEV(HospitalTransfer, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/doctor\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requireAdmin: true,\n                  children: /*#__PURE__*/_jsxDEV(Doctor, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/admin-dashboard\",\n                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                  requireAdmin: true,\n                  children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "useLocation", "useState", "useEffect", "useRef", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "ProtectedRoute", "Home", "About", "Help", "MedicalRecords", "Pharmacy", "Doctor", "Patients", "HospitalTransfer", "<PERSON><PERSON>", "Messages", "Room", "Appointment", "AdminDashboard", "BillingPage", "PatientPortal", "jsxDEV", "_jsxDEV", "Header", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "showLogin", "setShow<PERSON><PERSON>in", "showProfileDropdown", "setShowProfileDropdown", "location", "user", "logout", "isAuthenticated", "isAdmin", "login", "profileDropdownRef", "handleLogin", "userData", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "isActive", "path", "pathname", "getLinkClasses", "isMobile", "baseClasses", "activeClasses", "inactiveClasses", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "ref", "onClick", "firstName", "char<PERSON>t", "toUpperCase", "name", "role", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "email", "onLogin", "onClose", "_c", "App", "element", "allowedRoles", "requireAdmin", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';\nimport { useState, useEffect, useRef } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Help from './pages/Help';\nimport MedicalRecords from './pages/MedicalRecords';\nimport Pharmacy from './pages/Pharmacy';\nimport Doctor from './pages/Doctor';\nimport Patients from './pages/Patients';\nimport HospitalTransfer from './pages/HospitalTransfer';\nimport Exams from './pages/Exams';\nimport Messages from './pages/Messages';\nimport Room from './pages/Room';\nimport Appointment from './pages/Appointment';\nimport AdminDashboard from './pages/AdminDashboard';\nimport BillingPage from './pages/BillingPage';\nimport PatientPortal from './pages/PatientPortal';\nimport './index.css';\n\n// Header component with navigation\nfunction Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showLogin, setShowLogin] = useState(false);\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const location = useLocation();\n  const { user, logout, isAuthenticated, isAdmin, login } = useAuth();\n  const profileDropdownRef = useRef(null);\n\n  // Handle successful login\n  const handleLogin = (userData) => {\n    login(userData);\n    setShowLogin(false);\n  };\n\n  // Close profile dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {\n        setShowProfileDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  const getLinkClasses = (path, isMobile = false) => {\n    const baseClasses = isMobile\n      ? \"block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent\"\n      : \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative\";\n    const activeClasses = isMobile\n      ? \"text-blue-700 bg-blue-50 border-blue-600 shadow-sm\"\n      : \"text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200\";\n    const inactiveClasses = isMobile\n      ? \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300\"\n      : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm\";\n\n    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-800 tracking-wide\">\n                    HEALTH<span className=\"text-blue-600\">CARE</span>\n                  </h1>\n                  <p className=\"text-xs text-gray-500 -mt-1\">Portal</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link to=\"/\" className={getLinkClasses('/')}>\n              Home\n            </Link>\n            <Link to=\"/about\" className={getLinkClasses('/about')}>\n              About\n            </Link>\n            <Link to=\"/appointment\" className={getLinkClasses('/appointment')}>\n              Appointment\n            </Link>\n            <Link to=\"/billing\" className={getLinkClasses('/billing')}>\n              Billing\n            </Link>\n            <Link to=\"/help\" className={getLinkClasses('/help')}>\n              Help\n            </Link>\n          </nav>\n\n          {/* Profile/Login Section */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated() ? (\n              <div className=\"relative\" ref={profileDropdownRef}>\n                <button\n                  onClick={() => setShowProfileDropdown(!showProfileDropdown)}\n                  className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}\n                    </span>\n                  </div>\n                  <div className=\"text-left\">\n                    <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">{user.role}</div>\n                  </div>\n                  <svg className={`w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* Profile Dropdown */}\n                {showProfileDropdown && (\n                  <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\">\n                    <div className=\"px-4 py-3 border-b border-gray-100\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                          <span className=\"text-white font-bold\">\n                            {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}\n                          </span>\n                        </div>\n                        <div>\n                          <div className=\"font-medium text-gray-900\">{user.name}</div>\n                          <div className=\"text-sm text-gray-500\">{user.email}</div>\n                          <div className=\"text-xs text-blue-600 capitalize font-medium\">{user.role}</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"py-2\">\n                      <button\n                        onClick={() => {\n                          // Add profile page navigation here if needed\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                        <span>View Profile</span>\n                      </button>\n\n                      <button\n                        onClick={() => {\n                          // Add settings page navigation here if needed\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                        <span>Settings</span>\n                      </button>\n\n                      {isAdmin() && (\n                        <Link\n                          to=\"/admin-dashboard\"\n                          onClick={() => setShowProfileDropdown(false)}\n                          className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                          </svg>\n                          <span>Admin Dashboard</span>\n                        </Link>\n                      )}\n                    </div>\n\n                    <div className=\"border-t border-gray-100 pt-2\">\n                      <button\n                        onClick={() => {\n                          logout();\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        <span>Logout</span>\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <button\n                onClick={() => setShowLogin(true)}\n                className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\"\n              >\n                Login\n              </button>\n            )}\n          </div>\n\n          {/* Login Modal */}\n          {showLogin && (\n            <Login\n              onLogin={handleLogin}\n              onClose={() => setShowLogin(false)}\n            />\n          )}\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 p-2\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                {isMobileMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\">\n            <Link\n              to=\"/\"\n              className={getLinkClasses('/', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Home\n            </Link>\n            <Link\n              to=\"/about\"\n              className={getLinkClasses('/about', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              About\n            </Link>\n            <Link\n              to=\"/appointment\"\n              className={getLinkClasses('/appointment', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Appointment\n            </Link>\n            <Link\n              to=\"/billing\"\n              className={getLinkClasses('/billing', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Billing\n            </Link>\n            <Link\n              to=\"/help\"\n              className={getLinkClasses('/help', true)}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              Help\n            </Link>\n            <div className=\"pt-4 pb-3 border-t border-gray-200 mx-4\">\n              {isAuthenticated() ? (\n                <div className=\"space-y-3\">\n                  <div className=\"text-center\">\n                    <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">{user.role}</div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      logout();\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className=\"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <button\n                  onClick={() => {\n                    setShowLogin(true);\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\"\n                >\n                  Login\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n\nfunction App() {\n  return(\n    <AuthProvider>\n      <Router>\n        <ProtectedRoute>\n          <div className=\"min-h-screen bg-white\">\n            <Header />\n\n            {/* Main Content */}\n            <main>\n              <Routes>\n                {/* Public routes - accessible to all authenticated users */}\n                <Route path=\"/\" element={<Home />} />\n                <Route path=\"/about\" element={<About />} />\n                <Route path=\"/help\" element={<Help />} />\n                <Route path=\"/messages\" element={<Messages />} />\n\n                {/* Patient management - accessible to admin, doctor, nurse */}\n                <Route\n                  path=\"/patients\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse']}>\n                      <Patients />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Medical records - accessible to admin, doctor only */}\n                <Route\n                  path=\"/medical-records\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <MedicalRecords />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Exams - accessible to admin, doctor only */}\n                <Route\n                  path=\"/exams\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <Exams />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Pharmacy - accessible to admin, doctor, nurse, staff */}\n                <Route\n                  path=\"/pharmacy\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse', 'staff']}>\n                      <Pharmacy />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Billing - accessible to admin, doctor, staff */}\n                <Route\n                  path=\"/billing\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>\n                      <BillingPage />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Appointments - accessible to admin, doctor, staff only */}\n                <Route\n                  path=\"/appointment\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>\n                      <Appointment />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Room management - accessible to admin, doctor only */}\n                <Route\n                  path=\"/room\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <Room />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Hospital transfers - accessible to admin, doctor only */}\n                <Route\n                  path=\"/hospital-transfer\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <HospitalTransfer />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Doctor management - accessible to admin only */}\n                <Route\n                  path=\"/doctor\"\n                  element={\n                    <ProtectedRoute requireAdmin={true}>\n                      <Doctor />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Admin dashboard - accessible to admin only */}\n                <Route\n                  path=\"/admin-dashboard\"\n                  element={\n                    <ProtectedRoute requireAdmin={true}>\n                      <AdminDashboard />\n                    </ProtectedRoute>\n                  }\n                />\n              </Routes>\n            </main>\n          </div>\n        </ProtectedRoute>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC5F,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAO,aAAa;;AAEpB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMgC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGjC,OAAO,CAAC,CAAC;EACnE,MAAMkC,kBAAkB,GAAGpC,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMqC,WAAW,GAAIC,QAAQ,IAAK;IAChCH,KAAK,CAACG,QAAQ,CAAC;IACfX,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACd,MAAMwC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIJ,kBAAkB,CAACK,OAAO,IAAI,CAACL,kBAAkB,CAACK,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpFd,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAEDe,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOlB,QAAQ,CAACmB,QAAQ,KAAKD,IAAI;EACnC,CAAC;EAED,MAAME,cAAc,GAAGA,CAACF,IAAI,EAAEG,QAAQ,GAAG,KAAK,KAAK;IACjD,MAAMC,WAAW,GAAGD,QAAQ,GACxB,iGAAiG,GACjG,iFAAiF;IACrF,MAAME,aAAa,GAAGF,QAAQ,GAC1B,oDAAoD,GACpD,yDAAyD;IAC7D,MAAMG,eAAe,GAAGH,QAAQ,GAC5B,0EAA0E,GAC1E,oEAAoE;IAExE,OAAO,GAAGC,WAAW,IAAIL,QAAQ,CAACC,IAAI,CAAC,GAAGK,aAAa,GAAGC,eAAe,EAAE;EAC7E,CAAC;EAED,oBACEjC,OAAA;IAAQkC,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAC/EnC,OAAA;MAAKkC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDnC,OAAA;QAAKkC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCnC,OAAA;YAAKkC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eAC9CnC,OAAA;cAAKkC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CnC,OAAA;gBAAKkC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,eAChHnC,OAAA;kBAAKkC,SAAS,EAAC,oBAAoB;kBAACE,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAF,QAAA,eACzEnC,OAAA;oBAAMsC,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,qLAAqL;oBAACC,QAAQ,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAAmC,QAAA,gBACEnC,OAAA;kBAAIkC,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,GAAC,QACtD,eAAAnC,OAAA;oBAAMkC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAI;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACL5C,OAAA;kBAAGkC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5C,OAAA;UAAKkC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCnC,OAAA,CAACzB,IAAI;YAACsE,EAAE,EAAC,GAAG;YAACX,SAAS,EAAEL,cAAc,CAAC,GAAG,CAAE;YAAAM,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;YAACsE,EAAE,EAAC,QAAQ;YAACX,SAAS,EAAEL,cAAc,CAAC,QAAQ,CAAE;YAAAM,QAAA,EAAC;UAEvD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;YAACsE,EAAE,EAAC,cAAc;YAACX,SAAS,EAAEL,cAAc,CAAC,cAAc,CAAE;YAAAM,QAAA,EAAC;UAEnE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;YAACsE,EAAE,EAAC,UAAU;YAACX,SAAS,EAAEL,cAAc,CAAC,UAAU,CAAE;YAAAM,QAAA,EAAC;UAE3D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;YAACsE,EAAE,EAAC,OAAO;YAACX,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;YAAAM,QAAA,EAAC;UAErD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN5C,OAAA;UAAKkC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDvB,eAAe,CAAC,CAAC,gBAChBZ,OAAA;YAAKkC,SAAS,EAAC,UAAU;YAACY,GAAG,EAAE/B,kBAAmB;YAAAoB,QAAA,gBAChDnC,OAAA;cACE+C,OAAO,EAAEA,CAAA,KAAMvC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAC5D2B,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/LnC,OAAA;gBAAKkC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,eAChHnC,OAAA;kBAAMkC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC3CzB,IAAI,CAACsC,SAAS,GAAGtC,IAAI,CAACsC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;gBAAG;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5C,OAAA;gBAAKkC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnC,OAAA;kBAAKkC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEzB,IAAI,CAACyC;gBAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpE5C,OAAA;kBAAKkC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEzB,IAAI,CAAC0C;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN5C,OAAA;gBAAKkC,SAAS,EAAE,8CAA8C3B,mBAAmB,GAAG,YAAY,GAAG,EAAE,EAAG;gBAAC6B,IAAI,EAAC,MAAM;gBAACiB,MAAM,EAAC,cAAc;gBAAChB,OAAO,EAAC,WAAW;gBAAAF,QAAA,eAC5JnC,OAAA;kBAAMsD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACjB,CAAC,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGRrC,mBAAmB,iBAClBP,OAAA;cAAKkC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBACxGnC,OAAA;gBAAKkC,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDnC,OAAA;kBAAKkC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CnC,OAAA;oBAAKkC,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,eAClHnC,OAAA;sBAAMkC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EACnCzB,IAAI,CAACsC,SAAS,GAAGtC,IAAI,CAACsC,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;oBAAG;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5C,OAAA;oBAAAmC,QAAA,gBACEnC,OAAA;sBAAKkC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEzB,IAAI,CAACyC;oBAAI;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5D5C,OAAA;sBAAKkC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEzB,IAAI,CAAC+C;oBAAK;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzD5C,OAAA;sBAAKkC,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAEzB,IAAI,CAAC0C;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKkC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBnC,OAAA;kBACE+C,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACAvC,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF0B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzGnC,OAAA;oBAAKkC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5EnC,OAAA;sBAAMsD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAqE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1I,CAAC,eACN5C,OAAA;oBAAAmC,QAAA,EAAM;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAET5C,OAAA;kBACE+C,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACAvC,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF0B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzGnC,OAAA;oBAAKkC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,gBAC5EnC,OAAA;sBAAMsD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAqe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7iB5C,OAAA;sBAAMsD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC,eACN5C,OAAA;oBAAAmC,QAAA,EAAM;kBAAQ;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EAER/B,OAAO,CAAC,CAAC,iBACRb,OAAA,CAACzB,IAAI;kBACHsE,EAAE,EAAC,kBAAkB;kBACrBE,OAAO,EAAEA,CAAA,KAAMvC,sBAAsB,CAAC,KAAK,CAAE;kBAC7C0B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzGnC,OAAA;oBAAKkC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5EnC,OAAA;sBAAMsD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAgM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrQ,CAAC,eACN5C,OAAA;oBAAAmC,QAAA,EAAM;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN5C,OAAA;gBAAKkC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,eAC5CnC,OAAA;kBACE+C,OAAO,EAAEA,CAAA,KAAM;oBACbpC,MAAM,CAAC,CAAC;oBACRH,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF0B,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,gBAEvGnC,OAAA;oBAAKkC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5EnC,OAAA;sBAAMsD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAA2F;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK,CAAC,eACN5C,OAAA;oBAAAmC,QAAA,EAAM;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN5C,OAAA;YACE+C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,CAAC,IAAI,CAAE;YAClC4B,SAAS,EAAC,+NAA+N;YAAAC,QAAA,EAC1O;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLvC,SAAS,iBACRL,OAAA,CAAClB,KAAK;UACJ4E,OAAO,EAAE1C,WAAY;UACrB2C,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAAC,KAAK;QAAE;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACF,eAGD5C,OAAA;UAAKkC,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBnC,OAAA;YACE+C,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtD+B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAEjDnC,OAAA;cAAKkC,SAAS,EAAC,SAAS;cAACE,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACgB,MAAM,EAAC,cAAc;cAAAlB,QAAA,EAC3EhC,gBAAgB,gBACfH,OAAA;gBAAMsD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACjB,CAAC,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9F5C,OAAA;gBAAMsD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACjB,CAAC,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzC,gBAAgB,iBACfH,OAAA;MAAKkC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBnC,OAAA;QAAKkC,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBACjFnC,OAAA,CAACzB,IAAI;UACHsE,EAAE,EAAC,GAAG;UACNX,SAAS,EAAEL,cAAc,CAAC,GAAG,EAAE,IAAI,CAAE;UACrCkB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;UACHsE,EAAE,EAAC,QAAQ;UACXX,SAAS,EAAEL,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAE;UAC1CkB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;UACHsE,EAAE,EAAC,cAAc;UACjBX,SAAS,EAAEL,cAAc,CAAC,cAAc,EAAE,IAAI,CAAE;UAChDkB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;UACHsE,EAAE,EAAC,UAAU;UACbX,SAAS,EAAEL,cAAc,CAAC,UAAU,EAAE,IAAI,CAAE;UAC5CkB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5C,OAAA,CAACzB,IAAI;UACHsE,EAAE,EAAC,OAAO;UACVX,SAAS,EAAEL,cAAc,CAAC,OAAO,EAAE,IAAI,CAAE;UACzCkB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP5C,OAAA;UAAKkC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrDvB,eAAe,CAAC,CAAC,gBAChBZ,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBnC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnC,OAAA;gBAAKkC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEzB,IAAI,CAACyC;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpE5C,OAAA;gBAAKkC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEzB,IAAI,CAAC0C;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACN5C,OAAA;cACE+C,OAAO,EAAEA,CAAA,KAAM;gBACbpC,MAAM,CAAC,CAAC;gBACRP,mBAAmB,CAAC,KAAK,CAAC;cAC5B,CAAE;cACF8B,SAAS,EAAC,iMAAiM;cAAAC,QAAA,EAC5M;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEN5C,OAAA;YACE+C,OAAO,EAAEA,CAAA,KAAM;cACbzC,YAAY,CAAC,IAAI,CAAC;cAClBF,mBAAmB,CAAC,KAAK,CAAC;YAC5B,CAAE;YACF8B,SAAS,EAAC,qMAAqM;YAAAC,QAAA,EAChN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb;AAAC1C,EAAA,CAxSQD,MAAM;EAAA,QAIIzB,WAAW,EAC8BK,OAAO;AAAA;AAAA+E,EAAA,GAL1D3D,MAAM;AA0Sf,SAAS4D,GAAGA,CAAA,EAAG;EACb,oBACE7D,OAAA,CAACpB,YAAY;IAAAuD,QAAA,eACXnC,OAAA,CAAC5B,MAAM;MAAA+D,QAAA,eACLnC,OAAA,CAACjB,cAAc;QAAAoD,QAAA,eACbnC,OAAA;UAAKkC,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCnC,OAAA,CAACC,MAAM;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGV5C,OAAA;YAAAmC,QAAA,eACEnC,OAAA,CAAC3B,MAAM;cAAA8D,QAAA,gBAELnC,OAAA,CAAC1B,KAAK;gBAACqD,IAAI,EAAC,GAAG;gBAACmC,OAAO,eAAE9D,OAAA,CAAChB,IAAI;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrC5C,OAAA,CAAC1B,KAAK;gBAACqD,IAAI,EAAC,QAAQ;gBAACmC,OAAO,eAAE9D,OAAA,CAACf,KAAK;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3C5C,OAAA,CAAC1B,KAAK;gBAACqD,IAAI,EAAC,OAAO;gBAACmC,OAAO,eAAE9D,OAAA,CAACd,IAAI;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC5C,OAAA,CAAC1B,KAAK;gBAACqD,IAAI,EAAC,WAAW;gBAACmC,OAAO,eAAE9D,OAAA,CAACP,QAAQ;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGjD5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,WAAW;gBAChBmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;kBAAA5B,QAAA,eACzDnC,OAAA,CAACV,QAAQ;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,kBAAkB;gBACvBmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;kBAAA5B,QAAA,eAChDnC,OAAA,CAACb,cAAc;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,QAAQ;gBACbmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;kBAAA5B,QAAA,eAChDnC,OAAA,CAACR,KAAK;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,WAAW;gBAChBmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAE;kBAAA5B,QAAA,eAClEnC,OAAA,CAACZ,QAAQ;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,UAAU;gBACfmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;kBAAA5B,QAAA,eACzDnC,OAAA,CAACH,WAAW;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,cAAc;gBACnBmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;kBAAA5B,QAAA,eACzDnC,OAAA,CAACL,WAAW;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,OAAO;gBACZmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;kBAAA5B,QAAA,eAChDnC,OAAA,CAACN,IAAI;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,oBAAoB;gBACzBmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACgF,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;kBAAA5B,QAAA,eAChDnC,OAAA,CAACT,gBAAgB;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,SAAS;gBACdmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACiF,YAAY,EAAE,IAAK;kBAAA7B,QAAA,eACjCnC,OAAA,CAACX,MAAM;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGF5C,OAAA,CAAC1B,KAAK;gBACJqD,IAAI,EAAC,kBAAkB;gBACvBmC,OAAO,eACL9D,OAAA,CAACjB,cAAc;kBAACiF,YAAY,EAAE,IAAK;kBAAA7B,QAAA,eACjCnC,OAAA,CAACJ,cAAc;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cACjB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACqB,GAAA,GA3HQJ,GAAG;AA6HZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
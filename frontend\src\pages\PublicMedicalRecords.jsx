import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const PublicMedicalRecords = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('nationalId');
  const [medicalRecord, setMedicalRecord] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const API_BASE_URL = 'http://localhost:5000/api';

  // Search for medical records
  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/medical-records/${searchQuery}`);
      const data = await response.json();
      
      if (data.success) {
        setMedicalRecord(data.data);
      } else {
        setMedicalRecord(null);
        alert('No medical records found for this ID');
      }
    } catch (error) {
      console.error('Error fetching medical records:', error);
      alert('Error searching medical records');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/')}
                className="text-gray-600 hover:text-gray-900 flex items-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
                Back to Patient Portal
              </button>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-teal-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Medical Records</h1>
                <p className="text-sm text-gray-600">Public Access</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Search Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Search Medical Records</h2>
          
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Search By</label>
                <select
                  value={searchType}
                  onChange={(e) => setSearchType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="nationalId">National ID</option>
                  <option value="name">Patient Name</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {searchType === 'nationalId' ? 'National ID' : 'Patient Name'}
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={searchType === 'nationalId' ? 'Enter National ID' : 'Enter Patient Name'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  required
                />
              </div>
              
              <div className="flex items-end">
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Searching...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      Search Records
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Results Section */}
        {medicalRecord ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            {/* Patient Info Header */}
            <div className="bg-teal-50 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">
                    {medicalRecord.patientName?.charAt(0) || 'P'}
                  </span>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">{medicalRecord.patientName}</h4>
                  <p className="text-gray-600">National ID: {medicalRecord.nationalId}</p>
                </div>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="text-sm font-medium text-blue-700">Exams</h5>
                    <p className="text-2xl font-bold text-blue-900">{medicalRecord.summary?.totalExams || 0}</p>
                  </div>
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-blue-600">🔬</span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="text-sm font-medium text-green-700">Prescriptions</h5>
                    <p className="text-2xl font-bold text-green-900">{medicalRecord.summary?.totalPrescriptions || 0}</p>
                  </div>
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <span className="text-green-600">💊</span>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="text-sm font-medium text-purple-700">Appointments</h5>
                    <p className="text-2xl font-bold text-purple-900">{medicalRecord.summary?.totalAppointments || 0}</p>
                  </div>
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <span className="text-purple-600">📅</span>
                  </div>
                </div>
              </div>

              <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="text-sm font-medium text-orange-700">Room Stays</h5>
                    <p className="text-2xl font-bold text-orange-900">{medicalRecord.summary?.totalRoomAssignments || 0}</p>
                  </div>
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                    <span className="text-orange-600">🏥</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex space-x-8">
                {[
                  { id: 'overview', label: '📋 Overview' },
                  { id: 'exams', label: '🔬 Exams' },
                  { id: 'prescriptions', label: '💊 Prescriptions' },
                  { id: 'appointments', label: '📅 Appointments' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-3 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-teal-500 text-teal-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="min-h-[300px]">
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">📋 Medical Overview</h4>
                  
                  {/* Recent Exams */}
                  {medicalRecord.exams && medicalRecord.exams.length > 0 && (
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h5 className="font-medium text-blue-900 mb-3">🔬 Recent Exams</h5>
                      <div className="space-y-2">
                        {medicalRecord.exams.slice(0, 3).map((exam) => (
                          <div key={exam.id} className="flex justify-between items-center">
                            <div>
                              <span className="font-medium text-blue-800">{exam.examType}</span>
                              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(exam.status)}`}>
                                {exam.status}
                              </span>
                            </div>
                            <span className="text-blue-600 text-sm">{formatDate(exam.examDate)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Recent Prescriptions */}
                  {medicalRecord.prescriptions && medicalRecord.prescriptions.length > 0 && (
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h5 className="font-medium text-green-900 mb-3">💊 Recent Prescriptions</h5>
                      <div className="space-y-2">
                        {medicalRecord.prescriptions.slice(0, 3).map((prescription) => (
                          <div key={prescription.id} className="flex justify-between items-center">
                            <div>
                              <span className="font-medium text-green-800">{prescription.diagnosis}</span>
                              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(prescription.status)}`}>
                                {prescription.status}
                              </span>
                            </div>
                            <span className="text-green-600 text-sm">{formatDate(prescription.prescriptionDate)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Other tabs content would go here */}
              {activeTab !== 'overview' && (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">📋</span>
                  </div>
                  <p className="text-gray-500">Detailed {activeTab} view coming soon</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-gray-400 text-2xl">🔍</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Search for Medical Records</h3>
            <p className="text-gray-600">Enter a National ID or patient name to view medical records</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PublicMedicalRecords;

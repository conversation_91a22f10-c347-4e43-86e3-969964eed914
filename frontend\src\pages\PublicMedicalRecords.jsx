import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const PublicMedicalRecords = () => {
  const navigate = useNavigate();
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [medicalRecord, setMedicalRecord] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  const API_BASE_URL = 'http://localhost:5000/api';

  // Direct search for medical records - no patient suggestions for privacy
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a National ID to search medical records');
      return;
    }

    setLoading(true);
    setError('');
    setSelectedPatient(null);
    setMedicalRecord(null);

    try {
      // Direct search by National ID only - no patient suggestions
      const response = await fetch(`${API_BASE_URL}/medical-records/${encodeURIComponent(searchQuery)}`);
      const data = await response.json();

      if (data.success && data.data) {
        // Set the medical record data directly
        setMedicalRecord(data.data);
        setSelectedPatient(data.data.patient || {
          nationalId: searchQuery,
          firstName: data.data.patient?.firstName || 'Unknown',
          lastName: data.data.patient?.lastName || 'Patient'
        });
        setActiveTab('overview');
        console.log('Medical records found for National ID:', searchQuery);
      } else {
        setError('No medical records found for this National ID. Please check the ID and try again.');
        setMedicalRecord(null);
        setSelectedPatient(null);
      }
    } catch (err) {
      setError('Error connecting to server. Please try again.');
      console.error('Error searching medical records:', err);
      setMedicalRecord(null);
      setSelectedPatient(null);
    } finally {
      setLoading(false);
    }
  };



  // Helper functions - same as internal system
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format time
  const formatTime = (timeString) => {
    return new Date(`1970-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header - Mirror internal system */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/')}
                className="text-gray-600 hover:text-gray-900 flex items-center gap-2 bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
                </svg>
                Back to Patient Portal
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Medical Records</h1>
                <p className="text-gray-600 mt-1">Search and view comprehensive patient medical records</p>
              </div>
            </div>
            <div className="bg-gradient-to-r from-blue-500 to-green-500 text-white px-4 py-2 rounded-lg">
              <span className="text-sm font-medium">🌐 Public Access Portal</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Search Section - Direct National ID search only */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">🔍 Search Medical Records</h2>
          <p className="text-gray-600 mb-4">Enter your National ID to access your medical records</p>

          <div className="flex gap-4">
            <div className="flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                placeholder="Enter your National ID..."
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={loading}
              className="bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:scale-105 disabled:opacity-50"
            >
              {loading ? '🔄 Searching...' : '🔍 Search'}
            </button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-red-700 font-medium">{error}</span>
              </div>
            </div>
          )}

          {/* Privacy Notice */}
          <div className="mt-4 bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <div>
                <h4 className="font-medium text-blue-900">Privacy Protected</h4>
                <p className="text-sm text-blue-700 mt-1">You can only access your own medical records using your National ID. This ensures your privacy and data security.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Medical Records Display - Mirror internal system */}
        {selectedPatient && medicalRecord && (
          <div className="space-y-8">
            {/* Patient Header */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-green-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold text-xl">
                      {medicalRecord.patient.firstName?.charAt(0)}{medicalRecord.patient.lastName?.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                      {medicalRecord.patient.firstName} {medicalRecord.patient.lastName}
                    </h2>
                    <p className="text-gray-600">National ID: {medicalRecord.patient.nationalId}</p>
                    <p className="text-gray-600">{medicalRecord.patient.email} • {medicalRecord.patient.phone}</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setSelectedPatient(null);
                    setMedicalRecord(null);
                    setSearchQuery('');
                    setError('');
                  }}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg transition-colors"
                >
                  🔙 New Search
                </button>
              </div>

              {/* Patient Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900">Date of Birth</h4>
                  <p className="text-blue-700">{formatDate(medicalRecord.patient.dateOfBirth)}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-900">Gender</h4>
                  <p className="text-green-700">{medicalRecord.patient.gender || 'Not specified'}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-red-900">Blood Type</h4>
                  <p className="text-red-700">{medicalRecord.patient.bloodType || 'Not specified'}</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-yellow-900">Allergies</h4>
                  <p className="text-yellow-700">{medicalRecord.patient.allergies || 'None'}</p>
                </div>
              </div>

              {/* Medical History */}
              {medicalRecord.patient.medicalHistory && (
                <div className="mt-6 bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Medical History</h4>
                  <p className="text-gray-700">{medicalRecord.patient.medicalHistory}</p>
                </div>
              )}
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Exams</h3>
                    <p className="text-3xl font-bold text-blue-600">{medicalRecord.summary.totalExams}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-blue-600 text-xl">🔬</span>
                  </div>
                </div>
                {medicalRecord.summary.lastExam && (
                  <p className="text-sm text-gray-500 mt-2">Last: {formatDate(medicalRecord.summary.lastExam)}</p>
                )}
              </div>

              <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Prescriptions</h3>
                    <p className="text-3xl font-bold text-green-600">{medicalRecord.summary.totalPrescriptions}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <span className="text-green-600 text-xl">💊</span>
                  </div>
                </div>
                {medicalRecord.summary.lastPrescription && (
                  <p className="text-sm text-gray-500 mt-2">Last: {formatDate(medicalRecord.summary.lastPrescription)}</p>
                )}
              </div>

              <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Appointments</h3>
                    <p className="text-3xl font-bold text-purple-600">{medicalRecord.summary.totalAppointments}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <span className="text-purple-600 text-xl">📅</span>
                  </div>
                </div>
                {medicalRecord.summary.lastAppointment && (
                  <p className="text-sm text-gray-500 mt-2">Last: {formatDate(medicalRecord.summary.lastAppointment)}</p>
                )}
              </div>

              <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Room Stays</h3>
                    <p className="text-3xl font-bold text-orange-600">{medicalRecord.summary.totalRoomAssignments}</p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <span className="text-orange-600 text-xl">🏥</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {[
                    { id: 'overview', label: '📋 Overview', icon: '📋' },
                    { id: 'exams', label: '🔬 Exams', icon: '🔬' },
                    { id: 'prescriptions', label: '💊 Prescriptions', icon: '💊' },
                    { id: 'appointments', label: '📅 Appointments', icon: '📅' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {/* Overview Tab */}
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900">📋 Medical Overview</h3>

                    {/* Recent Activity */}
                    <div className="grid gap-6">
                      {/* Recent Exams */}
                      {medicalRecord.exams.length > 0 && (
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-blue-900 mb-3">🔬 Recent Exams</h4>
                          <div className="space-y-2">
                            {medicalRecord.exams.slice(0, 3).map((exam) => (
                              <div key={exam.id} className="flex justify-between items-center">
                                <div>
                                  <span className="font-medium text-blue-800">{exam.examType}</span>
                                  <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(exam.status)}`}>
                                    {exam.status}
                                  </span>
                                </div>
                                <span className="text-blue-600 text-sm">{formatDate(exam.examDate)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Recent Prescriptions */}
                      {medicalRecord.prescriptions.length > 0 && (
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-green-900 mb-3">💊 Recent Prescriptions</h4>
                          <div className="space-y-2">
                            {medicalRecord.prescriptions.slice(0, 3).map((prescription) => (
                              <div key={prescription.id} className="flex justify-between items-center">
                                <div>
                                  <span className="font-medium text-green-800">{prescription.diagnosis}</span>
                                  <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(prescription.status)}`}>
                                    {prescription.status}
                                  </span>
                                </div>
                                <span className="text-green-600 text-sm">{formatDate(prescription.prescriptionDate)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Recent Appointments */}
                      {medicalRecord.appointments.length > 0 && (
                        <div className="bg-purple-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-purple-900 mb-3">📅 Recent Appointments</h4>
                          <div className="space-y-2">
                            {medicalRecord.appointments.slice(0, 3).map((appointment) => (
                              <div key={appointment.id} className="flex justify-between items-center">
                                <div>
                                  <span className="font-medium text-purple-800">{appointment.appointmentType}</span>
                                  <span className="text-purple-600 text-sm ml-2">
                                    with Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                                  </span>
                                </div>
                                <span className="text-purple-600 text-sm">{formatDate(appointment.appointmentDate)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Exams Tab */}
                {activeTab === 'exams' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900">🔬 Medical Exams</h3>

                    {medicalRecord.exams.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-gray-400 text-2xl">🔬</span>
                        </div>
                        <p className="text-gray-500">No exams recorded</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {medicalRecord.exams.map((exam) => (
                          <div key={exam.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-3">
                              <div>
                                <h4 className="font-semibold text-gray-900">{exam.examType}</h4>
                                <p className="text-sm text-gray-600">Date: {formatDate(exam.examDate)}</p>
                              </div>
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(exam.status)}`}>
                                {exam.status}
                              </span>
                            </div>
                            <div className="space-y-2">
                              <div>
                                <h5 className="font-medium text-gray-700">Results:</h5>
                                <p className="text-gray-600">{exam.results}</p>
                              </div>
                              {exam.notes && (
                                <div>
                                  <h5 className="font-medium text-gray-700">Notes:</h5>
                                  <p className="text-gray-600">{exam.notes}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Prescriptions Tab */}
                {activeTab === 'prescriptions' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900">💊 Prescriptions</h3>

                    {medicalRecord.prescriptions.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-gray-400 text-2xl">💊</span>
                        </div>
                        <p className="text-gray-500">No prescriptions recorded</p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {medicalRecord.prescriptions.map((prescription) => (
                          <div key={prescription.id} className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                            <div className="flex justify-between items-start mb-4">
                              <div>
                                <h4 className="font-semibold text-gray-900">Prescription #{prescription.prescriptionId}</h4>
                                <p className="text-sm text-gray-600">
                                  Date: {formatDate(prescription.prescriptionDate)} •
                                  Dr. {prescription.doctorFirstName} {prescription.doctorLastName}
                                </p>
                                {prescription.validUntil && (
                                  <p className="text-sm text-gray-600">Valid until: {formatDate(prescription.validUntil)}</p>
                                )}
                              </div>
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(prescription.status)}`}>
                                {prescription.status}
                              </span>
                            </div>

                            <div className="space-y-4">
                              <div>
                                <h5 className="font-medium text-gray-700">Diagnosis:</h5>
                                <p className="text-gray-600">{prescription.diagnosis}</p>
                              </div>

                              <div>
                                <h5 className="font-medium text-gray-700">Medications:</h5>
                                <div className="space-y-3 mt-2">
                                  {prescription.medications && prescription.medications.map((medication, index) => (
                                    <div key={index} className="bg-white p-3 rounded border">
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                        <div>
                                          <span className="font-medium text-gray-900">{medication.name}</span>
                                          <p className="text-sm text-gray-600">Dosage: {medication.dosage}</p>
                                        </div>
                                        <div>
                                          <p className="text-sm text-gray-600">Frequency: {medication.frequency}</p>
                                          <p className="text-sm text-gray-600">Duration: {medication.duration}</p>
                                        </div>
                                      </div>
                                      {medication.instructions && (
                                        <p className="text-sm text-gray-600 mt-2">
                                          <span className="font-medium">Instructions:</span> {medication.instructions}
                                        </p>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>

                              {prescription.notes && (
                                <div>
                                  <h5 className="font-medium text-gray-700">Additional Notes:</h5>
                                  <p className="text-gray-600">{prescription.notes}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Appointments Tab */}
                {activeTab === 'appointments' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-bold text-gray-900">📅 Appointments</h3>

                    {medicalRecord.appointments.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-gray-400 text-2xl">📅</span>
                        </div>
                        <p className="text-gray-500">No appointments recorded</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {medicalRecord.appointments.map((appointment) => (
                          <div key={appointment.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-3">
                              <div>
                                <h4 className="font-semibold text-gray-900">
                                  {appointment.appointmentType.replace('_', ' ').charAt(0).toUpperCase() +
                                   appointment.appointmentType.replace('_', ' ').slice(1)}
                                </h4>
                                <p className="text-sm text-gray-600">
                                  {formatDate(appointment.appointmentDate)} at {formatTime(appointment.appointmentTime)}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Dr. {appointment.doctorFirstName} {appointment.doctorLastName} - {appointment.specialization}
                                </p>
                              </div>
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)}`}>
                                {appointment.status.replace('_', ' ')}
                              </span>
                            </div>

                            <div className="space-y-2">
                              <div>
                                <h5 className="font-medium text-gray-700">Reason:</h5>
                                <p className="text-gray-600">{appointment.reason}</p>
                              </div>
                              {appointment.symptoms && (
                                <div>
                                  <h5 className="font-medium text-gray-700">Symptoms:</h5>
                                  <p className="text-gray-600">{appointment.symptoms}</p>
                                </div>
                              )}
                              {appointment.notes && (
                                <div>
                                  <h5 className="font-medium text-gray-700">Notes:</h5>
                                  <p className="text-gray-600">{appointment.notes}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PublicMedicalRecords;

import { useEffect } from 'react';

const PrintInvoice = ({ bill, onClose }) => {
  useEffect(() => {
    // Add print styles when component mounts
    const printStyles = `
      @media print {
        body * {
          visibility: hidden;
        }
        .print-area, .print-area * {
          visibility: visible;
        }
        .print-area {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }
        .no-print {
          display: none !important;
        }
        .page-break {
          page-break-after: always;
        }
      }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerText = printStyles;
    document.head.appendChild(styleSheet);
    
    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  const handlePrint = () => {
    window.print();
  };

  if (!bill) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Print Controls */}
        <div className="no-print flex items-center justify-between p-4 border-b">
          <h3 className="text-xl font-bold text-gray-900">Invoice Preview</h3>
          <div className="flex gap-3">
            <button
              onClick={handlePrint}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
              </svg>
              Print Invoice
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl px-2"
            >
              ×
            </button>
          </div>
        </div>

        {/* Printable Invoice */}
        <div className="print-area p-8 bg-white">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">HealthCarePro Hospital</h1>
                  <p className="text-gray-600">Medical Center & Healthcare Services</p>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                <p>Kigali, Rwanda</p>
                <p>Phone: +250 788 123 456</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
            <div className="text-right">
              <h2 className="text-3xl font-bold text-blue-900 mb-2">MEDICAL BILL</h2>
              <div className="text-sm text-gray-600">
                <p><strong>Bill No.:</strong> {bill.invoiceNumber}</p>
                <p><strong>Bill Date:</strong> {new Date(bill.createdAt).toLocaleDateString('en-GB')}</p>
                <p><strong>Due Date:</strong> {new Date(new Date(bill.createdAt).getTime() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}</p>
              </div>
            </div>
          </div>

          {/* Patient Information */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">Patient Information:</h3>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="font-semibold text-gray-900">Patient Name: {bill.patientName}</p>
                  {bill.patientNationalId && <p className="text-gray-600">National ID: {bill.patientNationalId}</p>}
                  {bill.patientPhone && <p className="text-gray-600">Phone: {bill.patientPhone}</p>}
                </div>
                <div>
                  {bill.patientEmail && <p className="text-gray-600">Email: {bill.patientEmail}</p>}
                  {bill.patientAddress && <p className="text-gray-600">Address: {bill.patientAddress}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Medical Services Table */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">Medical Services & Charges:</h3>
            <table className="w-full border-collapse border border-blue-300">
              <thead>
                <tr className="bg-blue-100">
                  <th className="border border-blue-300 px-4 py-3 text-left font-semibold text-blue-900">Medical Service/Treatment</th>
                  <th className="border border-blue-300 px-4 py-3 text-center font-semibold text-blue-900">Qty</th>
                  <th className="border border-blue-300 px-4 py-3 text-right font-semibold text-blue-900">Rate (RWF)</th>
                  <th className="border border-blue-300 px-4 py-3 text-right font-semibold text-blue-900">Amount (RWF)</th>
                </tr>
              </thead>
              <tbody>
                {bill.items.map((item, index) => (
                  <tr key={index} className="hover:bg-blue-25">
                    <td className="border border-blue-300 px-4 py-3">{item.description}</td>
                    <td className="border border-blue-300 px-4 py-3 text-center">{item.quantity}</td>
                    <td className="border border-blue-300 px-4 py-3 text-right">{item.unitPrice.toFixed(2)}</td>
                    <td className="border border-blue-300 px-4 py-3 text-right font-semibold">{item.total.toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Bill Summary */}
          <div className="flex justify-end mb-8">
            <div className="w-96">
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 className="text-lg font-semibold text-yellow-900 mb-3">Bill Summary</h4>
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Subtotal (Services):</span>
                  <span className="font-semibold">{bill.subtotal.toFixed(2)} RWF</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">VAT (18%):</span>
                  <span className="font-semibold">{bill.tax.toFixed(2)} RWF</span>
                </div>
                {bill.discount > 0 && (
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Discount/Insurance:</span>
                    <span className="font-semibold text-red-600">-{bill.discount.toFixed(2)} RWF</span>
                  </div>
                )}
                <div className="border-t border-yellow-300 pt-2 mb-2">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total Amount:</span>
                    <span className="text-blue-700">{bill.total.toFixed(2)} RWF</span>
                  </div>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Amount Paid:</span>
                  <span className="font-semibold text-green-600">{(bill.amountPaid || 0).toFixed(2)} RWF</span>
                </div>
                <div className="border-t border-yellow-300 pt-2">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Balance Due:</span>
                    <span className={`${(bill.total - (bill.amountPaid || 0)) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {(bill.total - (bill.amountPaid || 0)).toFixed(2)} RWF
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Payment Information</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p><strong>Payment Method:</strong> {bill.paymentMethod}</p>
                <p><strong>Payment Status:</strong> 
                  <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${
                    bill.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800' :
                    bill.paymentStatus === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {bill.paymentStatus}
                  </span>
                </p>
              </div>
            </div>
            {bill.notes && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Notes</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700">{bill.notes}</p>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t pt-8 text-center text-gray-600">
            <p className="mb-2">Thank you for choosing HealthCarePro!</p>
            <p className="text-sm">For any questions regarding this invoice, please contact <NAME_EMAIL></p>
            <p className="text-xs mt-4">This is a computer-generated invoice and does not require a signature.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrintInvoice;

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\components\\\\FlutterwavePayment.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useFlutterwave, closePaymentModal } from 'flutterwave-react-v3';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FlutterwavePayment = ({\n  amount,\n  customerEmail,\n  customerPhone,\n  customerName,\n  billId,\n  onSuccess,\n  onClose\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n\n  // Flutterwave configuration\n  const config = {\n    public_key: process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || 'FLWPUBK_TEST-SANDBOXDEMOKEY-X',\n    // Replace with your public key\n    tx_ref: `HEALTHCARE_${billId}_${Date.now()}`,\n    amount: amount,\n    currency: 'RWF',\n    payment_options: 'card,mobilemoney,ussd,bank_transfer',\n    customer: {\n      email: customerEmail,\n      phone_number: customerPhone,\n      name: customerName\n    },\n    customizations: {\n      title: 'Healthcare Payment',\n      description: `Payment for medical bill #${billId}`,\n      logo: '/logo.png' // Add your logo here\n    }\n  };\n  const handleFlutterPayment = useFlutterwave(config);\n  const initiatePayment = () => {\n    setLoading(true);\n    handleFlutterPayment({\n      callback: response => {\n        console.log('Flutterwave payment response:', response);\n        if (response.status === 'successful') {\n          // Payment successful\n          onSuccess({\n            transactionId: response.transaction_id,\n            flwRef: response.flw_ref,\n            amount: response.amount,\n            currency: response.currency,\n            status: response.status,\n            paymentType: response.payment_type,\n            chargedAmount: response.charged_amount\n          });\n        } else {\n          // Payment failed or cancelled\n          alert('Payment was not successful. Please try again.');\n        }\n        closePaymentModal();\n        setLoading(false);\n      },\n      onClose: () => {\n        console.log('Payment modal closed');\n        setLoading(false);\n        onClose();\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-8 h-8 text-white\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-bold text-gray-900 mb-2\",\n        children: \"Secure Payment with Flutterwave\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Complete your medical bill payment securely\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 rounded-xl p-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold text-gray-900 mb-3\",\n        children: \"Payment Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Patient:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: customerName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Bill ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: [\"#\", billId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Email:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: customerEmail\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Phone:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: customerPhone\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-2 mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-lg font-bold\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Amount to Pay:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-600\",\n              children: [amount.toLocaleString(), \" RWF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold text-gray-900 mb-3\",\n        children: \"Available Payment Methods\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 border border-blue-200 rounded-lg p-3 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-blue-600 font-medium text-sm\",\n            children: \"\\uD83D\\uDCB3 Card Payment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600\",\n            children: \"Visa, Mastercard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 border border-green-200 rounded-lg p-3 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-green-600 font-medium text-sm\",\n            children: \"\\uD83D\\uDCF1 Mobile Money\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600\",\n            children: \"MTN, Airtel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-purple-50 border border-purple-200 rounded-lg p-3 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-purple-600 font-medium text-sm\",\n            children: \"\\uD83C\\uDFE6 Bank Transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600\",\n            children: \"Direct transfer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-orange-50 border border-orange-200 rounded-lg p-3 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-orange-600 font-medium text-sm\",\n            children: \"\\uD83D\\uDCDE USSD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600\",\n            children: \"Dial code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-blue-500 mr-2 mt-0.5\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          strokeWidth: \"2\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"font-medium text-blue-900 text-sm\",\n            children: \"Secure Payment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-blue-700 mt-1\",\n            children: \"Your payment is secured by Flutterwave's industry-standard encryption and security measures.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-colors\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: initiatePayment,\n        disabled: loading,\n        className: \"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), \"Processing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 mr-2\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), \"Pay Now\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(FlutterwavePayment, \"+X13uxoys901es4b5vgxc/lxECg=\", false, function () {\n  return [useFlutterwave];\n});\n_c = FlutterwavePayment;\nexport default FlutterwavePayment;\nvar _c;\n$RefreshReg$(_c, \"FlutterwavePayment\");", "map": {"version": 3, "names": ["useState", "useFlutterwave", "closePaymentModal", "jsxDEV", "_jsxDEV", "FlutterwavePayment", "amount", "customerEmail", "customerPhone", "customerName", "billId", "onSuccess", "onClose", "_s", "loading", "setLoading", "config", "public_key", "process", "env", "REACT_APP_FLUTTERWAVE_PUBLIC_KEY", "tx_ref", "Date", "now", "currency", "payment_options", "customer", "email", "phone_number", "name", "customizations", "title", "description", "logo", "handleFlutterPayment", "initiatePayment", "callback", "response", "console", "log", "status", "transactionId", "transaction_id", "flwRef", "flw_ref", "paymentType", "payment_type", "chargedAmount", "charged_amount", "alert", "className", "children", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/components/FlutterwavePayment.jsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useFlutterwave, closePaymentModal } from 'flutterwave-react-v3';\n\nconst FlutterwavePayment = ({ \n  amount, \n  customerEmail, \n  customerPhone, \n  customerName, \n  billId, \n  onSuccess, \n  onClose \n}) => {\n  const [loading, setLoading] = useState(false);\n\n  // Flutterwave configuration\n  const config = {\n    public_key: process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || 'FLWPUBK_TEST-SANDBOXDEMOKEY-X', // Replace with your public key\n    tx_ref: `HEALTHCARE_${billId}_${Date.now()}`,\n    amount: amount,\n    currency: 'RWF',\n    payment_options: 'card,mobilemoney,ussd,bank_transfer',\n    customer: {\n      email: customerEmail,\n      phone_number: customerPhone,\n      name: customerName,\n    },\n    customizations: {\n      title: 'Healthcare Payment',\n      description: `Payment for medical bill #${billId}`,\n      logo: '/logo.png', // Add your logo here\n    },\n  };\n\n  const handleFlutterPayment = useFlutterwave(config);\n\n  const initiatePayment = () => {\n    setLoading(true);\n    \n    handleFlutterPayment({\n      callback: (response) => {\n        console.log('Flutterwave payment response:', response);\n        \n        if (response.status === 'successful') {\n          // Payment successful\n          onSuccess({\n            transactionId: response.transaction_id,\n            flwRef: response.flw_ref,\n            amount: response.amount,\n            currency: response.currency,\n            status: response.status,\n            paymentType: response.payment_type,\n            chargedAmount: response.charged_amount\n          });\n        } else {\n          // Payment failed or cancelled\n          alert('Payment was not successful. Please try again.');\n        }\n        \n        closePaymentModal();\n        setLoading(false);\n      },\n      onClose: () => {\n        console.log('Payment modal closed');\n        setLoading(false);\n        onClose();\n      },\n    });\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 p-6\">\n      <div className=\"text-center mb-6\">\n        <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Secure Payment with Flutterwave</h3>\n        <p className=\"text-gray-600\">Complete your medical bill payment securely</p>\n      </div>\n\n      {/* Payment Details */}\n      <div className=\"bg-gray-50 rounded-xl p-4 mb-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-3\">Payment Details</h4>\n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Patient:</span>\n            <span className=\"font-medium\">{customerName}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Bill ID:</span>\n            <span className=\"font-medium\">#{billId}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Email:</span>\n            <span className=\"font-medium\">{customerEmail}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">Phone:</span>\n            <span className=\"font-medium\">{customerPhone}</span>\n          </div>\n          <div className=\"border-t pt-2 mt-3\">\n            <div className=\"flex justify-between text-lg font-bold\">\n              <span>Amount to Pay:</span>\n              <span className=\"text-green-600\">{amount.toLocaleString()} RWF</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Payment Methods */}\n      <div className=\"mb-6\">\n        <h4 className=\"font-semibold text-gray-900 mb-3\">Available Payment Methods</h4>\n        <div className=\"grid grid-cols-2 gap-3\">\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3 text-center\">\n            <div className=\"text-blue-600 font-medium text-sm\">💳 Card Payment</div>\n            <div className=\"text-xs text-gray-600\">Visa, Mastercard</div>\n          </div>\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-3 text-center\">\n            <div className=\"text-green-600 font-medium text-sm\">📱 Mobile Money</div>\n            <div className=\"text-xs text-gray-600\">MTN, Airtel</div>\n          </div>\n          <div className=\"bg-purple-50 border border-purple-200 rounded-lg p-3 text-center\">\n            <div className=\"text-purple-600 font-medium text-sm\">🏦 Bank Transfer</div>\n            <div className=\"text-xs text-gray-600\">Direct transfer</div>\n          </div>\n          <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-3 text-center\">\n            <div className=\"text-orange-600 font-medium text-sm\">📞 USSD</div>\n            <div className=\"text-xs text-gray-600\">Dial code</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Security Notice */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6\">\n        <div className=\"flex items-start\">\n          <svg className=\"w-5 h-5 text-blue-500 mr-2 mt-0.5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n          </svg>\n          <div>\n            <h5 className=\"font-medium text-blue-900 text-sm\">Secure Payment</h5>\n            <p className=\"text-xs text-blue-700 mt-1\">\n              Your payment is secured by Flutterwave's industry-standard encryption and security measures.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex gap-3\">\n        <button\n          onClick={onClose}\n          className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-colors\"\n        >\n          Cancel\n        </button>\n        <button\n          onClick={initiatePayment}\n          disabled={loading}\n          className=\"flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50\"\n        >\n          {loading ? (\n            <div className=\"flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2\"></div>\n              Processing...\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-center\">\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n              </svg>\n              Pay Now\n            </div>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default FlutterwavePayment;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,cAAc,EAAEC,iBAAiB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,aAAa;EACbC,aAAa;EACbC,YAAY;EACZC,MAAM;EACNC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAMgB,MAAM,GAAG;IACbC,UAAU,EAAEC,OAAO,CAACC,GAAG,CAACC,gCAAgC,IAAI,+BAA+B;IAAE;IAC7FC,MAAM,EAAE,cAAcX,MAAM,IAAIY,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IAC5CjB,MAAM,EAAEA,MAAM;IACdkB,QAAQ,EAAE,KAAK;IACfC,eAAe,EAAE,qCAAqC;IACtDC,QAAQ,EAAE;MACRC,KAAK,EAAEpB,aAAa;MACpBqB,YAAY,EAAEpB,aAAa;MAC3BqB,IAAI,EAAEpB;IACR,CAAC;IACDqB,cAAc,EAAE;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,6BAA6BtB,MAAM,EAAE;MAClDuB,IAAI,EAAE,WAAW,CAAE;IACrB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGjC,cAAc,CAACe,MAAM,CAAC;EAEnD,MAAMmB,eAAe,GAAGA,CAAA,KAAM;IAC5BpB,UAAU,CAAC,IAAI,CAAC;IAEhBmB,oBAAoB,CAAC;MACnBE,QAAQ,EAAGC,QAAQ,IAAK;QACtBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,QAAQ,CAAC;QAEtD,IAAIA,QAAQ,CAACG,MAAM,KAAK,YAAY,EAAE;UACpC;UACA7B,SAAS,CAAC;YACR8B,aAAa,EAAEJ,QAAQ,CAACK,cAAc;YACtCC,MAAM,EAAEN,QAAQ,CAACO,OAAO;YACxBtC,MAAM,EAAE+B,QAAQ,CAAC/B,MAAM;YACvBkB,QAAQ,EAAEa,QAAQ,CAACb,QAAQ;YAC3BgB,MAAM,EAAEH,QAAQ,CAACG,MAAM;YACvBK,WAAW,EAAER,QAAQ,CAACS,YAAY;YAClCC,aAAa,EAAEV,QAAQ,CAACW;UAC1B,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAC,KAAK,CAAC,+CAA+C,CAAC;QACxD;QAEA/C,iBAAiB,CAAC,CAAC;QACnBa,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACDH,OAAO,EAAEA,CAAA,KAAM;QACb0B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnCxB,UAAU,CAAC,KAAK,CAAC;QACjBH,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACER,OAAA;IAAK8C,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBACxE/C,OAAA;MAAK8C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B/C,OAAA;QAAK8C,SAAS,EAAC,mHAAmH;QAAAC,QAAA,eAChI/C,OAAA;UAAK8C,SAAS,EAAC,oBAAoB;UAACE,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,OAAO,EAAC,WAAW;UAAAJ,QAAA,eACvG/C,OAAA;YAAMoD,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAAwF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN1D,OAAA;QAAI8C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,EAAC;MAA+B;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzF1D,OAAA;QAAG8C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAA2C;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,eAGN1D,OAAA;MAAK8C,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C/C,OAAA;QAAI8C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAe;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE1D,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/C,OAAA;UAAK8C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC/C,OAAA;YAAM8C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/C1D,OAAA;YAAM8C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE1C;UAAY;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC/C,OAAA;YAAM8C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/C1D,OAAA;YAAM8C,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,GAAC,EAACzC,MAAM;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC/C,OAAA;YAAM8C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C1D,OAAA;YAAM8C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE5C;UAAa;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC/C,OAAA;YAAM8C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C1D,OAAA;YAAM8C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE3C;UAAa;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC/C,OAAA;YAAK8C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD/C,OAAA;cAAA+C,QAAA,EAAM;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3B1D,OAAA;cAAM8C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAE7C,MAAM,CAACyD,cAAc,CAAC,CAAC,EAAC,MAAI;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1D,OAAA;MAAK8C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/C,OAAA;QAAI8C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAyB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/E1D,OAAA;QAAK8C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC/C,OAAA;UAAK8C,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3E/C,OAAA;YAAK8C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxE1D,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7E/C,OAAA;YAAK8C,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzE1D,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/E/C,OAAA;YAAK8C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3E1D,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACN1D,OAAA;UAAK8C,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/E/C,OAAA;YAAK8C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClE1D,OAAA;YAAK8C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1D,OAAA;MAAK8C,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE/C,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/C,OAAA;UAAK8C,SAAS,EAAC,mCAAmC;UAACE,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,WAAW,EAAC,GAAG;UAACC,OAAO,EAAC,WAAW;UAAAJ,QAAA,eACtH/C,OAAA;YAAMoD,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAAsG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3J,CAAC,eACN1D,OAAA;UAAA+C,QAAA,gBACE/C,OAAA;YAAI8C,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE1D,OAAA;YAAG8C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1D,OAAA;MAAK8C,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/C,OAAA;QACE4D,OAAO,EAAEpD,OAAQ;QACjBsC,SAAS,EAAC,yGAAyG;QAAAC,QAAA,EACpH;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1D,OAAA;QACE4D,OAAO,EAAE7B,eAAgB;QACzB8B,QAAQ,EAAEnD,OAAQ;QAClBoC,SAAS,EAAC,yOAAyO;QAAAC,QAAA,EAElPrC,OAAO,gBACNV,OAAA;UAAK8C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C/C,OAAA;YAAK8C,SAAS,EAAC;UAAmF;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,iBAE3G;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEN1D,OAAA;UAAK8C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C/C,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eACjG/C,OAAA;cAAMoD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAwF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7I,CAAC,WAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA/KIR,kBAAkB;EAAA,QA8BOJ,cAAc;AAAA;AAAAiE,EAAA,GA9BvC7D,kBAAkB;AAiLxB,eAAeA,kBAAkB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
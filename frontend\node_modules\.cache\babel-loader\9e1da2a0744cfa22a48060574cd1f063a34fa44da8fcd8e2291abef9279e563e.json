{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\PatientPortal.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport Login from '../components/Login';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PatientPortal = () => {\n  _s();\n  var _user$name, _medicalRecord$summar, _medicalRecord$summar2, _medicalRecord$summar3, _medicalRecord$summar4;\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [medicalRecord, setMedicalRecord] = useState(null);\n  const [showMedicalRecordsModal, setShowMedicalRecordsModal] = useState(false);\n  const [medicalRecordsLoading, setMedicalRecordsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount (only if logged in)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n    }\n    // Always fetch medicines for pharmacy browsing\n    fetchMedicines();\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = medicine => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => item.id === medicine.id ? {\n        ...item,\n        quantity: item.quantity + 1\n      } : item));\n    } else {\n      setCart([...cart, {\n        ...medicine,\n        quantity: 1\n      }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = medicineId => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => item.id === medicineId ? {\n        ...item,\n        quantity: quantity\n      } : item));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + item.price * item.quantity, 0);\n\n  // Fetch medical records for the logged-in patient\n  const fetchMedicalRecords = async () => {\n    if (!(user !== null && user !== void 0 && user.nationalId)) {\n      console.error('No user national ID available');\n      return;\n    }\n    setMedicalRecordsLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/medical-records/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicalRecord(data.data);\n        console.log('Medical records fetched successfully');\n      } else {\n        console.error('Failed to fetch medical records:', data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching medical records:', error);\n    } finally {\n      setMedicalRecordsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const openExamReports = () => {\n    // Create a modal or navigate to exam reports section\n    alert('Opening Exam Reports - Feature coming soon!');\n  };\n  const openAppointments = () => {\n    // Create a modal or navigate to appointments section\n    alert('Opening Appointments - Feature coming soon!');\n  };\n  const openPharmacy = () => {\n    // Create a modal or navigate to pharmacy section\n    alert('Opening Pharmacy - Feature coming soon!');\n  };\n  const openHealthRecords = async () => {\n    if (!user) {\n      setShowLoginModal(true);\n      return;\n    }\n    setShowMedicalRecordsModal(true);\n    await fetchMedicalRecords();\n  };\n\n  // Helper functions for medical records\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'in-progress':\n        return 'bg-blue-100 text-blue-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      case 'active':\n        return 'bg-green-100 text-green-800';\n      case 'expired':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-gray-50 to-blue-50 font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center\",\n      style: {\n        backgroundImage: \"url('/image/Screenshot 2025-04-21 200615.png')\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black/40\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 text-center max-w-5xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4\",\n            children: \"\\uD83C\\uDFE5 Your Personal Health Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-7xl font-extrabold mb-6 leading-tight\",\n          children: [\"Your Health,\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 block\",\n            children: \"Your Way\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\",\n          children: user ? `Welcome back, ${user.name}! Access your health records, manage appointments, and order medicines.` : 'Access your health records, book appointments, and manage your healthcare journey all in one place.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('services').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n              children: \"View My Health Records\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('pharmacy').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\",\n              children: \"Order Medicines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLoginModal(true),\n              className: \"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n              children: \"Sign In to Your Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('services').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\",\n              children: \"Explore Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      id: \"services\",\n      className: \"py-20 px-6 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4\",\n            children: \"Your Health Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n            children: \"Patient Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Access your personal health information and manage your healthcare journey\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => user ? openExamReports() : setShowLoginModal(true),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"My Exam Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"View and download your medical test results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), !user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-600 mt-2 font-medium\",\n                children: \"Login required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 27\n              }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: [examReports.length, \" reports available\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => user ? openAppointments() : setShowLoginModal(true),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"My Appointments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Book and manage your doctor appointments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), !user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-600 mt-2 font-medium\",\n                children: \"Login required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 27\n              }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: [appointments.length, \" appointments\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => openPharmacy(),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Online Pharmacy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Order medicines and health products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: [medicines.length, \" medicines available\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-orange-600 mt-1 font-medium\",\n                children: [cart.length, \" items in cart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => user ? openHealthRecords() : setShowLoginModal(true),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Health Records\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Access your complete medical history\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), !user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-600 mt-2 font-medium\",\n                children: \"Login required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 27\n              }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: \"Complete records available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Emergency Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"24/7 emergency medical assistance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-600 mt-2 font-medium\",\n                children: \"Call: 112\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Health Tips\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Daily health tips and wellness advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-orange-600 mt-2 font-medium\",\n                children: \"Updated daily\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-1 md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"Patient Portal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300 mb-6 max-w-md leading-relaxed\",\n              children: \"Your personal healthcare portal providing secure access to medical records, appointments, and health services.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Quick Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => user ? openExamReports() : setShowLoginModal(true),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"My Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => user ? openAppointments() : setShowLoginModal(true),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openPharmacy(),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Pharmacy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => user ? openHealthRecords() : setShowLoginModal(true),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Health Records\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Help Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Contact Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Emergency: 112\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-700 pt-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"\\xA9 2025 HealthCarePro Patient Portal. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), showMedicalRecordsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"My Medical Records\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowMedicalRecordsModal(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this), medicalRecordsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 text-gray-600\",\n              children: \"Loading your medical records...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 17\n          }, this) : medicalRecord ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-bold text-lg\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0)) || 'P'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-semibold text-gray-900\",\n                    children: user === null || user === void 0 ? void 0 : user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600\",\n                    children: [\"National ID: \", user === null || user === void 0 ? void 0 : user.nationalId]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 p-4 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-sm font-medium text-gray-600\",\n                      children: \"Exams\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-blue-600\",\n                      children: ((_medicalRecord$summar = medicalRecord.summary) === null || _medicalRecord$summar === void 0 ? void 0 : _medicalRecord$summar.totalExams) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-600\",\n                      children: \"\\uD83D\\uDD2C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 p-4 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-sm font-medium text-gray-600\",\n                      children: \"Prescriptions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-green-600\",\n                      children: ((_medicalRecord$summar2 = medicalRecord.summary) === null || _medicalRecord$summar2 === void 0 ? void 0 : _medicalRecord$summar2.totalPrescriptions) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-green-600\",\n                      children: \"\\uD83D\\uDC8A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 p-4 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-sm font-medium text-gray-600\",\n                      children: \"Appointments\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-purple-600\",\n                      children: ((_medicalRecord$summar3 = medicalRecord.summary) === null || _medicalRecord$summar3 === void 0 ? void 0 : _medicalRecord$summar3.totalAppointments) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-purple-600\",\n                      children: \"\\uD83D\\uDCC5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 p-4 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-sm font-medium text-gray-600\",\n                      children: \"Room Stays\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-orange-600\",\n                      children: ((_medicalRecord$summar4 = medicalRecord.summary) === null || _medicalRecord$summar4 === void 0 ? void 0 : _medicalRecord$summar4.totalRoomAssignments) || 0\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-orange-600\",\n                      children: \"\\uD83C\\uDFE5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"nav\", {\n                className: \"flex space-x-8\",\n                children: [{\n                  id: 'overview',\n                  label: '📋 Overview'\n                }, {\n                  id: 'exams',\n                  label: '🔬 Exams'\n                }, {\n                  id: 'prescriptions',\n                  label: '💊 Prescriptions'\n                }, {\n                  id: 'appointments',\n                  label: '📅 Appointments'\n                }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveTab(tab.id),\n                  className: `py-3 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                  children: tab.label\n                }, tab.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-h-[300px]\",\n              children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\uD83D\\uDCCB Medical Overview\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 25\n                }, this), medicalRecord.exams && medicalRecord.exams.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 p-4 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-medium text-blue-900 mb-3\",\n                    children: \"\\uD83D\\uDD2C Recent Exams\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: medicalRecord.exams.slice(0, 3).map(exam => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-blue-800\",\n                          children: exam.examType\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 532,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(exam.status)}`,\n                          children: exam.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 533,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-600 text-sm\",\n                        children: formatDate(exam.examDate)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 35\n                      }, this)]\n                    }, exam.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 27\n                }, this), medicalRecord.prescriptions && medicalRecord.prescriptions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 p-4 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"font-medium text-green-900 mb-3\",\n                    children: \"\\uD83D\\uDC8A Recent Prescriptions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: medicalRecord.prescriptions.slice(0, 3).map(prescription => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"font-medium text-green-800\",\n                          children: prescription.diagnosis\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 552,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(prescription.status)}`,\n                          children: prescription.status\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 553,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 551,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-600 text-sm\",\n                        children: formatDate(prescription.prescriptionDate)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 557,\n                        columnNumber: 35\n                      }, this)]\n                    }, prescription.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 23\n              }, this), activeTab === 'exams' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\uD83D\\uDD2C Medical Exams\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 25\n                }, this), !medicalRecord.exams || medicalRecord.exams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400 text-2xl\",\n                      children: \"\\uD83D\\uDD2C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"No exams recorded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: medicalRecord.exams.map(exam => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"font-semibold text-gray-900\",\n                          children: exam.examType\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 584,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600\",\n                          children: [\"Date: \", formatDate(exam.examDate)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 585,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 583,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(exam.status)}`,\n                        children: exam.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-medium text-gray-700\",\n                          children: \"Results:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 593,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-600\",\n                          children: exam.results\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 594,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 35\n                      }, this), exam.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-medium text-gray-700\",\n                          children: \"Notes:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 598,\n                          columnNumber: 39\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-600\",\n                          children: exam.notes\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 599,\n                          columnNumber: 39\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 597,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 33\n                    }, this)]\n                  }, exam.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 23\n              }, this), activeTab === 'prescriptions' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\uD83D\\uDC8A Prescriptions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 25\n                }, this), !medicalRecord.prescriptions || medicalRecord.prescriptions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400 text-2xl\",\n                      children: \"\\uD83D\\uDC8A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"No prescriptions recorded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: medicalRecord.prescriptions.map(prescription => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"font-semibold text-gray-900\",\n                          children: [\"Prescription #\", prescription.prescriptionId]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 628,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600\",\n                          children: [\"Date: \", formatDate(prescription.prescriptionDate), prescription.doctorFirstName && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [\" \\u2022 Dr. \", prescription.doctorFirstName, \" \", prescription.doctorLastName]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 629,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(prescription.status)}`,\n                        children: prescription.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 636,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-medium text-gray-700\",\n                          children: \"Diagnosis:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 642,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-600\",\n                          children: prescription.diagnosis\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 643,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 641,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-medium text-gray-700\",\n                          children: \"Medication:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 646,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-600\",\n                          children: prescription.medication\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 647,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-medium text-gray-700\",\n                          children: \"Dosage:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 650,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-600\",\n                          children: prescription.dosage\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 651,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 649,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-medium text-gray-700\",\n                          children: \"Instructions:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 654,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-gray-600\",\n                          children: prescription.instructions\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 655,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 640,\n                      columnNumber: 33\n                    }, this)]\n                  }, prescription.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 23\n              }, this), activeTab === 'appointments' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\uD83D\\uDCC5 Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 25\n                }, this), !medicalRecord.appointments || medicalRecord.appointments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-400 text-2xl\",\n                      children: \"\\uD83D\\uDCC5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"No appointments recorded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: medicalRecord.appointments.map(appointment => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"font-semibold text-gray-900\",\n                          children: appointment.appointmentType\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 683,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600\",\n                          children: [\"Date: \", formatDate(appointment.appointmentDate), appointment.doctorFirstName && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [\" \\u2022 Dr. \", appointment.doctorFirstName, \" \", appointment.doctorLastName]\n                          }, void 0, true)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 684,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 682,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)}`,\n                        children: appointment.status\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 33\n                    }, this), appointment.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"font-medium text-gray-700\",\n                        children: \"Notes:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 697,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600\",\n                        children: appointment.notes\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 696,\n                      columnNumber: 35\n                    }, this)]\n                  }, appointment.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 31\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400 text-2xl\",\n                children: \"\\uD83D\\uDCCB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"No medical records found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 9\n    }, this), showLoginModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Patient Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLoginModal(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Login, {\n            onLogin: () => {\n              setShowLoginModal(false);\n              // Redirect to home page after successful login\n              navigate('/home');\n            },\n            onClose: () => setShowLoginModal(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 724,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientPortal, \"7aWYBaQETtzBDIdS0H3CpJiai/c=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = PatientPortal;\nexport default PatientPortal;\nvar _c;\n$RefreshReg$(_c, \"PatientPortal\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "useAuth", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PatientPortal", "_s", "_user$name", "_medicalRecord$summar", "_medicalRecord$summar2", "_medicalRecord$summar3", "_medicalRecord$summar4", "user", "navigate", "showLoginModal", "setShowLoginModal", "examReports", "setExamReports", "appointments", "setAppointments", "medicines", "setMedicines", "cart", "setCart", "medicalRecord", "setMedicalRecord", "showMedicalRecordsModal", "setShowMedicalRecordsModal", "medicalRecordsLoading", "setMedicalRecordsLoading", "activeTab", "setActiveTab", "API_BASE_URL", "nationalId", "fetchExamReports", "fetchAppointments", "fetchMedicines", "response", "fetch", "data", "json", "success", "error", "console", "addToCart", "medicine", "existingItem", "find", "item", "id", "map", "quantity", "removeFromCart", "medicineId", "filter", "updateCartQuantity", "cartTotal", "reduce", "total", "price", "fetchMedicalRecords", "log", "message", "openExamReports", "alert", "openAppointments", "openPharmacy", "openHealthRecords", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getStatusColor", "status", "className", "children", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "document", "getElementById", "scrollIntoView", "behavior", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "length", "href", "char<PERSON>t", "summary", "totalExams", "totalPrescriptions", "totalAppointments", "totalRoomAssignments", "label", "tab", "exams", "slice", "exam", "examType", "examDate", "prescriptions", "prescription", "diagnosis", "prescriptionDate", "results", "notes", "prescriptionId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medication", "dosage", "instructions", "appointment", "appointmentType", "appointmentDate", "onLogin", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/pages/PatientPortal.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport Login from '../components/Login';\n\nconst PatientPortal = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [medicalRecord, setMedicalRecord] = useState(null);\n  const [showMedicalRecordsModal, setShowMedicalRecordsModal] = useState(false);\n  const [medicalRecordsLoading, setMedicalRecordsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount (only if logged in)\n  useEffect(() => {\n    if (user?.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n    }\n    // Always fetch medicines for pharmacy browsing\n    fetchMedicines();\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = (medicine) => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => \n        item.id === medicine.id \n          ? { ...item, quantity: item.quantity + 1 }\n          : item\n      ));\n    } else {\n      setCart([...cart, { ...medicine, quantity: 1 }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = (medicineId) => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => \n        item.id === medicineId \n          ? { ...item, quantity: quantity }\n          : item\n      ));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);\n\n  // Fetch medical records for the logged-in patient\n  const fetchMedicalRecords = async () => {\n    if (!user?.nationalId) {\n      console.error('No user national ID available');\n      return;\n    }\n\n    setMedicalRecordsLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/medical-records/${user.nationalId}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setMedicalRecord(data.data);\n        console.log('Medical records fetched successfully');\n      } else {\n        console.error('Failed to fetch medical records:', data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching medical records:', error);\n    } finally {\n      setMedicalRecordsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const openExamReports = () => {\n    // Create a modal or navigate to exam reports section\n    alert('Opening Exam Reports - Feature coming soon!');\n  };\n\n  const openAppointments = () => {\n    // Create a modal or navigate to appointments section\n    alert('Opening Appointments - Feature coming soon!');\n  };\n\n  const openPharmacy = () => {\n    // Create a modal or navigate to pharmacy section\n    alert('Opening Pharmacy - Feature coming soon!');\n  };\n\n  const openHealthRecords = async () => {\n    if (!user) {\n      setShowLoginModal(true);\n      return;\n    }\n\n    setShowMedicalRecordsModal(true);\n    await fetchMedicalRecords();\n  };\n\n  // Helper functions for medical records\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return 'bg-green-100 text-green-800';\n      case 'pending': return 'bg-yellow-100 text-yellow-800';\n      case 'in-progress': return 'bg-blue-100 text-blue-800';\n      case 'cancelled': return 'bg-red-100 text-red-800';\n      case 'active': return 'bg-green-100 text-green-800';\n      case 'expired': return 'bg-gray-100 text-gray-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"bg-gradient-to-br from-gray-50 to-blue-50 font-sans\">\n      {/* Hero Section */}\n      <section\n        className=\"relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center\"\n        style={{\n          backgroundImage: \"url('/image/Screenshot 2025-04-21 200615.png')\"\n        }}\n      >\n        <div className=\"absolute inset-0 bg-black/40\"></div>\n        <div className=\"relative z-10 text-center max-w-5xl mx-auto\">\n          <div className=\"mb-6\">\n            <span className=\"inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4\">\n              🏥 Your Personal Health Portal\n            </span>\n          </div>\n          <h1 className=\"text-4xl md:text-7xl font-extrabold mb-6 leading-tight\">\n            Your Health,\n            <span className=\"text-yellow-400 block\">Your Way</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\">\n            {user \n              ? `Welcome back, ${user.name}! Access your health records, manage appointments, and order medicines.`\n              : 'Access your health records, book appointments, and manage your healthcare journey all in one place.'\n            }\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            {user ? (\n              <>\n                <button \n                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n                >\n                  View My Health Records\n                </button>\n                <button \n                  onClick={() => document.getElementById('pharmacy').scrollIntoView({ behavior: 'smooth' })}\n                  className=\"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\"\n                >\n                  Order Medicines\n                </button>\n              </>\n            ) : (\n              <>\n                <button \n                  onClick={() => setShowLoginModal(true)}\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n                >\n                  Sign In to Your Portal\n                </button>\n                <button \n                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}\n                  className=\"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\"\n                >\n                  Explore Services\n                </button>\n              </>\n            )}\n          </div>\n        </div>\n      </section>\n\n      {/* Patient Services Section */}\n      <section id=\"services\" className=\"py-20 px-6 bg-white\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4\">\n              Your Health Services\n            </span>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">Patient Services</h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Access your personal health information and manage your healthcare journey\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* Exam Reports */}\n            <div \n              onClick={() => user ? openExamReports() : setShowLoginModal(true)}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">My Exam Reports</h3>\n                <p className=\"text-sm text-gray-600\">View and download your medical test results</p>\n                {!user && <p className=\"text-xs text-blue-600 mt-2 font-medium\">Login required</p>}\n                {user && <p className=\"text-xs text-green-600 mt-2 font-medium\">{examReports.length} reports available</p>}\n              </div>\n            </div>\n\n            {/* Appointments */}\n            <div \n              onClick={() => user ? openAppointments() : setShowLoginModal(true)}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">My Appointments</h3>\n                <p className=\"text-sm text-gray-600\">Book and manage your doctor appointments</p>\n                {!user && <p className=\"text-xs text-blue-600 mt-2 font-medium\">Login required</p>}\n                {user && <p className=\"text-xs text-green-600 mt-2 font-medium\">{appointments.length} appointments</p>}\n              </div>\n            </div>\n\n            {/* Online Pharmacy */}\n            <div\n              onClick={() => openPharmacy()}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Online Pharmacy</h3>\n                <p className=\"text-sm text-gray-600\">Order medicines and health products</p>\n                <p className=\"text-xs text-green-600 mt-2 font-medium\">{medicines.length} medicines available</p>\n                {cart.length > 0 && (\n                  <p className=\"text-xs text-orange-600 mt-1 font-medium\">{cart.length} items in cart</p>\n                )}\n              </div>\n            </div>\n\n            {/* Health Records */}\n            <div\n              onClick={() => user ? openHealthRecords() : setShowLoginModal(true)}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Health Records</h3>\n                <p className=\"text-sm text-gray-600\">Access your complete medical history</p>\n                {!user && <p className=\"text-xs text-blue-600 mt-2 font-medium\">Login required</p>}\n                {user && <p className=\"text-xs text-green-600 mt-2 font-medium\">Complete records available</p>}\n              </div>\n            </div>\n\n            {/* Emergency Contact */}\n            <div className=\"group cursor-pointer\">\n              <div className=\"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Emergency Contact</h3>\n                <p className=\"text-sm text-gray-600\">24/7 emergency medical assistance</p>\n                <p className=\"text-xs text-red-600 mt-2 font-medium\">Call: 112</p>\n              </div>\n            </div>\n\n            {/* Health Tips */}\n            <div className=\"group cursor-pointer\">\n              <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Health Tips</h3>\n                <p className=\"text-sm text-gray-600\">Daily health tips and wellness advice</p>\n                <p className=\"text-xs text-orange-600 mt-2 font-medium\">Updated daily</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-2xl font-bold\">Patient Portal</h3>\n              </div>\n              <p className=\"text-gray-300 mb-6 max-w-md leading-relaxed\">\n                Your personal healthcare portal providing secure access to medical records, appointments, and health services.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Quick Access</h4>\n              <ul className=\"space-y-2\">\n                <li><button onClick={() => user ? openExamReports() : setShowLoginModal(true)} className=\"text-gray-300 hover:text-white transition-colors\">My Reports</button></li>\n                <li><button onClick={() => user ? openAppointments() : setShowLoginModal(true)} className=\"text-gray-300 hover:text-white transition-colors\">Appointments</button></li>\n                <li><button onClick={() => openPharmacy()} className=\"text-gray-300 hover:text-white transition-colors\">Pharmacy</button></li>\n                <li><button onClick={() => user ? openHealthRecords() : setShowLoginModal(true)} className=\"text-gray-300 hover:text-white transition-colors\">Health Records</button></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Support</h4>\n              <ul className=\"space-y-2\">\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Help Center</a></li>\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Contact Us</a></li>\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Privacy Policy</a></li>\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Emergency: 112</a></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-700 pt-8 text-center\">\n            <p className=\"text-gray-400\">\n              &copy; 2025 HealthCarePro Patient Portal. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n\n      {/* Medical Records Modal */}\n      {showMedicalRecordsModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">My Medical Records</h3>\n                <button\n                  onClick={() => setShowMedicalRecordsModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              {medicalRecordsLoading ? (\n                <div className=\"flex items-center justify-center py-12\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600\"></div>\n                  <span className=\"ml-3 text-gray-600\">Loading your medical records...</span>\n                </div>\n              ) : medicalRecord ? (\n                <div className=\"space-y-6\">\n                  {/* Patient Info Header */}\n                  <div className=\"bg-blue-50 rounded-lg p-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4\">\n                        <span className=\"text-white font-bold text-lg\">\n                          {user?.name?.charAt(0) || 'P'}\n                        </span>\n                      </div>\n                      <div>\n                        <h4 className=\"text-lg font-semibold text-gray-900\">{user?.name}</h4>\n                        <p className=\"text-gray-600\">National ID: {user?.nationalId}</p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Summary Cards */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                    <div className=\"bg-white border border-gray-200 p-4 rounded-lg\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h5 className=\"text-sm font-medium text-gray-600\">Exams</h5>\n                          <p className=\"text-2xl font-bold text-blue-600\">{medicalRecord.summary?.totalExams || 0}</p>\n                        </div>\n                        <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                          <span className=\"text-blue-600\">🔬</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white border border-gray-200 p-4 rounded-lg\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h5 className=\"text-sm font-medium text-gray-600\">Prescriptions</h5>\n                          <p className=\"text-2xl font-bold text-green-600\">{medicalRecord.summary?.totalPrescriptions || 0}</p>\n                        </div>\n                        <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                          <span className=\"text-green-600\">💊</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white border border-gray-200 p-4 rounded-lg\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h5 className=\"text-sm font-medium text-gray-600\">Appointments</h5>\n                          <p className=\"text-2xl font-bold text-purple-600\">{medicalRecord.summary?.totalAppointments || 0}</p>\n                        </div>\n                        <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n                          <span className=\"text-purple-600\">📅</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"bg-white border border-gray-200 p-4 rounded-lg\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h5 className=\"text-sm font-medium text-gray-600\">Room Stays</h5>\n                          <p className=\"text-2xl font-bold text-orange-600\">{medicalRecord.summary?.totalRoomAssignments || 0}</p>\n                        </div>\n                        <div className=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\">\n                          <span className=\"text-orange-600\">🏥</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Tab Navigation */}\n                  <div className=\"border-b border-gray-200\">\n                    <nav className=\"flex space-x-8\">\n                      {[\n                        { id: 'overview', label: '📋 Overview' },\n                        { id: 'exams', label: '🔬 Exams' },\n                        { id: 'prescriptions', label: '💊 Prescriptions' },\n                        { id: 'appointments', label: '📅 Appointments' }\n                      ].map((tab) => (\n                        <button\n                          key={tab.id}\n                          onClick={() => setActiveTab(tab.id)}\n                          className={`py-3 px-1 border-b-2 font-medium text-sm ${\n                            activeTab === tab.id\n                              ? 'border-blue-500 text-blue-600'\n                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                          }`}\n                        >\n                          {tab.label}\n                        </button>\n                      ))}\n                    </nav>\n                  </div>\n\n                  {/* Tab Content */}\n                  <div className=\"min-h-[300px]\">\n                    {/* Overview Tab */}\n                    {activeTab === 'overview' && (\n                      <div className=\"space-y-4\">\n                        <h4 className=\"text-lg font-semibold text-gray-900\">📋 Medical Overview</h4>\n\n                        {/* Recent Exams */}\n                        {medicalRecord.exams && medicalRecord.exams.length > 0 && (\n                          <div className=\"bg-blue-50 p-4 rounded-lg\">\n                            <h5 className=\"font-medium text-blue-900 mb-3\">🔬 Recent Exams</h5>\n                            <div className=\"space-y-2\">\n                              {medicalRecord.exams.slice(0, 3).map((exam) => (\n                                <div key={exam.id} className=\"flex justify-between items-center\">\n                                  <div>\n                                    <span className=\"font-medium text-blue-800\">{exam.examType}</span>\n                                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(exam.status)}`}>\n                                      {exam.status}\n                                    </span>\n                                  </div>\n                                  <span className=\"text-blue-600 text-sm\">{formatDate(exam.examDate)}</span>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n\n                        {/* Recent Prescriptions */}\n                        {medicalRecord.prescriptions && medicalRecord.prescriptions.length > 0 && (\n                          <div className=\"bg-green-50 p-4 rounded-lg\">\n                            <h5 className=\"font-medium text-green-900 mb-3\">💊 Recent Prescriptions</h5>\n                            <div className=\"space-y-2\">\n                              {medicalRecord.prescriptions.slice(0, 3).map((prescription) => (\n                                <div key={prescription.id} className=\"flex justify-between items-center\">\n                                  <div>\n                                    <span className=\"font-medium text-green-800\">{prescription.diagnosis}</span>\n                                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(prescription.status)}`}>\n                                      {prescription.status}\n                                    </span>\n                                  </div>\n                                  <span className=\"text-green-600 text-sm\">{formatDate(prescription.prescriptionDate)}</span>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n\n                    {/* Exams Tab */}\n                    {activeTab === 'exams' && (\n                      <div className=\"space-y-4\">\n                        <h4 className=\"text-lg font-semibold text-gray-900\">🔬 Medical Exams</h4>\n\n                        {!medicalRecord.exams || medicalRecord.exams.length === 0 ? (\n                          <div className=\"text-center py-8\">\n                            <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\n                              <span className=\"text-gray-400 text-2xl\">🔬</span>\n                            </div>\n                            <p className=\"text-gray-500\">No exams recorded</p>\n                          </div>\n                        ) : (\n                          <div className=\"space-y-4\">\n                            {medicalRecord.exams.map((exam) => (\n                              <div key={exam.id} className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n                                <div className=\"flex justify-between items-start mb-3\">\n                                  <div>\n                                    <h5 className=\"font-semibold text-gray-900\">{exam.examType}</h5>\n                                    <p className=\"text-sm text-gray-600\">Date: {formatDate(exam.examDate)}</p>\n                                  </div>\n                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(exam.status)}`}>\n                                    {exam.status}\n                                  </span>\n                                </div>\n                                <div className=\"space-y-2\">\n                                  <div>\n                                    <h6 className=\"font-medium text-gray-700\">Results:</h6>\n                                    <p className=\"text-gray-600\">{exam.results}</p>\n                                  </div>\n                                  {exam.notes && (\n                                    <div>\n                                      <h6 className=\"font-medium text-gray-700\">Notes:</h6>\n                                      <p className=\"text-gray-600\">{exam.notes}</p>\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    )}\n\n                    {/* Prescriptions Tab */}\n                    {activeTab === 'prescriptions' && (\n                      <div className=\"space-y-4\">\n                        <h4 className=\"text-lg font-semibold text-gray-900\">💊 Prescriptions</h4>\n\n                        {!medicalRecord.prescriptions || medicalRecord.prescriptions.length === 0 ? (\n                          <div className=\"text-center py-8\">\n                            <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\n                              <span className=\"text-gray-400 text-2xl\">💊</span>\n                            </div>\n                            <p className=\"text-gray-500\">No prescriptions recorded</p>\n                          </div>\n                        ) : (\n                          <div className=\"space-y-4\">\n                            {medicalRecord.prescriptions.map((prescription) => (\n                              <div key={prescription.id} className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n                                <div className=\"flex justify-between items-start mb-3\">\n                                  <div>\n                                    <h5 className=\"font-semibold text-gray-900\">Prescription #{prescription.prescriptionId}</h5>\n                                    <p className=\"text-sm text-gray-600\">\n                                      Date: {formatDate(prescription.prescriptionDate)}\n                                      {prescription.doctorFirstName && (\n                                        <> • Dr. {prescription.doctorFirstName} {prescription.doctorLastName}</>\n                                      )}\n                                    </p>\n                                  </div>\n                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(prescription.status)}`}>\n                                    {prescription.status}\n                                  </span>\n                                </div>\n                                <div className=\"space-y-2\">\n                                  <div>\n                                    <h6 className=\"font-medium text-gray-700\">Diagnosis:</h6>\n                                    <p className=\"text-gray-600\">{prescription.diagnosis}</p>\n                                  </div>\n                                  <div>\n                                    <h6 className=\"font-medium text-gray-700\">Medication:</h6>\n                                    <p className=\"text-gray-600\">{prescription.medication}</p>\n                                  </div>\n                                  <div>\n                                    <h6 className=\"font-medium text-gray-700\">Dosage:</h6>\n                                    <p className=\"text-gray-600\">{prescription.dosage}</p>\n                                  </div>\n                                  <div>\n                                    <h6 className=\"font-medium text-gray-700\">Instructions:</h6>\n                                    <p className=\"text-gray-600\">{prescription.instructions}</p>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    )}\n\n                    {/* Appointments Tab */}\n                    {activeTab === 'appointments' && (\n                      <div className=\"space-y-4\">\n                        <h4 className=\"text-lg font-semibold text-gray-900\">📅 Appointments</h4>\n\n                        {!medicalRecord.appointments || medicalRecord.appointments.length === 0 ? (\n                          <div className=\"text-center py-8\">\n                            <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\n                              <span className=\"text-gray-400 text-2xl\">📅</span>\n                            </div>\n                            <p className=\"text-gray-500\">No appointments recorded</p>\n                          </div>\n                        ) : (\n                          <div className=\"space-y-4\">\n                            {medicalRecord.appointments.map((appointment) => (\n                              <div key={appointment.id} className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n                                <div className=\"flex justify-between items-start mb-3\">\n                                  <div>\n                                    <h5 className=\"font-semibold text-gray-900\">{appointment.appointmentType}</h5>\n                                    <p className=\"text-sm text-gray-600\">\n                                      Date: {formatDate(appointment.appointmentDate)}\n                                      {appointment.doctorFirstName && (\n                                        <> • Dr. {appointment.doctorFirstName} {appointment.doctorLastName}</>\n                                      )}\n                                    </p>\n                                  </div>\n                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)}`}>\n                                    {appointment.status}\n                                  </span>\n                                </div>\n                                {appointment.notes && (\n                                  <div>\n                                    <h6 className=\"font-medium text-gray-700\">Notes:</h6>\n                                    <p className=\"text-gray-600\">{appointment.notes}</p>\n                                  </div>\n                                )}\n                              </div>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <span className=\"text-gray-400 text-2xl\">📋</span>\n                  </div>\n                  <p className=\"text-gray-500\">No medical records found</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Login Modal */}\n      {showLoginModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Patient Login</h3>\n                <button\n                  onClick={() => setShowLoginModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <Login\n                onLogin={() => {\n                  setShowLoginModal(false);\n                  // Redirect to home page after successful login\n                  navigate('/home');\n                }}\n                onClose={() => setShowLoginModal(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PatientPortal;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC1B,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0B,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8B,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACgC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAMoC,YAAY,GAAG,2BAA2B;;EAEhD;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIe,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,UAAU,EAAE;MACpBC,gBAAgB,CAAC,CAAC;MAClBC,iBAAiB,CAAC,CAAC;IACrB;IACA;IACAC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACxB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMsB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,kBAAkBpB,IAAI,CAACqB,UAAU,EAAE,CAAC;MAChF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBxB,cAAc,CAACsB,IAAI,CAACA,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMP,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,yBAAyBpB,IAAI,CAACqB,UAAU,EAAE,CAAC;MACvF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBtB,eAAe,CAACoB,IAAI,CAACA,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMN,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,WAAW,CAAC;MACxD,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBpB,YAAY,CAACkB,IAAI,CAACA,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAME,SAAS,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,YAAY,GAAGxB,IAAI,CAACyB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,CAAC;IAC/D,IAAIH,YAAY,EAAE;MAChBvB,OAAO,CAACD,IAAI,CAAC4B,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,GACnB;QAAE,GAAGD,IAAI;QAAEG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG;MAAE,CAAC,GACxCH,IACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGuB,QAAQ;QAAEM,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrC9B,OAAO,CAACD,IAAI,CAACgC,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKI,UAAU,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACF,UAAU,EAAEF,QAAQ,KAAK;IACnD,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBC,cAAc,CAACC,UAAU,CAAC;IAC5B,CAAC,MAAM;MACL9B,OAAO,CAACD,IAAI,CAAC4B,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKI,UAAU,GAClB;QAAE,GAAGL,IAAI;QAAEG,QAAQ,EAAEA;MAAS,CAAC,GAC/BH,IACN,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAGlC,IAAI,CAACmC,MAAM,CAAC,CAACC,KAAK,EAAEV,IAAI,KAAKU,KAAK,GAAIV,IAAI,CAACW,KAAK,GAAGX,IAAI,CAACG,QAAS,EAAE,CAAC,CAAC;;EAEvF;EACA,MAAMS,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,EAAChD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,UAAU,GAAE;MACrBU,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF;IAEAb,wBAAwB,CAAC,IAAI,CAAC;IAC9B,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,oBAAoBpB,IAAI,CAACqB,UAAU,EAAE,CAAC;MAClF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBhB,gBAAgB,CAACc,IAAI,CAACA,IAAI,CAAC;QAC3BI,OAAO,CAACkB,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,MAAM;QACLlB,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEH,IAAI,CAACuB,OAAO,CAAC;MACjE;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRb,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAC,KAAK,CAAC,6CAA6C,CAAC;EACtD,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACAD,KAAK,CAAC,6CAA6C,CAAC;EACtD,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAF,KAAK,CAAC,yCAAyC,CAAC;EAClD,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACvD,IAAI,EAAE;MACTG,iBAAiB,CAAC,IAAI,CAAC;MACvB;IACF;IAEAY,0BAA0B,CAAC,IAAI,CAAC;IAChC,MAAMiC,mBAAmB,CAAC,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMQ,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,6BAA6B;MACtD,KAAK,SAAS;QAAE,OAAO,+BAA+B;MACtD,KAAK,aAAa;QAAE,OAAO,2BAA2B;MACtD,KAAK,WAAW;QAAE,OAAO,yBAAyB;MAClD,KAAK,QAAQ;QAAE,OAAO,6BAA6B;MACnD,KAAK,SAAS;QAAE,OAAO,2BAA2B;MAClD;QAAS,OAAO,2BAA2B;IAC7C;EACF,CAAC;EAED,oBACE1E,OAAA;IAAK2E,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElE5E,OAAA;MACE2E,SAAS,EAAC,mFAAmF;MAC7FE,KAAK,EAAE;QACLC,eAAe,EAAE;MACnB,CAAE;MAAAF,QAAA,gBAEF5E,OAAA;QAAK2E,SAAS,EAAC;MAA8B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpDlF,OAAA;QAAK2E,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D5E,OAAA;UAAK2E,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5E,OAAA;YAAM2E,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAAC;UAE5G;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlF,OAAA;UAAI2E,SAAS,EAAC,wDAAwD;UAAAC,QAAA,GAAC,cAErE,eAAA5E,OAAA;YAAM2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACLlF,OAAA;UAAG2E,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EACpFlE,IAAI,GACD,iBAAiBA,IAAI,CAACyE,IAAI,yEAAyE,GACnG;QAAqG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAExG,CAAC,eACJlF,OAAA;UAAK2E,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAC5DlE,IAAI,gBACHV,OAAA,CAAAE,SAAA;YAAA0E,QAAA,gBACE5E,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Fb,SAAS,EAAC,4NAA4N;cAAAC,QAAA,EACvO;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Fb,SAAS,EAAC,iJAAiJ;cAAAC,QAAA,EAC5J;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHlF,OAAA,CAAAE,SAAA;YAAA0E,QAAA,gBACE5E,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMvE,iBAAiB,CAAC,IAAI,CAAE;cACvC8D,SAAS,EAAC,4NAA4N;cAAAC,QAAA,EACvO;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Fb,SAAS,EAAC,iJAAiJ;cAAAC,QAAA,EAC5J;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlF,OAAA;MAAS+C,EAAE,EAAC,UAAU;MAAC4B,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACpD5E,OAAA;QAAK2E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5E,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5E,OAAA;YAAM2E,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EAAC;UAE3G;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPlF,OAAA;YAAI2E,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFlF,OAAA;YAAG2E,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENlF,OAAA;UAAK2E,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBAEnE5E,OAAA;YACEoF,OAAO,EAAEA,CAAA,KAAM1E,IAAI,GAAGmD,eAAe,CAAC,CAAC,GAAGhD,iBAAiB,CAAC,IAAI,CAAE;YAClE8D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC5E,OAAA;cAAK2E,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/K5E,OAAA;gBAAK2E,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7K5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsH;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjElF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA2C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACnF,CAACxE,IAAI,iBAAIV,OAAA;gBAAG2E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFxE,IAAI,iBAAIV,OAAA;gBAAG2E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAE9D,WAAW,CAACkF,MAAM,EAAC,oBAAkB;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YACEoF,OAAO,EAAEA,CAAA,KAAM1E,IAAI,GAAGqD,gBAAgB,CAAC,CAAC,GAAGlD,iBAAiB,CAAC,IAAI,CAAE;YACnE8D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC5E,OAAA;cAAK2E,SAAS,EAAC,qKAAqK;cAAAC,QAAA,gBAClL5E,OAAA;gBAAK2E,SAAS,EAAC,kKAAkK;gBAAAC,QAAA,eAC/K5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAwF;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjElF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAwC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAChF,CAACxE,IAAI,iBAAIV,OAAA;gBAAG2E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFxE,IAAI,iBAAIV,OAAA;gBAAG2E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAE5D,YAAY,CAACgF,MAAM,EAAC,eAAa;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YACEoF,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAAC,CAAE;YAC9BW,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC5E,OAAA;cAAK2E,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrL5E,OAAA;gBAAK2E,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjL5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5T;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjElF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5ElF,OAAA;gBAAG2E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAE1D,SAAS,CAAC8E,MAAM,EAAC,sBAAoB;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAChG9D,IAAI,CAAC4E,MAAM,GAAG,CAAC,iBACdhG,OAAA;gBAAG2E,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,GAAExD,IAAI,CAAC4E,MAAM,EAAC,gBAAc;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACvF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YACEoF,OAAO,EAAEA,CAAA,KAAM1E,IAAI,GAAGuD,iBAAiB,CAAC,CAAC,GAAGpD,iBAAiB,CAAC,IAAI,CAAE;YACpE8D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC5E,OAAA;cAAK2E,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/K5E,OAAA;gBAAK2E,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7K5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsH;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChElF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAoC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAC5E,CAACxE,IAAI,iBAAIV,OAAA;gBAAG2E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFxE,IAAI,iBAAIV,OAAA;gBAAG2E,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAA0B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAK2E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC5E,OAAA;cAAK2E,SAAS,EAAC,+JAA+J;cAAAC,QAAA,gBAC5K5E,OAAA;gBAAK2E,SAAS,EAAC,8JAA8J;gBAAAC,QAAA,eAC3K5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuN;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5Q;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1ElF,OAAA;gBAAG2E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAK2E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC5E,OAAA;cAAK2E,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrL5E,OAAA;gBAAK2E,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjL5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAA6H;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7DlF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9ElF,OAAA;gBAAG2E,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlF,OAAA;MAAQ2E,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAClF5E,OAAA;QAAK2E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5E,OAAA;UAAK2E,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD5E,OAAA;YAAK2E,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC5E,OAAA;cAAK2E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC5E,OAAA;gBAAK2E,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,eACrH5E,OAAA;kBAAK2E,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG5E,OAAA;oBAAM6F,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAA6H;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAI2E,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNlF,OAAA;cAAG2E,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENlF,OAAA;YAAA4E,QAAA,gBACE5E,OAAA;cAAI2E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DlF,OAAA;cAAI2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvB5E,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAQoF,OAAO,EAAEA,CAAA,KAAM1E,IAAI,GAAGmD,eAAe,CAAC,CAAC,GAAGhD,iBAAiB,CAAC,IAAI,CAAE;kBAAC8D,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpKlF,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAQoF,OAAO,EAAEA,CAAA,KAAM1E,IAAI,GAAGqD,gBAAgB,CAAC,CAAC,GAAGlD,iBAAiB,CAAC,IAAI,CAAE;kBAAC8D,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvKlF,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAQoF,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAAC,CAAE;kBAACW,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9HlF,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAQoF,OAAO,EAAEA,CAAA,KAAM1E,IAAI,GAAGuD,iBAAiB,CAAC,CAAC,GAAGpD,iBAAiB,CAAC,IAAI,CAAE;kBAAC8D,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENlF,OAAA;YAAA4E,QAAA,gBACE5E,OAAA;cAAI2E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDlF,OAAA;cAAI2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvB5E,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAGiG,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGlF,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAGiG,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChGlF,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAGiG,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpGlF,OAAA;gBAAA4E,QAAA,eAAI5E,OAAA;kBAAGiG,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK2E,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxD5E,OAAA;YAAG2E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGR1D,uBAAuB,iBACtBxB,OAAA;MAAK2E,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5E,OAAA;QAAK2E,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F5E,OAAA;UAAK2E,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5E,OAAA;YAAK2E,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF5E,OAAA;cAAI2E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxElF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAM3D,0BAA0B,CAAC,KAAK,CAAE;cACjDkD,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELxD,qBAAqB,gBACpB1B,OAAA;YAAK2E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD5E,OAAA;cAAK2E,SAAS,EAAC;YAA8E;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpGlF,OAAA;cAAM2E,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAA+B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,GACJ5D,aAAa,gBACftB,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB5E,OAAA;cAAK2E,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACxC5E,OAAA;gBAAK2E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5E,OAAA;kBAAK2E,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,eACvF5E,OAAA;oBAAM2E,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EAC3C,CAAAlE,IAAI,aAAJA,IAAI,wBAAAL,UAAA,GAAJK,IAAI,CAAEyE,IAAI,cAAA9E,UAAA,uBAAVA,UAAA,CAAY6F,MAAM,CAAC,CAAC,CAAC,KAAI;kBAAG;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNlF,OAAA;kBAAA4E,QAAA,gBACE5E,OAAA;oBAAI2E,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAElE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrElF,OAAA;oBAAG2E,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAC,eAAa,EAAClE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,UAAU;kBAAA;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlF,OAAA;cAAK2E,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnE5E,OAAA;gBAAK2E,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,eAC7D5E,OAAA;kBAAK2E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5E,OAAA;oBAAA4E,QAAA,gBACE5E,OAAA;sBAAI2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5DlF,OAAA;sBAAG2E,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAE,EAAAtE,qBAAA,GAAAgB,aAAa,CAAC6E,OAAO,cAAA7F,qBAAA,uBAArBA,qBAAA,CAAuB8F,UAAU,KAAI;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzF,CAAC,eACNlF,OAAA;oBAAK2E,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChF5E,OAAA;sBAAM2E,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlF,OAAA;gBAAK2E,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,eAC7D5E,OAAA;kBAAK2E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5E,OAAA;oBAAA4E,QAAA,gBACE5E,OAAA;sBAAI2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpElF,OAAA;sBAAG2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE,EAAArE,sBAAA,GAAAe,aAAa,CAAC6E,OAAO,cAAA5F,sBAAA,uBAArBA,sBAAA,CAAuB8F,kBAAkB,KAAI;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC,eACNlF,OAAA;oBAAK2E,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjF5E,OAAA;sBAAM2E,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlF,OAAA;gBAAK2E,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,eAC7D5E,OAAA;kBAAK2E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5E,OAAA;oBAAA4E,QAAA,gBACE5E,OAAA;sBAAI2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnElF,OAAA;sBAAG2E,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAE,EAAApE,sBAAA,GAAAc,aAAa,CAAC6E,OAAO,cAAA3F,sBAAA,uBAArBA,sBAAA,CAAuB8F,iBAAiB,KAAI;oBAAC;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC,eACNlF,OAAA;oBAAK2E,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF5E,OAAA;sBAAM2E,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlF,OAAA;gBAAK2E,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,eAC7D5E,OAAA;kBAAK2E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChD5E,OAAA;oBAAA4E,QAAA,gBACE5E,OAAA;sBAAI2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjElF,OAAA;sBAAG2E,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAE,EAAAnE,sBAAA,GAAAa,aAAa,CAAC6E,OAAO,cAAA1F,sBAAA,uBAArBA,sBAAA,CAAuB8F,oBAAoB,KAAI;oBAAC;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrG,CAAC,eACNlF,OAAA;oBAAK2E,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF5E,OAAA;sBAAM2E,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlF,OAAA;cAAK2E,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvC5E,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5B,CACC;kBAAE7B,EAAE,EAAE,UAAU;kBAAEyD,KAAK,EAAE;gBAAc,CAAC,EACxC;kBAAEzD,EAAE,EAAE,OAAO;kBAAEyD,KAAK,EAAE;gBAAW,CAAC,EAClC;kBAAEzD,EAAE,EAAE,eAAe;kBAAEyD,KAAK,EAAE;gBAAmB,CAAC,EAClD;kBAAEzD,EAAE,EAAE,cAAc;kBAAEyD,KAAK,EAAE;gBAAkB,CAAC,CACjD,CAACxD,GAAG,CAAEyD,GAAG,iBACRzG,OAAA;kBAEEoF,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC4E,GAAG,CAAC1D,EAAE,CAAE;kBACpC4B,SAAS,EAAE,4CACT/C,SAAS,KAAK6E,GAAG,CAAC1D,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,EAC/E;kBAAA6B,QAAA,EAEF6B,GAAG,CAACD;gBAAK,GARLC,GAAG,CAAC1D,EAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASL,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlF,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,GAE3BhD,SAAS,KAAK,UAAU,iBACvB5B,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAI2E,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAG3E5D,aAAa,CAACoF,KAAK,IAAIpF,aAAa,CAACoF,KAAK,CAACV,MAAM,GAAG,CAAC,iBACpDhG,OAAA;kBAAK2E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC5E,OAAA;oBAAI2E,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnElF,OAAA;oBAAK2E,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvBtD,aAAa,CAACoF,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAAE4D,IAAI,iBACxC5G,OAAA;sBAAmB2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAC9D5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAM2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAEgC,IAAI,CAACC;wBAAQ;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAClElF,OAAA;0BAAM2E,SAAS,EAAE,uCAAuCF,cAAc,CAACmC,IAAI,CAAClC,MAAM,CAAC,EAAG;0BAAAE,QAAA,EACnFgC,IAAI,CAAClC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNlF,OAAA;wBAAM2E,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAEV,UAAU,CAAC0C,IAAI,CAACE,QAAQ;sBAAC;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,GAPlE0B,IAAI,CAAC7D,EAAE;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQZ,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA5D,aAAa,CAACyF,aAAa,IAAIzF,aAAa,CAACyF,aAAa,CAACf,MAAM,GAAG,CAAC,iBACpEhG,OAAA;kBAAK2E,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC5E,OAAA;oBAAI2E,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5ElF,OAAA;oBAAK2E,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvBtD,aAAa,CAACyF,aAAa,CAACJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAAEgE,YAAY,iBACxDhH,OAAA;sBAA2B2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBACtE5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAM2E,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAEoC,YAAY,CAACC;wBAAS;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC5ElF,OAAA;0BAAM2E,SAAS,EAAE,uCAAuCF,cAAc,CAACuC,YAAY,CAACtC,MAAM,CAAC,EAAG;0BAAAE,QAAA,EAC3FoC,YAAY,CAACtC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNlF,OAAA;wBAAM2E,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,EAAEV,UAAU,CAAC8C,YAAY,CAACE,gBAAgB;sBAAC;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,GAPnF8B,YAAY,CAACjE,EAAE;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQpB,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAGAtD,SAAS,KAAK,OAAO,iBACpB5B,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAI2E,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAExE,CAAC5D,aAAa,CAACoF,KAAK,IAAIpF,aAAa,CAACoF,KAAK,CAACV,MAAM,KAAK,CAAC,gBACvDhG,OAAA;kBAAK2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B5E,OAAA;oBAAK2E,SAAS,EAAC,kFAAkF;oBAAAC,QAAA,eAC/F5E,OAAA;sBAAM2E,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNlF,OAAA;oBAAG2E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,gBAENlF,OAAA;kBAAK2E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBtD,aAAa,CAACoF,KAAK,CAAC1D,GAAG,CAAE4D,IAAI,iBAC5B5G,OAAA;oBAAmB2E,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBAC7E5E,OAAA;sBAAK2E,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAEgC,IAAI,CAACC;wBAAQ;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAChElF,OAAA;0BAAG2E,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAC,QAAM,EAACV,UAAU,CAAC0C,IAAI,CAACE,QAAQ,CAAC;wBAAA;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE,CAAC,eACNlF,OAAA;wBAAM2E,SAAS,EAAE,8CAA8CF,cAAc,CAACmC,IAAI,CAAClC,MAAM,CAAC,EAAG;wBAAAE,QAAA,EAC1FgC,IAAI,CAAClC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNlF,OAAA;sBAAK2E,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACvDlF,OAAA;0BAAG2E,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEgC,IAAI,CAACO;wBAAO;0BAAApC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC,EACL0B,IAAI,CAACQ,KAAK,iBACTpH,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAC;wBAAM;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrDlF,OAAA;0BAAG2E,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEgC,IAAI,CAACQ;wBAAK;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA,GArBE0B,IAAI,CAAC7D,EAAE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAsBZ,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAGAtD,SAAS,KAAK,eAAe,iBAC5B5B,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAI2E,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAExE,CAAC5D,aAAa,CAACyF,aAAa,IAAIzF,aAAa,CAACyF,aAAa,CAACf,MAAM,KAAK,CAAC,gBACvEhG,OAAA;kBAAK2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B5E,OAAA;oBAAK2E,SAAS,EAAC,kFAAkF;oBAAAC,QAAA,eAC/F5E,OAAA;sBAAM2E,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNlF,OAAA;oBAAG2E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,gBAENlF,OAAA;kBAAK2E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBtD,aAAa,CAACyF,aAAa,CAAC/D,GAAG,CAAEgE,YAAY,iBAC5ChH,OAAA;oBAA2B2E,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBACrF5E,OAAA;sBAAK2E,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,GAAC,gBAAc,EAACoC,YAAY,CAACK,cAAc;wBAAA;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC5FlF,OAAA;0BAAG2E,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAC,QAC7B,EAACV,UAAU,CAAC8C,YAAY,CAACE,gBAAgB,CAAC,EAC/CF,YAAY,CAACM,eAAe,iBAC3BtH,OAAA,CAAAE,SAAA;4BAAA0E,QAAA,GAAE,cAAO,EAACoC,YAAY,CAACM,eAAe,EAAC,GAAC,EAACN,YAAY,CAACO,cAAc;0BAAA,eAAG,CACxE;wBAAA;0BAAAxC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNlF,OAAA;wBAAM2E,SAAS,EAAE,8CAA8CF,cAAc,CAACuC,YAAY,CAACtC,MAAM,CAAC,EAAG;wBAAAE,QAAA,EAClGoC,YAAY,CAACtC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNlF,OAAA;sBAAK2E,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxB5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACzDlF,OAAA;0BAAG2E,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEoC,YAAY,CAACC;wBAAS;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,eACNlF,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAC;wBAAW;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1DlF,OAAA;0BAAG2E,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEoC,YAAY,CAACQ;wBAAU;0BAAAzC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACNlF,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACtDlF,OAAA;0BAAG2E,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEoC,YAAY,CAACS;wBAAM;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC,eACNlF,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAC;wBAAa;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC5DlF,OAAA;0BAAG2E,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEoC,YAAY,CAACU;wBAAY;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAhCE8B,YAAY,CAACjE,EAAE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiCpB,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAGAtD,SAAS,KAAK,cAAc,iBAC3B5B,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAI2E,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAEvE,CAAC5D,aAAa,CAACN,YAAY,IAAIM,aAAa,CAACN,YAAY,CAACgF,MAAM,KAAK,CAAC,gBACrEhG,OAAA;kBAAK2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B5E,OAAA;oBAAK2E,SAAS,EAAC,kFAAkF;oBAAAC,QAAA,eAC/F5E,OAAA;sBAAM2E,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNlF,OAAA;oBAAG2E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAwB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,gBAENlF,OAAA;kBAAK2E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBtD,aAAa,CAACN,YAAY,CAACgC,GAAG,CAAE2E,WAAW,iBAC1C3H,OAAA;oBAA0B2E,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBACpF5E,OAAA;sBAAK2E,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpD5E,OAAA;wBAAA4E,QAAA,gBACE5E,OAAA;0BAAI2E,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAE+C,WAAW,CAACC;wBAAe;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC9ElF,OAAA;0BAAG2E,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,GAAC,QAC7B,EAACV,UAAU,CAACyD,WAAW,CAACE,eAAe,CAAC,EAC7CF,WAAW,CAACL,eAAe,iBAC1BtH,OAAA,CAAAE,SAAA;4BAAA0E,QAAA,GAAE,cAAO,EAAC+C,WAAW,CAACL,eAAe,EAAC,GAAC,EAACK,WAAW,CAACJ,cAAc;0BAAA,eAAG,CACtE;wBAAA;0BAAAxC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNlF,OAAA;wBAAM2E,SAAS,EAAE,8CAA8CF,cAAc,CAACkD,WAAW,CAACjD,MAAM,CAAC,EAAG;wBAAAE,QAAA,EACjG+C,WAAW,CAACjD;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EACLyC,WAAW,CAACP,KAAK,iBAChBpH,OAAA;sBAAA4E,QAAA,gBACE5E,OAAA;wBAAI2E,SAAS,EAAC,2BAA2B;wBAAAC,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrDlF,OAAA;wBAAG2E,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAE+C,WAAW,CAACP;sBAAK;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CACN;kBAAA,GApBOyC,WAAW,CAAC5E,EAAE;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBnB,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENlF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B5E,OAAA;cAAK2E,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F5E,OAAA;gBAAM2E,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNlF,OAAA;cAAG2E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAtE,cAAc,iBACbZ,OAAA;MAAK2E,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5E,OAAA;QAAK2E,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9D5E,OAAA;UAAK2E,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5E,OAAA;YAAK2E,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF5E,OAAA;cAAI2E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnElF,OAAA;cACEoF,OAAO,EAAEA,CAAA,KAAMvE,iBAAiB,CAAC,KAAK,CAAE;cACxC8D,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlF,OAAA,CAACF,KAAK;YACJgI,OAAO,EAAEA,CAAA,KAAM;cACbjH,iBAAiB,CAAC,KAAK,CAAC;cACxB;cACAF,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAE;YACFoH,OAAO,EAAEA,CAAA,KAAMlH,iBAAiB,CAAC,KAAK;UAAE;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CAzuBID,aAAa;EAAA,QACAN,OAAO,EACPD,WAAW;AAAA;AAAAoI,EAAA,GAFxB7H,aAAa;AA2uBnB,eAAeA,aAAa;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
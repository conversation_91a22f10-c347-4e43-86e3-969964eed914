{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useState, useEffect, useRef } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Help from './pages/Help';\nimport MedicalRecords from './pages/MedicalRecords';\nimport Pharmacy from './pages/Pharmacy';\nimport Doctor from './pages/Doctor';\nimport Patients from './pages/Patients';\nimport HospitalTransfer from './pages/HospitalTransfer';\nimport Exams from './pages/Exams';\nimport Messages from './pages/Messages';\nimport Room from './pages/Room';\nimport Appointment from './pages/Appointment';\nimport AdminDashboard from './pages/AdminDashboard';\nimport BillingPage from './pages/BillingPage';\nimport PatientPortal from './pages/PatientPortal';\nimport PublicMedicalRecords from './pages/PublicMedicalRecords';\nimport PublicPharmacy from './pages/PublicPharmacy';\nimport PublicExamResults from './pages/PublicExamResults';\nimport PublicAppointment from './pages/PublicAppointment';\nimport './index.css';\n\n// Header component with navigation\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Header() {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showLogin, setShowLogin] = useState(false);\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout,\n    isAuthenticated,\n    isAdmin,\n    login\n  } = useAuth();\n  const profileDropdownRef = useRef(null);\n\n  // Handle successful login\n  const handleLogin = userData => {\n    login(userData);\n    setShowLogin(false);\n    // Redirect to home page after successful login\n    navigate('/home');\n  };\n\n  // Handle logout with redirect to patient portal\n  const handleLogout = () => {\n    logout(() => {\n      navigate('/'); // Redirect to patient portal\n    });\n    setShowProfileDropdown(false);\n  };\n\n  // Check if we're on the patient portal or any public page\n  const isPatientPortal = location.pathname === '/';\n  const isPublicPage = location.pathname.startsWith('/public-') || location.pathname === '/';\n\n  // Close profile dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {\n        setShowProfileDropdown(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  const getLinkClasses = (path, isMobile = false) => {\n    const baseClasses = isMobile ? \"block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent\" : \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative\";\n    const activeClasses = isMobile ? \"text-blue-700 bg-blue-50 border-blue-600 shadow-sm\" : \"text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200\";\n    const inactiveClasses = isMobile ? \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300\" : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm\";\n    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl font-bold text-gray-800 tracking-wide\",\n                  children: [\"HEALTH\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600\",\n                    children: \"CARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 -mt-1\",\n                  children: \"Portal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden md:flex space-x-8\",\n          children: isPublicPage ? /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: getLinkClasses('/'),\n            children: \"Patient Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this) : (/* Show hospital system navigation only for authenticated staff */\n          user && user.role !== 'patient' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/home\",\n              className: getLinkClasses('/home'),\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: getLinkClasses('/about'),\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/appointment\",\n              className: getLinkClasses('/appointment'),\n              children: \"Appointment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/billing\",\n              className: getLinkClasses('/billing'),\n              children: \"Billing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/help\",\n              className: getLinkClasses('/help'),\n              children: \"Help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/help\",\n              className: getLinkClasses('/help'),\n              children: \"Help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: getLinkClasses('/about'),\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            ref: profileDropdownRef,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowProfileDropdown(!showProfileDropdown),\n              className: \"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-sm font-bold\",\n                  children: user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 capitalize\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: `w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`,\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M19 9l-7 7-7-7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), showProfileDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-4 py-3 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-white font-bold\",\n                      children: user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-900\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600 capitalize font-medium\",\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add profile page navigation here if needed\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"View Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    // Add settings page navigation here if needed\n                    setShowProfileDropdown(false);\n                  },\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), isAdmin() && /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/admin-dashboard\",\n                  onClick: () => setShowProfileDropdown(false),\n                  className: \"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Admin Dashboard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t border-gray-100 pt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: \"2\",\n                      d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Logout\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowLogin(true),\n            className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), showLogin && /*#__PURE__*/_jsxDEV(Login, {\n          onLogin: handleLogin,\n          onClose: () => setShowLogin(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n            className: \"text-gray-700 hover:text-blue-600 p-2\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\",\n        children: [isPublicPage ? /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: getLinkClasses('/', true),\n          onClick: () => setIsMobileMenuOpen(false),\n          children: \"Patient Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 15\n        }, this) : (/* Show hospital system navigation only for authenticated staff */\n        user && user.role !== 'patient' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/home\",\n            className: getLinkClasses('/home', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: getLinkClasses('/about', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/appointment\",\n            className: getLinkClasses('/appointment', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"Appointment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/billing\",\n            className: getLinkClasses('/billing', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"Billing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/help\",\n            className: getLinkClasses('/help', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"Help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/help\",\n            className: getLinkClasses('/help', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"Help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/about\",\n            className: getLinkClasses('/about', true),\n            onClick: () => setIsMobileMenuOpen(false),\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4 pb-3 border-t border-gray-200 mx-4\",\n          children: isAuthenticated() ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 capitalize\",\n                children: user.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                handleLogout();\n                setIsMobileMenuOpen(false);\n              },\n              className: \"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setShowLogin(true);\n              setIsMobileMenuOpen(false);\n            },\n            className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"6bAmn5nfDmj7c6xmaO8z6oswvyA=\", false, function () {\n  return [useLocation, useNavigate, useAuth];\n});\n_c = Header;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(PatientPortal, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/public-medical-records\",\n              element: /*#__PURE__*/_jsxDEV(PublicMedicalRecords, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 64\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/public-pharmacy\",\n              element: /*#__PURE__*/_jsxDEV(PublicPharmacy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 57\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/public-exam-results\",\n              element: /*#__PURE__*/_jsxDEV(PublicExamResults, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 61\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/public-appointment\",\n              element: /*#__PURE__*/_jsxDEV(PublicAppointment, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 60\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/home\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/help\",\n              element: /*#__PURE__*/_jsxDEV(Help, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/messages\",\n              element: /*#__PURE__*/_jsxDEV(Messages, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/patients\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'nurse'],\n                children: /*#__PURE__*/_jsxDEV(Patients, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/medical-records\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(MedicalRecords, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/exams\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/pharmacy\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'nurse', 'staff'],\n                children: /*#__PURE__*/_jsxDEV(Pharmacy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/billing\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'staff'],\n                children: /*#__PURE__*/_jsxDEV(BillingPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/patient-portal\",\n              element: /*#__PURE__*/_jsxDEV(PatientPortal, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 28\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/appointment\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor', 'staff'],\n                children: /*#__PURE__*/_jsxDEV(Appointment, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/room\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(Room, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/hospital-transfer\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                allowedRoles: ['admin', 'doctor'],\n                children: /*#__PURE__*/_jsxDEV(HospitalTransfer, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/doctor\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requireAdmin: true,\n                children: /*#__PURE__*/_jsxDEV(Doctor, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin-dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requireAdmin: true,\n                children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "useLocation", "useNavigate", "useState", "useEffect", "useRef", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "ProtectedRoute", "Home", "About", "Help", "MedicalRecords", "Pharmacy", "Doctor", "Patients", "HospitalTransfer", "<PERSON><PERSON>", "Messages", "Room", "Appointment", "AdminDashboard", "BillingPage", "PatientPortal", "PublicMedicalRecords", "PublicPharmacy", "PublicExamResults", "PublicAppointment", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Header", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "showLogin", "setShow<PERSON><PERSON>in", "showProfileDropdown", "setShowProfileDropdown", "location", "navigate", "user", "logout", "isAuthenticated", "isAdmin", "login", "profileDropdownRef", "handleLogin", "userData", "handleLogout", "isPatientPortal", "pathname", "isPublicPage", "startsWith", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "isActive", "path", "getLinkClasses", "isMobile", "baseClasses", "activeClasses", "inactiveClasses", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "role", "ref", "onClick", "firstName", "char<PERSON>t", "toUpperCase", "name", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "email", "onLogin", "onClose", "_c", "App", "element", "allowedRoles", "requireAdmin", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useState, useEffect, useRef } from 'react';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport About from './pages/About';\nimport Help from './pages/Help';\nimport MedicalRecords from './pages/MedicalRecords';\nimport Pharmacy from './pages/Pharmacy';\nimport Doctor from './pages/Doctor';\nimport Patients from './pages/Patients';\nimport HospitalTransfer from './pages/HospitalTransfer';\nimport Exams from './pages/Exams';\nimport Messages from './pages/Messages';\nimport Room from './pages/Room';\nimport Appointment from './pages/Appointment';\nimport AdminDashboard from './pages/AdminDashboard';\nimport BillingPage from './pages/BillingPage';\nimport PatientPortal from './pages/PatientPortal';\nimport PublicMedicalRecords from './pages/PublicMedicalRecords';\nimport PublicPharmacy from './pages/PublicPharmacy';\nimport PublicExamResults from './pages/PublicExamResults';\nimport PublicAppointment from './pages/PublicAppointment';\nimport './index.css';\n\n// Header component with navigation\nfunction Header() {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [showLogin, setShowLogin] = useState(false);\n  const [showProfileDropdown, setShowProfileDropdown] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout, isAuthenticated, isAdmin, login } = useAuth();\n  const profileDropdownRef = useRef(null);\n\n  // Handle successful login\n  const handleLogin = (userData) => {\n    login(userData);\n    setShowLogin(false);\n    // Redirect to home page after successful login\n    navigate('/home');\n  };\n\n  // Handle logout with redirect to patient portal\n  const handleLogout = () => {\n    logout(() => {\n      navigate('/'); // Redirect to patient portal\n    });\n    setShowProfileDropdown(false);\n  };\n\n  // Check if we're on the patient portal or any public page\n  const isPatientPortal = location.pathname === '/';\n  const isPublicPage = location.pathname.startsWith('/public-') || location.pathname === '/';\n\n  // Close profile dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target)) {\n        setShowProfileDropdown(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  const getLinkClasses = (path, isMobile = false) => {\n    const baseClasses = isMobile\n      ? \"block px-4 py-3 text-base font-medium transition-all duration-300 border-l-4 border-transparent\"\n      : \"px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 relative\";\n    const activeClasses = isMobile\n      ? \"text-blue-700 bg-blue-50 border-blue-600 shadow-sm\"\n      : \"text-blue-700 bg-blue-50 shadow-sm ring-2 ring-blue-200\";\n    const inactiveClasses = isMobile\n      ? \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300\"\n      : \"text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm\";\n\n    return `${baseClasses} ${isActive(path) ? activeClasses : inactiveClasses}`;\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-800 tracking-wide\">\n                    HEALTH<span className=\"text-blue-600\">CARE</span>\n                  </h1>\n                  <p className=\"text-xs text-gray-500 -mt-1\">Portal</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {/* Hide navigation completely on public pages */}\n            {isPublicPage ? (\n              <Link to=\"/\" className={getLinkClasses('/')}>\n                Patient Portal\n              </Link>\n            ) : (\n              /* Show hospital system navigation only for authenticated staff */\n              user && user.role !== 'patient' ? (\n                <>\n                  <Link to=\"/home\" className={getLinkClasses('/home')}>\n                    Home\n                  </Link>\n                  <Link to=\"/about\" className={getLinkClasses('/about')}>\n                    About\n                  </Link>\n                  <Link to=\"/appointment\" className={getLinkClasses('/appointment')}>\n                    Appointment\n                  </Link>\n                  <Link to=\"/billing\" className={getLinkClasses('/billing')}>\n                    Billing\n                  </Link>\n                  <Link to=\"/help\" className={getLinkClasses('/help')}>\n                    Help\n                  </Link>\n                </>\n              ) : (\n                <>\n                  <Link to=\"/help\" className={getLinkClasses('/help')}>\n                    Help\n                  </Link>\n                  <Link to=\"/about\" className={getLinkClasses('/about')}>\n                    About\n                  </Link>\n                </>\n              )\n            )}\n          </nav>\n\n          {/* Profile/Login Section */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated() ? (\n              <div className=\"relative\" ref={profileDropdownRef}>\n                <button\n                  onClick={() => setShowProfileDropdown(!showProfileDropdown)}\n                  className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 px-4 py-2 rounded-lg transition-all duration-300 border border-blue-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-bold\">\n                      {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}\n                    </span>\n                  </div>\n                  <div className=\"text-left\">\n                    <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">{user.role}</div>\n                  </div>\n                  <svg className={`w-4 h-4 text-gray-500 transition-transform ${showProfileDropdown ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* Profile Dropdown */}\n                {showProfileDropdown && (\n                  <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\">\n                    <div className=\"px-4 py-3 border-b border-gray-100\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center\">\n                          <span className=\"text-white font-bold\">\n                            {user.firstName ? user.firstName.charAt(0).toUpperCase() : 'U'}\n                          </span>\n                        </div>\n                        <div>\n                          <div className=\"font-medium text-gray-900\">{user.name}</div>\n                          <div className=\"text-sm text-gray-500\">{user.email}</div>\n                          <div className=\"text-xs text-blue-600 capitalize font-medium\">{user.role}</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"py-2\">\n                      <button\n                        onClick={() => {\n                          // Add profile page navigation here if needed\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                        <span>View Profile</span>\n                      </button>\n\n                      <button\n                        onClick={() => {\n                          // Add settings page navigation here if needed\n                          setShowProfileDropdown(false);\n                        }}\n                        className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                        <span>Settings</span>\n                      </button>\n\n                      {isAdmin() && (\n                        <Link\n                          to=\"/admin-dashboard\"\n                          onClick={() => setShowProfileDropdown(false)}\n                          className=\"w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2\"\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                          </svg>\n                          <span>Admin Dashboard</span>\n                        </Link>\n                      )}\n                    </div>\n\n                    <div className=\"border-t border-gray-100 pt-2\">\n                      <button\n                        onClick={handleLogout}\n                        className=\"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                        </svg>\n                        <span>Logout</span>\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <button\n                onClick={() => setShowLogin(true)}\n                className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5\"\n              >\n                Login\n              </button>\n            )}\n          </div>\n\n          {/* Login Modal */}\n          {showLogin && (\n            <Login\n              onLogin={handleLogin}\n              onClose={() => setShowLogin(false)}\n            />\n          )}\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 p-2\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                {isMobileMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\">\n            {/* Hide navigation completely on public pages */}\n            {isPublicPage ? (\n              <Link\n                to=\"/\"\n                className={getLinkClasses('/', true)}\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                Patient Portal\n              </Link>\n            ) : (\n              /* Show hospital system navigation only for authenticated staff */\n              user && user.role !== 'patient' ? (\n                <>\n                  <Link\n                    to=\"/home\"\n                    className={getLinkClasses('/home', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Home\n                  </Link>\n                  <Link\n                    to=\"/about\"\n                    className={getLinkClasses('/about', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    About\n                  </Link>\n                  <Link\n                    to=\"/appointment\"\n                    className={getLinkClasses('/appointment', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Appointment\n                  </Link>\n                  <Link\n                    to=\"/billing\"\n                    className={getLinkClasses('/billing', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Billing\n                  </Link>\n                  <Link\n                    to=\"/help\"\n                    className={getLinkClasses('/help', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Help\n                  </Link>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/help\"\n                    className={getLinkClasses('/help', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    Help\n                  </Link>\n                  <Link\n                    to=\"/about\"\n                    className={getLinkClasses('/about', true)}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    About\n                  </Link>\n                </>\n              )\n            )}\n            <div className=\"pt-4 pb-3 border-t border-gray-200 mx-4\">\n              {isAuthenticated() ? (\n                <div className=\"space-y-3\">\n                  <div className=\"text-center\">\n                    <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">{user.role}</div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMobileMenuOpen(false);\n                    }}\n                    className=\"w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <button\n                  onClick={() => {\n                    setShowLogin(true);\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg\"\n                >\n                  Login\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n}\n\nfunction App() {\n  return(\n    <AuthProvider>\n      <Router>\n        <div className=\"min-h-screen bg-white\">\n          <Header />\n\n          {/* Main Content */}\n          <main>\n            <Routes>\n                {/* Default route - starts with patient portal */}\n                <Route path=\"/\" element={<PatientPortal />} />\n\n                {/* Public patient services - no authentication required */}\n                <Route path=\"/public-medical-records\" element={<PublicMedicalRecords />} />\n                <Route path=\"/public-pharmacy\" element={<PublicPharmacy />} />\n                <Route path=\"/public-exam-results\" element={<PublicExamResults />} />\n                <Route path=\"/public-appointment\" element={<PublicAppointment />} />\n\n                {/* Hospital staff system routes - authentication required */}\n                <Route path=\"/home\" element={<Home />} />\n                <Route path=\"/about\" element={<About />} />\n                <Route path=\"/help\" element={<Help />} />\n                <Route path=\"/messages\" element={<Messages />} />\n\n                {/* Patient management - accessible to admin, doctor, nurse */}\n                <Route\n                  path=\"/patients\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse']}>\n                      <Patients />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Medical records - accessible to admin, doctor only */}\n                <Route\n                  path=\"/medical-records\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <MedicalRecords />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Exams - accessible to admin, doctor only */}\n                <Route\n                  path=\"/exams\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <Exams />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Pharmacy - accessible to admin, doctor, nurse, staff */}\n                <Route\n                  path=\"/pharmacy\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'nurse', 'staff']}>\n                      <Pharmacy />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Billing - accessible to admin, doctor, staff */}\n                <Route\n                  path=\"/billing\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>\n                      <BillingPage />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Patient Portal - accessible to everyone */}\n                <Route\n                  path=\"/patient-portal\"\n                  element={<PatientPortal />}\n                />\n\n                {/* Appointments - accessible to admin, doctor, staff only */}\n                <Route\n                  path=\"/appointment\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor', 'staff']}>\n                      <Appointment />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Room management - accessible to admin, doctor only */}\n                <Route\n                  path=\"/room\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <Room />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Hospital transfers - accessible to admin, doctor only */}\n                <Route\n                  path=\"/hospital-transfer\"\n                  element={\n                    <ProtectedRoute allowedRoles={['admin', 'doctor']}>\n                      <HospitalTransfer />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Doctor management - accessible to admin only */}\n                <Route\n                  path=\"/doctor\"\n                  element={\n                    <ProtectedRoute requireAdmin={true}>\n                      <Doctor />\n                    </ProtectedRoute>\n                  }\n                />\n\n                {/* Admin dashboard - accessible to admin only */}\n                <Route\n                  path=\"/admin-dashboard\"\n                  element={\n                    <ProtectedRoute requireAdmin={true}>\n                      <AdminDashboard />\n                    </ProtectedRoute>\n                  }\n                />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzG,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAO,aAAa;;AAEpB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAMsC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAMyC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGxC,OAAO,CAAC,CAAC;EACnE,MAAMyC,kBAAkB,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM4C,WAAW,GAAIC,QAAQ,IAAK;IAChCH,KAAK,CAACG,QAAQ,CAAC;IACfZ,YAAY,CAAC,KAAK,CAAC;IACnB;IACAI,QAAQ,CAAC,OAAO,CAAC;EACnB,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBP,MAAM,CAAC,MAAM;MACXF,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFF,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMY,eAAe,GAAGX,QAAQ,CAACY,QAAQ,KAAK,GAAG;EACjD,MAAMC,YAAY,GAAGb,QAAQ,CAACY,QAAQ,CAACE,UAAU,CAAC,UAAU,CAAC,IAAId,QAAQ,CAACY,QAAQ,KAAK,GAAG;;EAE1F;EACAjD,SAAS,CAAC,MAAM;IACd,MAAMoD,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIT,kBAAkB,CAACU,OAAO,IAAI,CAACV,kBAAkB,CAACU,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpFpB,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAEDqB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOxB,QAAQ,CAACY,QAAQ,KAAKY,IAAI;EACnC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACD,IAAI,EAAEE,QAAQ,GAAG,KAAK,KAAK;IACjD,MAAMC,WAAW,GAAGD,QAAQ,GACxB,iGAAiG,GACjG,iFAAiF;IACrF,MAAME,aAAa,GAAGF,QAAQ,GAC1B,oDAAoD,GACpD,yDAAyD;IAC7D,MAAMG,eAAe,GAAGH,QAAQ,GAC5B,0EAA0E,GAC1E,oEAAoE;IAExE,OAAO,GAAGC,WAAW,IAAIJ,QAAQ,CAACC,IAAI,CAAC,GAAGI,aAAa,GAAGC,eAAe,EAAE;EAC7E,CAAC;EAED,oBACExC,OAAA;IAAQyC,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAC/E1C,OAAA;MAAKyC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrD1C,OAAA;QAAKyC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD1C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC1C,OAAA;YAAKyC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eAC9C1C,OAAA;cAAKyC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C1C,OAAA;gBAAKyC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,eAChH1C,OAAA;kBAAKyC,SAAS,EAAC,oBAAoB;kBAACE,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAF,QAAA,eACzE1C,OAAA;oBAAM6C,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,qLAAqL;oBAACC,QAAQ,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnD,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAIyC,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,GAAC,QACtD,eAAA1C,OAAA;oBAAMyC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAI;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACLnD,OAAA;kBAAGyC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAM;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnD,OAAA;UAAKyC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAEtClB,YAAY,gBACXxB,OAAA,CAAC9B,IAAI;YAACkF,EAAE,EAAC,GAAG;YAACX,SAAS,EAAEL,cAAc,CAAC,GAAG,CAAE;YAAAM,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,IAEP;UACAtC,IAAI,IAAIA,IAAI,CAACwC,IAAI,KAAK,SAAS,gBAC7BrD,OAAA,CAAAE,SAAA;YAAAwC,QAAA,gBACE1C,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,OAAO;cAACX,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;cAAAM,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,QAAQ;cAACX,SAAS,EAAEL,cAAc,CAAC,QAAQ,CAAE;cAAAM,QAAA,EAAC;YAEvD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,cAAc;cAACX,SAAS,EAAEL,cAAc,CAAC,cAAc,CAAE;cAAAM,QAAA,EAAC;YAEnE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,UAAU;cAACX,SAAS,EAAEL,cAAc,CAAC,UAAU,CAAE;cAAAM,QAAA,EAAC;YAE3D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,OAAO;cAACX,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;cAAAM,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;YAAAwC,QAAA,gBACE1C,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,OAAO;cAACX,SAAS,EAAEL,cAAc,CAAC,OAAO,CAAE;cAAAM,QAAA,EAAC;YAErD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;cAACkF,EAAE,EAAC,QAAQ;cAACX,SAAS,EAAEL,cAAc,CAAC,QAAQ,CAAE;cAAAM,QAAA,EAAC;YAEvD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACP,CACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnD,OAAA;UAAKyC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD3B,eAAe,CAAC,CAAC,gBAChBf,OAAA;YAAKyC,SAAS,EAAC,UAAU;YAACa,GAAG,EAAEpC,kBAAmB;YAAAwB,QAAA,gBAChD1C,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM7C,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAC5DgC,SAAS,EAAC,qLAAqL;cAAAC,QAAA,gBAE/L1C,OAAA;gBAAKyC,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,eAChH1C,OAAA;kBAAMyC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC3C7B,IAAI,CAAC2C,SAAS,GAAG3C,IAAI,CAAC2C,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;gBAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNnD,OAAA;gBAAKyC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1C,OAAA;kBAAKyC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE7B,IAAI,CAAC8C;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEnD,OAAA;kBAAKyC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAE7B,IAAI,CAACwC;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNnD,OAAA;gBAAKyC,SAAS,EAAE,8CAA8ChC,mBAAmB,GAAG,YAAY,GAAG,EAAE,EAAG;gBAACkC,IAAI,EAAC,MAAM;gBAACiB,MAAM,EAAC,cAAc;gBAAChB,OAAO,EAAC,WAAW;gBAAAF,QAAA,eAC5J1C,OAAA;kBAAM6D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACjB,CAAC,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGR1C,mBAAmB,iBAClBT,OAAA;cAAKyC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBACxG1C,OAAA;gBAAKyC,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjD1C,OAAA;kBAAKyC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1C,OAAA;oBAAKyC,SAAS,EAAC,qGAAqG;oBAAAC,QAAA,eAClH1C,OAAA;sBAAMyC,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EACnC7B,IAAI,CAAC2C,SAAS,GAAG3C,IAAI,CAAC2C,SAAS,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;oBAAG;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNnD,OAAA;oBAAA0C,QAAA,gBACE1C,OAAA;sBAAKyC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAE7B,IAAI,CAAC8C;oBAAI;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5DnD,OAAA;sBAAKyC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAE7B,IAAI,CAACmD;oBAAK;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzDnD,OAAA;sBAAKyC,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAE7B,IAAI,CAACwC;oBAAI;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnD,OAAA;gBAAKyC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1C,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACA7C,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF+B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzG1C,OAAA;oBAAKyC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5E1C,OAAA;sBAAM6D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAqE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1I,CAAC,eACNnD,OAAA;oBAAA0C,QAAA,EAAM;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eAETnD,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAM;oBACb;oBACA7C,sBAAsB,CAAC,KAAK,CAAC;kBAC/B,CAAE;kBACF+B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzG1C,OAAA;oBAAKyC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,gBAC5E1C,OAAA;sBAAM6D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAqe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7iBnD,OAAA;sBAAM6D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC,eACNnD,OAAA;oBAAA0C,QAAA,EAAM;kBAAQ;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EAERnC,OAAO,CAAC,CAAC,iBACRhB,OAAA,CAAC9B,IAAI;kBACHkF,EAAE,EAAC,kBAAkB;kBACrBG,OAAO,EAAEA,CAAA,KAAM7C,sBAAsB,CAAC,KAAK,CAAE;kBAC7C+B,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,gBAEzG1C,OAAA;oBAAKyC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5E1C,OAAA;sBAAM6D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAAgM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrQ,CAAC,eACNnD,OAAA;oBAAA0C,QAAA,EAAM;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENnD,OAAA;gBAAKyC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,eAC5C1C,OAAA;kBACEuD,OAAO,EAAElC,YAAa;kBACtBoB,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,gBAEvG1C,OAAA;oBAAKyC,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACiB,MAAM,EAAC,cAAc;oBAAChB,OAAO,EAAC,WAAW;oBAAAF,QAAA,eAC5E1C,OAAA;sBAAM6D,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAC,GAAG;sBAACjB,CAAC,EAAC;oBAA2F;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK,CAAC,eACNnD,OAAA;oBAAA0C,QAAA,EAAM;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENnD,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAAC,IAAI,CAAE;YAClCiC,SAAS,EAAC,+NAA+N;YAAAC,QAAA,EAC1O;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL5C,SAAS,iBACRP,OAAA,CAACtB,KAAK;UACJuF,OAAO,EAAE9C,WAAY;UACrB+C,OAAO,EAAEA,CAAA,KAAM1D,YAAY,CAAC,KAAK;QAAE;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACF,eAGDnD,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB1C,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;YACtDoC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAEjD1C,OAAA;cAAKyC,SAAS,EAAC,SAAS;cAACE,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACgB,MAAM,EAAC,cAAc;cAAAlB,QAAA,EAC3ErC,gBAAgB,gBACfL,OAAA;gBAAM6D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACjB,CAAC,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE9FnD,OAAA;gBAAM6D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACjB,CAAC,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACjG;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9C,gBAAgB,iBACfL,OAAA;MAAKyC,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB1C,OAAA;QAAKyC,SAAS,EAAC,oEAAoE;QAAAC,QAAA,GAEhFlB,YAAY,gBACXxB,OAAA,CAAC9B,IAAI;UACHkF,EAAE,EAAC,GAAG;UACNX,SAAS,EAAEL,cAAc,CAAC,GAAG,EAAE,IAAI,CAAE;UACrCmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;UAAAoC,QAAA,EAC3C;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,IAEP;QACAtC,IAAI,IAAIA,IAAI,CAACwC,IAAI,KAAK,SAAS,gBAC7BrD,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACE1C,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,OAAO;YACVX,SAAS,EAAEL,cAAc,CAAC,OAAO,EAAE,IAAI,CAAE;YACzCmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,QAAQ;YACXX,SAAS,EAAEL,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAE;YAC1CmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,cAAc;YACjBX,SAAS,EAAEL,cAAc,CAAC,cAAc,EAAE,IAAI,CAAE;YAChDmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,UAAU;YACbX,SAAS,EAAEL,cAAc,CAAC,UAAU,EAAE,IAAI,CAAE;YAC5CmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,OAAO;YACVX,SAAS,EAAEL,cAAc,CAAC,OAAO,EAAE,IAAI,CAAE;YACzCmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;UAAAwC,QAAA,gBACE1C,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,OAAO;YACVX,SAAS,EAAEL,cAAc,CAAC,OAAO,EAAE,IAAI,CAAE;YACzCmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnD,OAAA,CAAC9B,IAAI;YACHkF,EAAE,EAAC,QAAQ;YACXX,SAAS,EAAEL,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAE;YAC1CmB,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAAC,KAAK,CAAE;YAAAoC,QAAA,EAC3C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,eACP,CACH,CACF,eACDnD,OAAA;UAAKyC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrD3B,eAAe,CAAC,CAAC,gBAChBf,OAAA;YAAKyC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB1C,OAAA;cAAKyC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1C,OAAA;gBAAKyC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAE7B,IAAI,CAAC8C;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEnD,OAAA;gBAAKyC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE7B,IAAI,CAACwC;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNnD,OAAA;cACEuD,OAAO,EAAEA,CAAA,KAAM;gBACblC,YAAY,CAAC,CAAC;gBACdf,mBAAmB,CAAC,KAAK,CAAC;cAC5B,CAAE;cACFmC,SAAS,EAAC,iMAAiM;cAAAC,QAAA,EAC5M;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENnD,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAM;cACb/C,YAAY,CAAC,IAAI,CAAC;cAClBF,mBAAmB,CAAC,KAAK,CAAC;YAC5B,CAAE;YACFmC,SAAS,EAAC,qMAAqM;YAAAC,QAAA,EAChN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb;AAAC/C,EAAA,CA1WQD,MAAM;EAAA,QAIIhC,WAAW,EACXC,WAAW,EAC8BK,OAAO;AAAA;AAAA0F,EAAA,GAN1DhE,MAAM;AA4Wf,SAASiE,GAAGA,CAAA,EAAG;EACb,oBACEpE,OAAA,CAACxB,YAAY;IAAAkE,QAAA,eACX1C,OAAA,CAACjC,MAAM;MAAA2E,QAAA,eACL1C,OAAA;QAAKyC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC1C,OAAA,CAACG,MAAM;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGVnD,OAAA;UAAA0C,QAAA,eACE1C,OAAA,CAAChC,MAAM;YAAA0E,QAAA,gBAEH1C,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,GAAG;cAACkC,OAAO,eAAErE,OAAA,CAACN,aAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG9CnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,yBAAyB;cAACkC,OAAO,eAAErE,OAAA,CAACL,oBAAoB;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3EnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,kBAAkB;cAACkC,OAAO,eAAErE,OAAA,CAACJ,cAAc;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,sBAAsB;cAACkC,OAAO,eAAErE,OAAA,CAACH,iBAAiB;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrEnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,qBAAqB;cAACkC,OAAO,eAAErE,OAAA,CAACF,iBAAiB;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGpEnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,OAAO;cAACkC,OAAO,eAAErE,OAAA,CAACpB,IAAI;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,QAAQ;cAACkC,OAAO,eAAErE,OAAA,CAACnB,KAAK;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,OAAO;cAACkC,OAAO,eAAErE,OAAA,CAAClB,IAAI;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCnD,OAAA,CAAC/B,KAAK;cAACkE,IAAI,EAAC,WAAW;cAACkC,OAAO,eAAErE,OAAA,CAACX,QAAQ;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGjDnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,WAAW;cAChBkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eACzD1C,OAAA,CAACd,QAAQ;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,kBAAkB;cACvBkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChD1C,OAAA,CAACjB,cAAc;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,QAAQ;cACbkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChD1C,OAAA,CAACZ,KAAK;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,WAAW;cAChBkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eAClE1C,OAAA,CAAChB,QAAQ;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,UAAU;cACfkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eACzD1C,OAAA,CAACP,WAAW;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,iBAAiB;cACtBkC,OAAO,eAAErE,OAAA,CAACN,aAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,cAAc;cACnBkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;gBAAA5B,QAAA,eACzD1C,OAAA,CAACT,WAAW;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,OAAO;cACZkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChD1C,OAAA,CAACV,IAAI;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,oBAAoB;cACzBkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC2F,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAE;gBAAA5B,QAAA,eAChD1C,OAAA,CAACb,gBAAgB;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,SAAS;cACdkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC4F,YAAY,EAAE,IAAK;gBAAA7B,QAAA,eACjC1C,OAAA,CAACf,MAAM;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFnD,OAAA,CAAC/B,KAAK;cACJkE,IAAI,EAAC,kBAAkB;cACvBkC,OAAO,eACLrE,OAAA,CAACrB,cAAc;gBAAC4F,YAAY,EAAE,IAAK;gBAAA7B,QAAA,eACjC1C,OAAA,CAACR,cAAc;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACqB,GAAA,GAxIQJ,GAAG;AA0IZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAK,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
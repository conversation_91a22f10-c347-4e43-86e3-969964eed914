{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\components\\\\Billing.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport PrintInvoice from './PrintInvoice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Billing = () => {\n  _s();\n  const [showBillingForm, setShowBillingForm] = useState(false);\n  const [bills, setBills] = useState([]);\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedBill, setSelectedBill] = useState(null);\n  const [showPrintModal, setShowPrintModal] = useState(false);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    patientNationalId: '',\n    patientName: '',\n    patientEmail: '',\n    patientPhone: '',\n    patientAddress: '',\n    items: [{\n      description: '',\n      quantity: 1,\n      unitPrice: 0,\n      total: 0\n    }],\n    subtotal: 0,\n    tax: 0,\n    discount: 0,\n    total: 0,\n    amountPaid: 0,\n    paymentMethod: 'Cash',\n    paymentStatus: 'Pending',\n    notes: ''\n  });\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch bills and patients on component mount\n  useEffect(() => {\n    fetchBills();\n    fetchPatients();\n  }, []);\n\n  // Fetch all bills\n  const fetchBills = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/billing`);\n      const data = await response.json();\n      if (data.success) {\n        setBills(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching bills:', error);\n    }\n  };\n\n  // Fetch all patients\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n    }\n  };\n\n  // Handle input changes\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle patient selection\n  const handlePatientSelect = e => {\n    const nationalId = e.target.value;\n    const patient = patients.find(p => p.nationalId === nationalId);\n    if (patient) {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: patient.nationalId,\n        patientName: `${patient.firstName} ${patient.lastName}`,\n        patientEmail: patient.email,\n        patientPhone: patient.phone,\n        patientAddress: patient.address\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: '',\n        patientName: '',\n        patientEmail: '',\n        patientPhone: '',\n        patientAddress: ''\n      }));\n    }\n  };\n\n  // Handle item changes\n  const handleItemChange = (index, field, value) => {\n    const newItems = [...formData.items];\n    newItems[index][field] = value;\n\n    // Calculate item total\n    if (field === 'quantity' || field === 'unitPrice') {\n      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;\n    }\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n\n    // Recalculate totals\n    calculateTotals(newItems);\n  };\n\n  // Add new item\n  const addItem = () => {\n    setFormData(prev => ({\n      ...prev,\n      items: [...prev.items, {\n        description: '',\n        quantity: 1,\n        unitPrice: 0,\n        total: 0\n      }]\n    }));\n  };\n\n  // Remove item\n  const removeItem = index => {\n    const newItems = formData.items.filter((_, i) => i !== index);\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n    calculateTotals(newItems);\n  };\n\n  // Calculate totals\n  const calculateTotals = items => {\n    const subtotal = items.reduce((sum, item) => sum + item.total, 0);\n    const tax = subtotal * 0.18; // 18% tax\n    const total = subtotal + tax - formData.discount;\n    setFormData(prev => ({\n      ...prev,\n      subtotal: subtotal,\n      tax: tax,\n      total: total\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/billing`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Bill created successfully!');\n        setShowBillingForm(false);\n        resetForm();\n        fetchBills();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error creating bill:', error);\n      alert('Error creating bill. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      patientNationalId: '',\n      patientName: '',\n      patientEmail: '',\n      patientPhone: '',\n      patientAddress: '',\n      items: [{\n        description: '',\n        quantity: 1,\n        unitPrice: 0,\n        total: 0\n      }],\n      subtotal: 0,\n      tax: 0,\n      discount: 0,\n      total: 0,\n      amountPaid: 0,\n      paymentMethod: 'Cash',\n      paymentStatus: 'Pending',\n      notes: ''\n    });\n  };\n\n  // Handle print\n  const handlePrint = bill => {\n    setSelectedBill(bill);\n    setShowPrintModal(true);\n  };\n\n  // Print invoice\n  const printInvoice = () => {\n    window.print();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-blue-600 to-green-600 rounded-xl flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-7 h-7 text-white\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Hospital Billing & Accounts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Patient billing and financial management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowBillingForm(true),\n        className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 4v16m8-8H4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), \"Generate Medical Bill\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Recent Medical Bills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/billing',\n          className: \"text-blue-600 hover:text-blue-800 font-medium text-sm flex items-center gap-1\",\n          children: [\"View All Bills\", /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M9 5l7 7-7 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto bg-white rounded-lg border border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-blue-50 border-b border-blue-100\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Bill No.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Patient Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Patient ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Total Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Payment Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Bill Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"divide-y divide-gray-100\",\n            children: bills.slice(0, 5).map(bill => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-blue-25 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3 text-sm font-medium text-blue-900\",\n                children: bill.invoiceNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                children: bill.patientName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3 text-sm text-gray-600\",\n                children: bill.patientNationalId || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3 text-sm font-bold text-green-700\",\n                children: [bill.total.toFixed(2), \" RWF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-3 py-1 text-xs font-bold rounded-full border ${bill.paymentStatus === 'Paid' ? 'bg-green-50 text-green-700 border-green-200' : bill.paymentStatus === 'Pending' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' : bill.paymentStatus === 'Partial' ? 'bg-orange-50 text-orange-700 border-orange-200' : 'bg-red-50 text-red-700 border-red-200'}`,\n                  children: bill.paymentStatus\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3 text-sm text-gray-600\",\n                children: new Date(bill.createdAt).toLocaleDateString('en-GB')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePrint(bill),\n                  className: \"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg text-xs font-medium transition-colors flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 23\n                  }, this), \"Print Bill\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)]\n            }, bill.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this), showBillingForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Create New Invoice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowBillingForm(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-xl p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"\\uD83D\\uDC64 Patient Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Select Patient\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: formData.patientNationalId,\n                    onChange: handlePatientSelect,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select a patient or enter manually\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 25\n                    }, this), patients.map(patient => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: patient.nationalId,\n                      children: [patient.firstName, \" \", patient.lastName, \" - \", patient.nationalId]\n                    }, patient.nationalId, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Patient Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"patientName\",\n                    value: formData.patientName,\n                    onChange: handleInputChange,\n                    required: true,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    name: \"patientEmail\",\n                    value: formData.patientEmail,\n                    onChange: handleInputChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"patientPhone\",\n                    value: formData.patientPhone,\n                    onChange: handleInputChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Address\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    name: \"patientAddress\",\n                    value: formData.patientAddress,\n                    onChange: handleInputChange,\n                    rows: \"2\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-xl p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"\\uD83D\\uDECD\\uFE0F Items & Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: addItem,\n                  className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium\",\n                  children: \"+ Add Item\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: formData.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-5 gap-3 bg-white p-3 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:col-span-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      placeholder: \"Description\",\n                      value: item.description,\n                      onChange: e => handleItemChange(index, 'description', e.target.value),\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      placeholder: \"Qty\",\n                      value: item.quantity,\n                      onChange: e => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0),\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      placeholder: \"Unit Price\",\n                      value: item.unitPrice,\n                      onChange: e => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0),\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      placeholder: \"Total\",\n                      value: item.total,\n                      readOnly: true,\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 431,\n                      columnNumber: 27\n                    }, this), formData.items.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => removeItem(index),\n                      className: \"text-red-500 hover:text-red-700 p-1\",\n                      children: \"\\uD83D\\uDDD1\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-xl p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"\\uD83D\\uDCB3 Payment Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1\",\n                      children: \"Payment Method\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      name: \"paymentMethod\",\n                      value: formData.paymentMethod,\n                      onChange: handleInputChange,\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Cash\",\n                        children: \"Cash\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 467,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Card\",\n                        children: \"Card\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 468,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Insurance\",\n                        children: \"Insurance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 469,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Mobile Money\",\n                        children: \"Mobile Money\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1\",\n                      children: \"Payment Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 474,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      name: \"paymentStatus\",\n                      value: formData.paymentStatus,\n                      onChange: handleInputChange,\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Pending\",\n                        children: \"Pending\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Paid\",\n                        children: \"Paid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 482,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Partial\",\n                        children: \"Partial\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Cancelled\",\n                        children: \"Cancelled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 484,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1\",\n                      children: \"Notes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      name: \"notes\",\n                      value: formData.notes,\n                      onChange: handleInputChange,\n                      rows: \"3\",\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-xl p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-semibold text-gray-900 mb-4\",\n                  children: \"\\uD83D\\uDCB0 Invoice Totals\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Subtotal:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold\",\n                      children: [formData.subtotal.toFixed(2), \" Rwf\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Tax (18%):\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold\",\n                      children: [formData.tax.toFixed(2), \" Rwf\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Discount:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      name: \"discount\",\n                      value: formData.discount,\n                      onChange: e => {\n                        const discount = parseFloat(e.target.value) || 0;\n                        setFormData(prev => ({\n                          ...prev,\n                          discount: discount,\n                          total: prev.subtotal + prev.tax - discount\n                        }));\n                      },\n                      className: \"w-24 px-2 py-1 border border-gray-300 rounded text-right\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 514,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border-t pt-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between text-lg font-bold\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Total:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-600\",\n                        children: [formData.total.toFixed(2), \" Rwf\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 532,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end gap-4 pt-6 border-t\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowBillingForm(false),\n                className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-medium\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50\",\n                children: loading ? 'Creating...' : 'Create Invoice'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 9\n    }, this), showPrintModal && selectedBill && /*#__PURE__*/_jsxDEV(PrintInvoice, {\n      bill: selectedBill,\n      onClose: () => {\n        setShowPrintModal(false);\n        setSelectedBill(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(Billing, \"lycMRISRGg35T0QLUJEK3OqbVSI=\");\n_c = Billing;\nexport default Billing;\nvar _c;\n$RefreshReg$(_c, \"Billing\");", "map": {"version": 3, "names": ["useState", "useEffect", "PrintInvoice", "jsxDEV", "_jsxDEV", "Billing", "_s", "showBillingForm", "setShowBillingForm", "bills", "setBills", "patients", "setPatients", "loading", "setLoading", "selected<PERSON><PERSON>", "setSelectedBill", "showPrintModal", "setShowPrintModal", "formData", "setFormData", "patientNationalId", "patientName", "patientEmail", "patientPhone", "patientAddress", "items", "description", "quantity", "unitPrice", "total", "subtotal", "tax", "discount", "amountPaid", "paymentMethod", "paymentStatus", "notes", "API_BASE_URL", "fetchBills", "fetchPatients", "response", "fetch", "data", "json", "success", "error", "console", "handleInputChange", "e", "name", "value", "target", "prev", "handlePatientSelect", "nationalId", "patient", "find", "p", "firstName", "lastName", "email", "phone", "address", "handleItemChange", "index", "field", "newItems", "calculateTotals", "addItem", "removeItem", "filter", "_", "i", "reduce", "sum", "item", "handleSubmit", "preventDefault", "method", "headers", "body", "JSON", "stringify", "alert", "resetForm", "message", "handlePrint", "bill", "printInvoice", "window", "print", "className", "children", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "location", "href", "slice", "map", "invoiceNumber", "toFixed", "Date", "createdAt", "toLocaleDateString", "id", "onSubmit", "onChange", "type", "required", "rows", "placeholder", "parseFloat", "readOnly", "length", "disabled", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/components/Billing.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport PrintInvoice from './PrintInvoice';\n\nconst Billing = () => {\n  const [showBillingForm, setShowBillingForm] = useState(false);\n  const [bills, setBills] = useState([]);\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedBill, setSelectedBill] = useState(null);\n  const [showPrintModal, setShowPrintModal] = useState(false);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    patientNationalId: '',\n    patientName: '',\n    patientEmail: '',\n    patientPhone: '',\n    patientAddress: '',\n    items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\n    subtotal: 0,\n    tax: 0,\n    discount: 0,\n    total: 0,\n    amountPaid: 0,\n    paymentMethod: 'Cash',\n    paymentStatus: 'Pending',\n    notes: ''\n  });\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch bills and patients on component mount\n  useEffect(() => {\n    fetchBills();\n    fetchPatients();\n  }, []);\n\n  // Fetch all bills\n  const fetchBills = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/billing`);\n      const data = await response.json();\n      if (data.success) {\n        setBills(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching bills:', error);\n    }\n  };\n\n  // Fetch all patients\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n    }\n  };\n\n  // Handle input changes\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle patient selection\n  const handlePatientSelect = (e) => {\n    const nationalId = e.target.value;\n    const patient = patients.find(p => p.nationalId === nationalId);\n    \n    if (patient) {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: patient.nationalId,\n        patientName: `${patient.firstName} ${patient.lastName}`,\n        patientEmail: patient.email,\n        patientPhone: patient.phone,\n        patientAddress: patient.address\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        patientNationalId: '',\n        patientName: '',\n        patientEmail: '',\n        patientPhone: '',\n        patientAddress: ''\n      }));\n    }\n  };\n\n  // Handle item changes\n  const handleItemChange = (index, field, value) => {\n    const newItems = [...formData.items];\n    newItems[index][field] = value;\n    \n    // Calculate item total\n    if (field === 'quantity' || field === 'unitPrice') {\n      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;\n    }\n    \n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n    \n    // Recalculate totals\n    calculateTotals(newItems);\n  };\n\n  // Add new item\n  const addItem = () => {\n    setFormData(prev => ({\n      ...prev,\n      items: [...prev.items, { description: '', quantity: 1, unitPrice: 0, total: 0 }]\n    }));\n  };\n\n  // Remove item\n  const removeItem = (index) => {\n    const newItems = formData.items.filter((_, i) => i !== index);\n    setFormData(prev => ({\n      ...prev,\n      items: newItems\n    }));\n    calculateTotals(newItems);\n  };\n\n  // Calculate totals\n  const calculateTotals = (items) => {\n    const subtotal = items.reduce((sum, item) => sum + item.total, 0);\n    const tax = subtotal * 0.18; // 18% tax\n    const total = subtotal + tax - formData.discount;\n    \n    setFormData(prev => ({\n      ...prev,\n      subtotal: subtotal,\n      tax: tax,\n      total: total\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const response = await fetch(`${API_BASE_URL}/billing`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        alert('Bill created successfully!');\n        setShowBillingForm(false);\n        resetForm();\n        fetchBills();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error creating bill:', error);\n      alert('Error creating bill. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset form\n  const resetForm = () => {\n    setFormData({\n      patientNationalId: '',\n      patientName: '',\n      patientEmail: '',\n      patientPhone: '',\n      patientAddress: '',\n      items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],\n      subtotal: 0,\n      tax: 0,\n      discount: 0,\n      total: 0,\n      amountPaid: 0,\n      paymentMethod: 'Cash',\n      paymentStatus: 'Pending',\n      notes: ''\n    });\n  };\n\n  // Handle print\n  const handlePrint = (bill) => {\n    setSelectedBill(bill);\n    setShowPrintModal(true);\n  };\n\n  // Print invoice\n  const printInvoice = () => {\n    window.print();\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl p-6 border border-gray-100\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-green-600 rounded-xl flex items-center justify-center\">\n            <svg className=\"w-7 h-7 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Hospital Billing & Accounts</h2>\n            <p className=\"text-gray-600\">Patient billing and financial management</p>\n          </div>\n        </div>\n        <button\n          onClick={() => setShowBillingForm(true)}\n          className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\"\n        >\n          <span className=\"flex items-center gap-2\">\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Generate Medical Bill\n          </span>\n        </button>\n      </div>\n\n      {/* Recent Medical Bills */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Recent Medical Bills</h3>\n          <button\n            onClick={() => window.location.href = '/billing'}\n            className=\"text-blue-600 hover:text-blue-800 font-medium text-sm flex items-center gap-1\"\n          >\n            View All Bills\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </button>\n        </div>\n        <div className=\"overflow-x-auto bg-white rounded-lg border border-gray-200\">\n          <table className=\"w-full\">\n            <thead className=\"bg-blue-50 border-b border-blue-100\">\n              <tr>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Bill No.</th>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Patient Name</th>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Patient ID</th>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Total Amount</th>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Payment Status</th>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Bill Date</th>\n                <th className=\"px-4 py-3 text-left text-xs font-semibold text-blue-700 uppercase\">Actions</th>\n              </tr>\n            </thead>\n            <tbody className=\"divide-y divide-gray-100\">\n              {bills.slice(0, 5).map((bill) => (\n                <tr key={bill.id} className=\"hover:bg-blue-25 transition-colors\">\n                  <td className=\"px-4 py-3 text-sm font-medium text-blue-900\">{bill.invoiceNumber}</td>\n                  <td className=\"px-4 py-3 text-sm font-medium text-gray-900\">{bill.patientName}</td>\n                  <td className=\"px-4 py-3 text-sm text-gray-600\">{bill.patientNationalId || 'N/A'}</td>\n                  <td className=\"px-4 py-3 text-sm font-bold text-green-700\">{bill.total.toFixed(2)} RWF</td>\n                  <td className=\"px-4 py-3\">\n                    <span className={`inline-flex px-3 py-1 text-xs font-bold rounded-full border ${\n                      bill.paymentStatus === 'Paid' ? 'bg-green-50 text-green-700 border-green-200' :\n                      bill.paymentStatus === 'Pending' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :\n                      bill.paymentStatus === 'Partial' ? 'bg-orange-50 text-orange-700 border-orange-200' :\n                      'bg-red-50 text-red-700 border-red-200'\n                    }`}>\n                      {bill.paymentStatus}\n                    </span>\n                  </td>\n                  <td className=\"px-4 py-3 text-sm text-gray-600\">\n                    {new Date(bill.createdAt).toLocaleDateString('en-GB')}\n                  </td>\n                  <td className=\"px-4 py-3\">\n                    <button\n                      onClick={() => handlePrint(bill)}\n                      className=\"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg text-xs font-medium transition-colors flex items-center gap-1\"\n                    >\n                      <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\n                      </svg>\n                      Print Bill\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Billing Form Modal */}\n      {showBillingForm && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              {/* Modal Header */}\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Create New Invoice</h3>\n                <button\n                  onClick={() => setShowBillingForm(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                >\n                  ×\n                </button>\n              </div>\n\n              {/* Billing Form */}\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\n                {/* Patient Information */}\n                <div className=\"bg-gray-50 rounded-xl p-4\">\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">👤 Patient Information</h4>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Select Patient</label>\n                      <select\n                        value={formData.patientNationalId}\n                        onChange={handlePatientSelect}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"\">Select a patient or enter manually</option>\n                        {patients.map((patient) => (\n                          <option key={patient.nationalId} value={patient.nationalId}>\n                            {patient.firstName} {patient.lastName} - {patient.nationalId}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Patient Name *</label>\n                      <input\n                        type=\"text\"\n                        name=\"patientName\"\n                        value={formData.patientName}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n                      <input\n                        type=\"email\"\n                        name=\"patientEmail\"\n                        value={formData.patientEmail}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone</label>\n                      <input\n                        type=\"tel\"\n                        name=\"patientPhone\"\n                        value={formData.patientPhone}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                    <div className=\"md:col-span-2\">\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Address</label>\n                      <textarea\n                        name=\"patientAddress\"\n                        value={formData.patientAddress}\n                        onChange={handleInputChange}\n                        rows=\"2\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Items */}\n                <div className=\"bg-gray-50 rounded-xl p-4\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-900\">🛍️ Items & Services</h4>\n                    <button\n                      type=\"button\"\n                      onClick={addItem}\n                      className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium\"\n                    >\n                      + Add Item\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    {formData.items.map((item, index) => (\n                      <div key={index} className=\"grid grid-cols-1 md:grid-cols-5 gap-3 bg-white p-3 rounded-lg\">\n                        <div className=\"md:col-span-2\">\n                          <input\n                            type=\"text\"\n                            placeholder=\"Description\"\n                            value={item.description}\n                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          />\n                        </div>\n                        <div>\n                          <input\n                            type=\"number\"\n                            placeholder=\"Qty\"\n                            value={item.quantity}\n                            onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          />\n                        </div>\n                        <div>\n                          <input\n                            type=\"number\"\n                            placeholder=\"Unit Price\"\n                            value={item.unitPrice}\n                            onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                          />\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <input\n                            type=\"number\"\n                            placeholder=\"Total\"\n                            value={item.total}\n                            readOnly\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100\"\n                          />\n                          {formData.items.length > 1 && (\n                            <button\n                              type=\"button\"\n                              onClick={() => removeItem(index)}\n                              className=\"text-red-500 hover:text-red-700 p-1\"\n                            >\n                              🗑️\n                            </button>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Totals and Payment */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  {/* Payment Details */}\n                  <div className=\"bg-gray-50 rounded-xl p-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">💳 Payment Details</h4>\n                    <div className=\"space-y-3\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Payment Method</label>\n                        <select\n                          name=\"paymentMethod\"\n                          value={formData.paymentMethod}\n                          onChange={handleInputChange}\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        >\n                          <option value=\"Cash\">Cash</option>\n                          <option value=\"Card\">Card</option>\n                          <option value=\"Insurance\">Insurance</option>\n                          <option value=\"Mobile Money\">Mobile Money</option>\n                        </select>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Payment Status</label>\n                        <select\n                          name=\"paymentStatus\"\n                          value={formData.paymentStatus}\n                          onChange={handleInputChange}\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        >\n                          <option value=\"Pending\">Pending</option>\n                          <option value=\"Paid\">Paid</option>\n                          <option value=\"Partial\">Partial</option>\n                          <option value=\"Cancelled\">Cancelled</option>\n                        </select>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Notes</label>\n                        <textarea\n                          name=\"notes\"\n                          value={formData.notes}\n                          onChange={handleInputChange}\n                          rows=\"3\"\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Totals */}\n                  <div className=\"bg-gray-50 rounded-xl p-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">💰 Invoice Totals</h4>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Subtotal:</span>\n                        <span className=\"font-semibold\">{formData.subtotal.toFixed(2)} Rwf</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Tax (18%):</span>\n                        <span className=\"font-semibold\">{formData.tax.toFixed(2)} Rwf</span>\n                      </div>\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-gray-600\">Discount:</span>\n                        <input\n                          type=\"number\"\n                          name=\"discount\"\n                          value={formData.discount}\n                          onChange={(e) => {\n                            const discount = parseFloat(e.target.value) || 0;\n                            setFormData(prev => ({\n                              ...prev,\n                              discount: discount,\n                              total: prev.subtotal + prev.tax - discount\n                            }));\n                          }}\n                          className=\"w-24 px-2 py-1 border border-gray-300 rounded text-right\"\n                        />\n                      </div>\n                      <div className=\"border-t pt-3\">\n                        <div className=\"flex justify-between text-lg font-bold\">\n                          <span>Total:</span>\n                          <span className=\"text-green-600\">{formData.total.toFixed(2)} Rwf</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Form Actions */}\n                <div className=\"flex justify-end gap-4 pt-6 border-t\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowBillingForm(false)}\n                    className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-medium\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    disabled={loading}\n                    className=\"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50\"\n                  >\n                    {loading ? 'Creating...' : 'Create Invoice'}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Print Invoice Modal */}\n      {showPrintModal && selectedBill && (\n        <PrintInvoice\n          bill={selectedBill}\n          onClose={() => {\n            setShowPrintModal(false);\n            setSelectedBill(null);\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default Billing;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,iBAAiB,EAAE,EAAE;IACrBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,SAAS,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;IACjEC,QAAQ,EAAE,CAAC;IACXC,GAAG,EAAE,CAAC;IACNC,QAAQ,EAAE,CAAC;IACXH,KAAK,EAAE,CAAC;IACRI,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,MAAM;IACrBC,aAAa,EAAE,SAAS;IACxBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAG,2BAA2B;;EAEhD;EACArC,SAAS,CAAC,MAAM;IACdsC,UAAU,CAAC,CAAC;IACZC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,UAAU,CAAC;MACvD,MAAMK,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBnC,QAAQ,CAACiC,IAAI,CAACA,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMN,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,WAAW,CAAC;MACxD,MAAMK,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBjC,WAAW,CAAC+B,IAAI,CAACA,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChC,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIL,CAAC,IAAK;IACjC,MAAMM,UAAU,GAAGN,CAAC,CAACG,MAAM,CAACD,KAAK;IACjC,MAAMK,OAAO,GAAG7C,QAAQ,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,UAAU,KAAKA,UAAU,CAAC;IAE/D,IAAIC,OAAO,EAAE;MACXpC,WAAW,CAACiC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPhC,iBAAiB,EAAEmC,OAAO,CAACD,UAAU;QACrCjC,WAAW,EAAE,GAAGkC,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,QAAQ,EAAE;QACvDrC,YAAY,EAAEiC,OAAO,CAACK,KAAK;QAC3BrC,YAAY,EAAEgC,OAAO,CAACM,KAAK;QAC3BrC,cAAc,EAAE+B,OAAO,CAACO;MAC1B,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL3C,WAAW,CAACiC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPhC,iBAAiB,EAAE,EAAE;QACrBC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,cAAc,EAAE;MAClB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMuC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEf,KAAK,KAAK;IAChD,MAAMgB,QAAQ,GAAG,CAAC,GAAGhD,QAAQ,CAACO,KAAK,CAAC;IACpCyC,QAAQ,CAACF,KAAK,CAAC,CAACC,KAAK,CAAC,GAAGf,KAAK;;IAE9B;IACA,IAAIe,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,WAAW,EAAE;MACjDC,QAAQ,CAACF,KAAK,CAAC,CAACnC,KAAK,GAAGqC,QAAQ,CAACF,KAAK,CAAC,CAACrC,QAAQ,GAAGuC,QAAQ,CAACF,KAAK,CAAC,CAACpC,SAAS;IAC9E;IAEAT,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAEyC;IACT,CAAC,CAAC,CAAC;;IAEH;IACAC,eAAe,CAACD,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAME,OAAO,GAAGA,CAAA,KAAM;IACpBjD,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAE,CAAC,GAAG2B,IAAI,CAAC3B,KAAK,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC;IACjF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAIL,KAAK,IAAK;IAC5B,MAAME,QAAQ,GAAGhD,QAAQ,CAACO,KAAK,CAAC6C,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKR,KAAK,CAAC;IAC7D7C,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,KAAK,EAAEyC;IACT,CAAC,CAAC,CAAC;IACHC,eAAe,CAACD,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAI1C,KAAK,IAAK;IACjC,MAAMK,QAAQ,GAAGL,KAAK,CAACgD,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAAC9C,KAAK,EAAE,CAAC,CAAC;IACjE,MAAME,GAAG,GAAGD,QAAQ,GAAG,IAAI,CAAC,CAAC;IAC7B,MAAMD,KAAK,GAAGC,QAAQ,GAAGC,GAAG,GAAGb,QAAQ,CAACc,QAAQ;IAEhDb,WAAW,CAACiC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPtB,QAAQ,EAAEA,QAAQ;MAClBC,GAAG,EAAEA,GAAG;MACRF,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAG,MAAO5B,CAAC,IAAK;IAChCA,CAAC,CAAC6B,cAAc,CAAC,CAAC;IAClBhE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAM2B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,UAAU,EAAE;QACtDyC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAChE,QAAQ;MAC/B,CAAC,CAAC;MAEF,MAAMwB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBuC,KAAK,CAAC,4BAA4B,CAAC;QACnC5E,kBAAkB,CAAC,KAAK,CAAC;QACzB6E,SAAS,CAAC,CAAC;QACX9C,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACL6C,KAAK,CAAC,UAAUzC,IAAI,CAAC2C,OAAO,EAAE,CAAC;MACjC;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CsC,KAAK,CAAC,wCAAwC,CAAC;IACjD,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuE,SAAS,GAAGA,CAAA,KAAM;IACtBjE,WAAW,CAAC;MACVC,iBAAiB,EAAE,EAAE;MACrBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE,CAAC;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;MACjEC,QAAQ,EAAE,CAAC;MACXC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,CAAC;MACXH,KAAK,EAAE,CAAC;MACRI,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,MAAM;MACrBC,aAAa,EAAE,SAAS;MACxBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkD,WAAW,GAAIC,IAAI,IAAK;IAC5BxE,eAAe,CAACwE,IAAI,CAAC;IACrBtE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMuE,YAAY,GAAGA,CAAA,KAAM;IACzBC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,oBACEvF,OAAA;IAAKwF,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEzF,OAAA;MAAKwF,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDzF,OAAA;QAAKwF,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCzF,OAAA;UAAKwF,SAAS,EAAC,oGAAoG;UAAAC,QAAA,eACjHzF,OAAA;YAAKwF,SAAS,EAAC,oBAAoB;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eACvGzF,OAAA;cAAM8F,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpG,OAAA;UAAAyF,QAAA,gBACEzF,OAAA;YAAIwF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAA2B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFpG,OAAA;YAAGwF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNpG,OAAA;QACEqG,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAAC,IAAI,CAAE;QACxCoF,SAAS,EAAC,kNAAkN;QAAAC,QAAA,eAE5NzF,OAAA;UAAMwF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACvCzF,OAAA;YAAKwF,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5FzF,OAAA;cAAM8F,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,yBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpG,OAAA;MAAKwF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzF,OAAA;QAAKwF,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzF,OAAA;UAAIwF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EpG,OAAA;UACEqG,OAAO,EAAEA,CAAA,KAAMf,MAAM,CAACgB,QAAQ,CAACC,IAAI,GAAG,UAAW;UACjDf,SAAS,EAAC,+EAA+E;UAAAC,QAAA,GAC1F,gBAEC,eAAAzF,OAAA;YAAKwF,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5FzF,OAAA;cAAM8F,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNpG,OAAA;QAAKwF,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzEzF,OAAA;UAAOwF,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvBzF,OAAA;YAAOwF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eACpDzF,OAAA;cAAAyF,QAAA,gBACEzF,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/FpG,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnGpG,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGpG,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnGpG,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrGpG,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChGpG,OAAA;gBAAIwF,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpG,OAAA;YAAOwF,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACxCpF,KAAK,CAACmG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAErB,IAAI,iBAC1BpF,OAAA;cAAkBwF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBAC9DzF,OAAA;gBAAIwF,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAEL,IAAI,CAACsB;cAAa;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrFpG,OAAA;gBAAIwF,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,EAAEL,IAAI,CAAClE;cAAW;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnFpG,OAAA;gBAAIwF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAEL,IAAI,CAACnE,iBAAiB,IAAI;cAAK;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtFpG,OAAA;gBAAIwF,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,GAAEL,IAAI,CAAC1D,KAAK,CAACiF,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FpG,OAAA;gBAAIwF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBzF,OAAA;kBAAMwF,SAAS,EAAE,+DACfJ,IAAI,CAACpD,aAAa,KAAK,MAAM,GAAG,6CAA6C,GAC7EoD,IAAI,CAACpD,aAAa,KAAK,SAAS,GAAG,gDAAgD,GACnFoD,IAAI,CAACpD,aAAa,KAAK,SAAS,GAAG,gDAAgD,GACnF,uCAAuC,EACtC;kBAAAyD,QAAA,EACAL,IAAI,CAACpD;gBAAa;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLpG,OAAA;gBAAIwF,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAC5C,IAAImB,IAAI,CAACxB,IAAI,CAACyB,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACLpG,OAAA;gBAAIwF,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvBzF,OAAA;kBACEqG,OAAO,EAAEA,CAAA,KAAMlB,WAAW,CAACC,IAAI,CAAE;kBACjCI,SAAS,EAAC,gIAAgI;kBAAAC,QAAA,gBAE1IzF,OAAA;oBAAKwF,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC5FzF,OAAA;sBAAM8F,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA8K;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnO,CAAC,cAER;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GA5BEhB,IAAI,CAAC2B,EAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjG,eAAe,iBACdH,OAAA;MAAKwF,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAClFzF,OAAA;QAAKwF,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5FzF,OAAA;UAAKwF,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAElBzF,OAAA;YAAKwF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzF,OAAA;cAAIwF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxEpG,OAAA;cACEqG,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAAC,KAAK,CAAE;cACzCoF,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNpG,OAAA;YAAMgH,QAAQ,EAAEvC,YAAa;YAACe,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAEjDzF,OAAA;cAAKwF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCzF,OAAA;gBAAIwF,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAsB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFpG,OAAA;gBAAKwF,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDzF,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAOwF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtFpG,OAAA;oBACE+C,KAAK,EAAEhC,QAAQ,CAACE,iBAAkB;oBAClCgG,QAAQ,EAAE/D,mBAAoB;oBAC9BsC,SAAS,EAAC,8GAA8G;oBAAAC,QAAA,gBAExHzF,OAAA;sBAAQ+C,KAAK,EAAC,EAAE;sBAAA0C,QAAA,EAAC;oBAAkC;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC3D7F,QAAQ,CAACkG,GAAG,CAAErD,OAAO,iBACpBpD,OAAA;sBAAiC+C,KAAK,EAAEK,OAAO,CAACD,UAAW;sBAAAsC,QAAA,GACxDrC,OAAO,CAACG,SAAS,EAAC,GAAC,EAACH,OAAO,CAACI,QAAQ,EAAC,KAAG,EAACJ,OAAO,CAACD,UAAU;oBAAA,GADjDC,OAAO,CAACD,UAAU;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEvB,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNpG,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAOwF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtFpG,OAAA;oBACEkH,IAAI,EAAC,MAAM;oBACXpE,IAAI,EAAC,aAAa;oBAClBC,KAAK,EAAEhC,QAAQ,CAACG,WAAY;oBAC5B+F,QAAQ,EAAErE,iBAAkB;oBAC5BuE,QAAQ;oBACR3B,SAAS,EAAC;kBAA8G;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpG,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAOwF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EpG,OAAA;oBACEkH,IAAI,EAAC,OAAO;oBACZpE,IAAI,EAAC,cAAc;oBACnBC,KAAK,EAAEhC,QAAQ,CAACI,YAAa;oBAC7B8F,QAAQ,EAAErE,iBAAkB;oBAC5B4C,SAAS,EAAC;kBAA8G;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpG,OAAA;kBAAAyF,QAAA,gBACEzF,OAAA;oBAAOwF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EpG,OAAA;oBACEkH,IAAI,EAAC,KAAK;oBACVpE,IAAI,EAAC,cAAc;oBACnBC,KAAK,EAAEhC,QAAQ,CAACK,YAAa;oBAC7B6F,QAAQ,EAAErE,iBAAkB;oBAC5B4C,SAAS,EAAC;kBAA8G;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNpG,OAAA;kBAAKwF,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BzF,OAAA;oBAAOwF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EpG,OAAA;oBACE8C,IAAI,EAAC,gBAAgB;oBACrBC,KAAK,EAAEhC,QAAQ,CAACM,cAAe;oBAC/B4F,QAAQ,EAAErE,iBAAkB;oBAC5BwE,IAAI,EAAC,GAAG;oBACR5B,SAAS,EAAC;kBAA8G;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpG,OAAA;cAAKwF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCzF,OAAA;gBAAKwF,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDzF,OAAA;kBAAIwF,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EpG,OAAA;kBACEkH,IAAI,EAAC,QAAQ;kBACbb,OAAO,EAAEpC,OAAQ;kBACjBuB,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,EAC9F;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENpG,OAAA;gBAAKwF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvB1E,QAAQ,CAACO,KAAK,CAACmF,GAAG,CAAC,CAACjC,IAAI,EAAEX,KAAK,kBAC9B7D,OAAA;kBAAiBwF,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,gBACxFzF,OAAA;oBAAKwF,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BzF,OAAA;sBACEkH,IAAI,EAAC,MAAM;sBACXG,WAAW,EAAC,aAAa;sBACzBtE,KAAK,EAAEyB,IAAI,CAACjD,WAAY;sBACxB0F,QAAQ,EAAGpE,CAAC,IAAKe,gBAAgB,CAACC,KAAK,EAAE,aAAa,EAAEhB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBACxEyC,SAAS,EAAC;oBAA8G;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNpG,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBACEkH,IAAI,EAAC,QAAQ;sBACbG,WAAW,EAAC,KAAK;sBACjBtE,KAAK,EAAEyB,IAAI,CAAChD,QAAS;sBACrByF,QAAQ,EAAGpE,CAAC,IAAKe,gBAAgB,CAACC,KAAK,EAAE,UAAU,EAAEyD,UAAU,CAACzE,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAE;sBACtFyC,SAAS,EAAC;oBAA8G;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNpG,OAAA;oBAAAyF,QAAA,eACEzF,OAAA;sBACEkH,IAAI,EAAC,QAAQ;sBACbG,WAAW,EAAC,YAAY;sBACxBtE,KAAK,EAAEyB,IAAI,CAAC/C,SAAU;sBACtBwF,QAAQ,EAAGpE,CAAC,IAAKe,gBAAgB,CAACC,KAAK,EAAE,WAAW,EAAEyD,UAAU,CAACzE,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,CAAE;sBACvFyC,SAAS,EAAC;oBAA8G;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNpG,OAAA;oBAAKwF,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCzF,OAAA;sBACEkH,IAAI,EAAC,QAAQ;sBACbG,WAAW,EAAC,OAAO;sBACnBtE,KAAK,EAAEyB,IAAI,CAAC9C,KAAM;sBAClB6F,QAAQ;sBACR/B,SAAS,EAAC;oBAAgE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC,EACDrF,QAAQ,CAACO,KAAK,CAACkG,MAAM,GAAG,CAAC,iBACxBxH,OAAA;sBACEkH,IAAI,EAAC,QAAQ;sBACbb,OAAO,EAAEA,CAAA,KAAMnC,UAAU,CAACL,KAAK,CAAE;sBACjC2B,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAChD;oBAED;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA7CEvC,KAAK;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8CV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpG,OAAA;cAAKwF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDzF,OAAA;gBAAKwF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCzF,OAAA;kBAAIwF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChFpG,OAAA;kBAAKwF,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBzF,OAAA;oBAAAyF,QAAA,gBACEzF,OAAA;sBAAOwF,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtFpG,OAAA;sBACE8C,IAAI,EAAC,eAAe;sBACpBC,KAAK,EAAEhC,QAAQ,CAACgB,aAAc;sBAC9BkF,QAAQ,EAAErE,iBAAkB;sBAC5B4C,SAAS,EAAC,8GAA8G;sBAAAC,QAAA,gBAExHzF,OAAA;wBAAQ+C,KAAK,EAAC,MAAM;wBAAA0C,QAAA,EAAC;sBAAI;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClCpG,OAAA;wBAAQ+C,KAAK,EAAC,MAAM;wBAAA0C,QAAA,EAAC;sBAAI;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClCpG,OAAA;wBAAQ+C,KAAK,EAAC,WAAW;wBAAA0C,QAAA,EAAC;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5CpG,OAAA;wBAAQ+C,KAAK,EAAC,cAAc;wBAAA0C,QAAA,EAAC;sBAAY;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNpG,OAAA;oBAAAyF,QAAA,gBACEzF,OAAA;sBAAOwF,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtFpG,OAAA;sBACE8C,IAAI,EAAC,eAAe;sBACpBC,KAAK,EAAEhC,QAAQ,CAACiB,aAAc;sBAC9BiF,QAAQ,EAAErE,iBAAkB;sBAC5B4C,SAAS,EAAC,8GAA8G;sBAAAC,QAAA,gBAExHzF,OAAA;wBAAQ+C,KAAK,EAAC,SAAS;wBAAA0C,QAAA,EAAC;sBAAO;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACxCpG,OAAA;wBAAQ+C,KAAK,EAAC,MAAM;wBAAA0C,QAAA,EAAC;sBAAI;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClCpG,OAAA;wBAAQ+C,KAAK,EAAC,SAAS;wBAAA0C,QAAA,EAAC;sBAAO;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACxCpG,OAAA;wBAAQ+C,KAAK,EAAC,WAAW;wBAAA0C,QAAA,EAAC;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNpG,OAAA;oBAAAyF,QAAA,gBACEzF,OAAA;sBAAOwF,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAAK;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC7EpG,OAAA;sBACE8C,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEhC,QAAQ,CAACkB,KAAM;sBACtBgF,QAAQ,EAAErE,iBAAkB;sBAC5BwE,IAAI,EAAC,GAAG;sBACR5B,SAAS,EAAC;oBAA8G;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNpG,OAAA;gBAAKwF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCzF,OAAA;kBAAIwF,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/EpG,OAAA;kBAAKwF,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBzF,OAAA;oBAAKwF,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzF,OAAA;sBAAMwF,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChDpG,OAAA;sBAAMwF,SAAS,EAAC,eAAe;sBAAAC,QAAA,GAAE1E,QAAQ,CAACY,QAAQ,CAACgF,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACNpG,OAAA;oBAAKwF,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnCzF,OAAA;sBAAMwF,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAU;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjDpG,OAAA;sBAAMwF,SAAS,EAAC,eAAe;sBAAAC,QAAA,GAAE1E,QAAQ,CAACa,GAAG,CAAC+E,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNpG,OAAA;oBAAKwF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDzF,OAAA;sBAAMwF,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChDpG,OAAA;sBACEkH,IAAI,EAAC,QAAQ;sBACbpE,IAAI,EAAC,UAAU;sBACfC,KAAK,EAAEhC,QAAQ,CAACc,QAAS;sBACzBoF,QAAQ,EAAGpE,CAAC,IAAK;wBACf,MAAMhB,QAAQ,GAAGyF,UAAU,CAACzE,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC;wBAChD/B,WAAW,CAACiC,IAAI,KAAK;0BACnB,GAAGA,IAAI;0BACPpB,QAAQ,EAAEA,QAAQ;0BAClBH,KAAK,EAAEuB,IAAI,CAACtB,QAAQ,GAAGsB,IAAI,CAACrB,GAAG,GAAGC;wBACpC,CAAC,CAAC,CAAC;sBACL,CAAE;sBACF2D,SAAS,EAAC;oBAA0D;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNpG,OAAA;oBAAKwF,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BzF,OAAA;sBAAKwF,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDzF,OAAA;wBAAAyF,QAAA,EAAM;sBAAM;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnBpG,OAAA;wBAAMwF,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,GAAE1E,QAAQ,CAACW,KAAK,CAACiF,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;sBAAA;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpG,OAAA;cAAKwF,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnDzF,OAAA;gBACEkH,IAAI,EAAC,QAAQ;gBACbb,OAAO,EAAEA,CAAA,KAAMjG,kBAAkB,CAAC,KAAK,CAAE;gBACzCoF,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,EACnG;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpG,OAAA;gBACEkH,IAAI,EAAC,QAAQ;gBACbO,QAAQ,EAAEhH,OAAQ;gBAClB+E,SAAS,EAAC,sOAAsO;gBAAAC,QAAA,EAE/OhF,OAAO,GAAG,aAAa,GAAG;cAAgB;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAvF,cAAc,IAAIF,YAAY,iBAC7BX,OAAA,CAACF,YAAY;MACXsF,IAAI,EAAEzE,YAAa;MACnB+G,OAAO,EAAEA,CAAA,KAAM;QACb5G,iBAAiB,CAAC,KAAK,CAAC;QACxBF,eAAe,CAAC,IAAI,CAAC;MACvB;IAAE;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClG,EAAA,CA1jBID,OAAO;AAAA0H,EAAA,GAAP1H,OAAO;AA4jBb,eAAeA,OAAO;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\PatientPortal.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport Login from '../components/Login';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientPortal = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount (only if logged in)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n    }\n    // Always fetch medicines for pharmacy browsing\n    fetchMedicines();\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = medicine => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => item.id === medicine.id ? {\n        ...item,\n        quantity: item.quantity + 1\n      } : item));\n    } else {\n      setCart([...cart, {\n        ...medicine,\n        quantity: 1\n      }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = medicineId => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => item.id === medicineId ? {\n        ...item,\n        quantity: quantity\n      } : item));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + item.price * item.quantity, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-br from-blue-600 to-green-600 rounded-2xl flex items-center justify-center shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-8 h-8 text-white\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\",\n              children: \"Patient Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-lg\",\n              children: user ? `Welcome back, ${user.name}` : 'Access your health records and services'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), !user && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl shadow-xl p-6 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold mb-2\",\n                  children: \"Sign In to Access Your Health Records\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100\",\n                  children: \"Login to view your exam reports, manage appointments, and access personalized healthcare services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowLoginModal(true),\n                className: \"bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), \"Sign In\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-2 border border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('dashboard'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'dashboard' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('reports'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'reports' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), \"Exam Reports\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('appointments'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'appointments' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), \"Appointments\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('pharmacy'),\n              className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${activeTab === 'pharmacy' ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg' : 'text-gray-600 hover:bg-gray-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), \"Pharmacy\", cart.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                  children: cart.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-8\",\n        children: [activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(DashboardTab, {\n          user: user,\n          examReports: examReports,\n          appointments: appointments,\n          cartItems: cart.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), activeTab === 'reports' && (user ? /*#__PURE__*/_jsxDEV(ExamReportsTab, {\n          examReports: examReports,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(LoginPrompt, {\n          title: \"View Your Exam Reports\",\n          description: \"Login to access your medical exam reports and test results\",\n          onLogin: () => setShowLoginModal(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 15\n        }, this)), activeTab === 'appointments' && (user ? /*#__PURE__*/_jsxDEV(AppointmentsTab, {\n          appointments: appointments,\n          user: user,\n          onAppointmentCreated: fetchAppointments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(LoginPrompt, {\n          title: \"Manage Your Appointments\",\n          description: \"Login to view your appointments and request new ones\",\n          onLogin: () => setShowLoginModal(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this)), activeTab === 'pharmacy' && /*#__PURE__*/_jsxDEV(PharmacyTab, {\n          medicines: medicines,\n          cart: cart,\n          addToCart: addToCart,\n          removeFromCart: removeFromCart,\n          updateCartQuantity: updateCartQuantity,\n          cartTotal: cartTotal\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), showLoginModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: \"Patient Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowLoginModal(false),\n                className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Login, {\n              onLogin: userData => {\n                setShowLoginModal(false);\n                // The user state will be updated automatically by the auth context\n              },\n              onClose: () => setShowLoginModal(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n\n// Login Prompt Component\n_s(PatientPortal, \"cSmOgAgMf+4NCjkFWzXEE2pgKIw=\", false, function () {\n  return [useAuth];\n});\n_c = PatientPortal;\nconst LoginPrompt = ({\n  title,\n  description,\n  onLogin\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-2xl shadow-lg p-8 border border-gray-100 text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-24 h-24 bg-gradient-to-br from-blue-600 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-12 h-12 text-white\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-2xl font-bold text-gray-900 mb-3\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onLogin,\n      className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2 mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), \"Sign In to Continue\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n};\n\n// Dashboard Tab Component\n_c2 = LoginPrompt;\nconst DashboardTab = ({\n  user,\n  examReports,\n  appointments,\n  cartItems\n}) => {\n  const upcomingAppointments = appointments.filter(apt => new Date(apt.appointmentDate) > new Date()).slice(0, 3);\n  const recentReports = examReports.slice(0, 3);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl shadow-xl p-8 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold mb-2\",\n            children: \"Welcome to Your Health Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg\",\n            children: \"Manage your health records, appointments, and prescriptions all in one place\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-12 h-12 text-white\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-blue-600\",\n              children: examReports.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-blue-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-green-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-green-600\",\n              children: appointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-green-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-yellow-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Upcoming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-yellow-600\",\n              children: upcomingAppointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-yellow-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-purple-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Cart Items\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-purple-600\",\n              children: cartItems\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-purple-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-900 mb-4\",\n          children: \"Recent Exam Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), recentReports.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recentReports.map((report, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-900\",\n                children: report.examType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: new Date(report.examDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\",\n              children: report.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-center py-8\",\n          children: \"No exam reports available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-900 mb-4\",\n          children: \"Upcoming Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), upcomingAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: upcomingAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-900\",\n                children: [\"Dr. \", appointment.doctorName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: new Date(appointment.appointmentDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium\",\n              children: appointment.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 text-center py-8\",\n          children: \"No upcoming appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 338,\n    columnNumber: 5\n  }, this);\n};\n\n// Exam Reports Tab Component\n_c3 = DashboardTab;\nconst ExamReportsTab = ({\n  examReports,\n  loading\n}) => {\n  _s2();\n  const [selectedReport, setSelectedReport] = useState(null);\n  const downloadReport = report => {\n    // Create a simple text report\n    const reportContent = `\nMEDICAL EXAM REPORT\n==================\n\nPatient: ${report.patientName}\nNational ID: ${report.nationalId}\nExam Type: ${report.examType}\nExam Date: ${new Date(report.examDate).toLocaleDateString()}\nDoctor: ${report.doctorName}\nStatus: ${report.status}\n\nRESULTS:\n${report.results}\n\nRECOMMENDATIONS:\n${report.recommendations || 'No specific recommendations'}\n\nReport generated on: ${new Date().toLocaleDateString()}\nHealthCarePro Medical Center\n    `;\n    const blob = new Blob([reportContent], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `exam-report-${report.examType}-${new Date(report.examDate).toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Your Exam Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), examReports.length, \" Reports Available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-3 text-gray-600\",\n          children: \"Loading reports...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 11\n      }, this) : examReports.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: examReports.map((report, index) => {\n          var _report$results;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-green-50 rounded-xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-blue-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-bold text-gray-900\",\n                    children: report.examType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: new Date(report.examDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${report.status === 'Completed' ? 'bg-green-100 text-green-800' : report.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                children: report.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Doctor:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 42\n                }, this), \" \", report.doctorName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Results:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 42\n                }, this), \" \", (_report$results = report.results) === null || _report$results === void 0 ? void 0 : _report$results.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedReport(report),\n                className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\",\n                children: \"View Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => downloadReport(report),\n                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-gray-400\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"No Exam Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"You don't have any exam reports yet. They will appear here once available.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this), selectedReport && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Exam Report Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedReport(null),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Exam Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 font-semibold\",\n                  children: selectedReport.examType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: new Date(selectedReport.examDate).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Doctor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900\",\n                  children: selectedReport.doctorName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-block px-2 py-1 rounded-full text-xs font-medium ${selectedReport.status === 'Completed' ? 'bg-green-100 text-green-800' : selectedReport.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                  children: selectedReport.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 whitespace-pre-wrap\",\n                  children: selectedReport.results\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this), selectedReport.recommendations && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Recommendations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-50 rounded-lg p-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-900 whitespace-pre-wrap\",\n                  children: selectedReport.recommendations\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => downloadReport(selectedReport),\n                className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this), \"Download Report\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedReport(null),\n                className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\",\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 506,\n    columnNumber: 5\n  }, this);\n};\n\n// Appointments Tab Component\n_s2(ExamReportsTab, \"20BLP4QGphg0wQGhiT6pQGpcZQw=\");\n_c4 = ExamReportsTab;\nconst AppointmentsTab = ({\n  appointments,\n  user,\n  onAppointmentCreated\n}) => {\n  _s3();\n  const [showRequestForm, setShowRequestForm] = useState(false);\n  const [doctors, setDoctors] = useState([]);\n  const [formData, setFormData] = useState({\n    doctorId: '',\n    appointmentDate: '',\n    appointmentTime: '',\n    reason: '',\n    urgency: 'Normal'\n  });\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch doctors on component mount\n  useEffect(() => {\n    fetchDoctors();\n  }, []);\n  const fetchDoctors = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/doctors`);\n      const data = await response.json();\n      if (data.success) {\n        setDoctors(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching doctors:', error);\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const appointmentData = {\n        ...formData,\n        patientNationalId: user.nationalId,\n        patientName: user.name,\n        patientEmail: user.email,\n        patientPhone: user.phone,\n        status: 'Pending'\n      };\n      const response = await fetch(`${API_BASE_URL}/appointments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(appointmentData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        alert('Appointment request submitted successfully!');\n        setShowRequestForm(false);\n        setFormData({\n          doctorId: '',\n          appointmentDate: '',\n          appointmentTime: '',\n          reason: '',\n          urgency: 'Normal'\n        });\n        onAppointmentCreated();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error submitting appointment:', error);\n      alert('Error submitting appointment request');\n    }\n  };\n  const upcomingAppointments = appointments.filter(apt => new Date(apt.appointmentDate) > new Date());\n  const pastAppointments = appointments.filter(apt => new Date(apt.appointmentDate) <= new Date());\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Your Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowRequestForm(true),\n          className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 4v16m8-8H4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 13\n          }, this), \"Request Appointment\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Upcoming Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 11\n        }, this), upcomingAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: upcomingAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-green-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900\",\n                    children: [\"Dr. \", appointment.doctorName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: appointment.specialty\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 rounded-full text-xs font-medium ${appointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' : appointment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n                children: appointment.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 44\n                }, this), \" \", new Date(appointment.appointmentDate).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Time:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 44\n                }, this), \" \", appointment.appointmentTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: \"Reason:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 44\n                }, this), \" \", appointment.reason]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 776,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 bg-gray-50 rounded-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"No upcoming appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Past Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this), pastAppointments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: pastAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-4 flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-gray-600\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-semibold text-gray-900\",\n                  children: [\"Dr. \", appointment.doctorName]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [new Date(appointment.appointmentDate).toLocaleDateString(), \" at \", appointment.appointmentTime]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${appointment.status === 'Completed' ? 'bg-blue-100 text-blue-800' : appointment.status === 'Cancelled' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`,\n              children: appointment.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 bg-gray-50 rounded-xl\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: \"No past appointments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 758,\n      columnNumber: 7\n    }, this), showRequestForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Request Appointment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowRequestForm(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Select Doctor *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"doctorId\",\n                  value: formData.doctorId,\n                  onChange: handleInputChange,\n                  required: true,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Choose a doctor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 876,\n                    columnNumber: 23\n                  }, this), doctors.map(doctor => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: doctor.id,\n                    children: [\"Dr. \", doctor.firstName, \" \", doctor.lastName, \" - \", doctor.specialty]\n                  }, doctor.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Urgency Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"urgency\",\n                  value: formData.urgency,\n                  onChange: handleInputChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Normal\",\n                    children: \"Normal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Urgent\",\n                    children: \"Urgent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 893,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Emergency\",\n                    children: \"Emergency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Preferred Date *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"appointmentDate\",\n                  value: formData.appointmentDate,\n                  onChange: handleInputChange,\n                  min: new Date().toISOString().split('T')[0],\n                  required: true,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Preferred Time *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"time\",\n                  name: \"appointmentTime\",\n                  value: formData.appointmentTime,\n                  onChange: handleInputChange,\n                  required: true,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Reason for Visit *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"reason\",\n                value: formData.reason,\n                onChange: handleInputChange,\n                rows: \"4\",\n                required: true,\n                placeholder: \"Please describe your symptoms or reason for the appointment...\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 21\n                }, this), \"Submit Request\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => setShowRequestForm(false),\n                className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 853,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 757,\n    columnNumber: 5\n  }, this);\n};\n\n// Pharmacy Tab Component\n_s3(AppointmentsTab, \"TLtSDVqQKl61zq2jQzbRSi+XwV8=\");\n_c5 = AppointmentsTab;\nconst PharmacyTab = ({\n  medicines,\n  cart,\n  addToCart,\n  removeFromCart,\n  updateCartQuantity,\n  cartTotal\n}) => {\n  _s4();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('All');\n  const [showCart, setShowCart] = useState(false);\n\n  // Filter medicines based on search and category\n  const filteredMedicines = medicines.filter(medicine => {\n    var _medicine$description;\n    const matchesSearch = medicine.name.toLowerCase().includes(searchQuery.toLowerCase()) || ((_medicine$description = medicine.description) === null || _medicine$description === void 0 ? void 0 : _medicine$description.toLowerCase().includes(searchQuery.toLowerCase()));\n    const matchesCategory = categoryFilter === 'All' || medicine.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories\n  const categories = ['All', ...new Set(medicines.map(med => med.category).filter(Boolean))];\n  const handleCheckout = async () => {\n    if (cart.length === 0) {\n      alert('Your cart is empty');\n      return;\n    }\n    try {\n      // Here you would typically process the order\n      alert(`Order placed successfully! Total: ${cartTotal.toFixed(2)} RWF`);\n      // Clear cart after successful order\n      cart.forEach(item => removeFromCart(item.id));\n      setShowCart(false);\n    } catch (error) {\n      console.error('Error processing order:', error);\n      alert('Error processing order. Please try again.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Online Pharmacy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCart(true),\n          className: \"bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 13\n          }, this), \"Cart (\", cart.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search medicines...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"absolute left-3 top-2.5 w-5 h-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: categoryFilter,\n            onChange: e => setCategoryFilter(e.target.value),\n            className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category,\n              children: category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1013,\n        columnNumber: 9\n      }, this), filteredMedicines.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: filteredMedicines.map(medicine => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-6 border border-green-100 hover:shadow-lg transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6 text-green-600\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-gray-900\",\n                  children: medicine.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: medicine.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-medium ${medicine.stock > 10 ? 'bg-green-100 text-green-800' : medicine.stock > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`,\n              children: medicine.stock > 0 ? `${medicine.stock} in stock` : 'Out of stock'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: medicine.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-bold text-green-600\",\n              children: [medicine.price, \" RWF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 19\n            }, this), medicine.dosage && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Dosage: \", medicine.dosage]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => addToCart(medicine),\n            disabled: medicine.stock === 0,\n            className: `w-full py-2 px-4 rounded-lg font-medium transition-colors ${medicine.stock > 0 ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,\n            children: medicine.stock > 0 ? 'Add to Cart' : 'Out of Stock'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 17\n          }, this)]\n        }, medicine.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-gray-400\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1090,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"No Medicines Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Try adjusting your search or filter criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 998,\n      columnNumber: 7\n    }, this), showCart && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Shopping Cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCart(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1105,\n            columnNumber: 15\n          }, this), cart.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [cart.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6 text-green-600\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1122,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1121,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1126,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [item.price, \" RWF each\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1125,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => updateCartQuantity(item.id, item.quantity - 1),\n                    className: \"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\",\n                    children: \"-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-8 text-center font-medium\",\n                    children: item.quantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1138,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => updateCartQuantity(item.id, item.quantity + 1),\n                    className: \"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1139,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1131,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-bold text-green-600 w-20 text-right\",\n                  children: [(item.price * item.quantity).toFixed(2), \" RWF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeFromCart(item.id),\n                  className: \"text-red-500 hover:text-red-700 p-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1152,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1151,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 23\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between text-xl font-bold mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Total:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600\",\n                  children: [cartTotal.toFixed(2), \" RWF\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCheckout,\n                  className: \"flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300\",\n                  children: \"Proceed to Checkout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowCart(false),\n                  className: \"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors\",\n                  children: \"Continue Shopping\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1116,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-12 h-12 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1184,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1183,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"Your cart is empty\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1187,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Add some medicines to get started.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1104,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1103,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1102,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 997,\n    columnNumber: 5\n  }, this);\n};\n_s4(PharmacyTab, \"IQvyvj4+vyoukcTENfghZo8Xh+M=\");\n_c6 = PharmacyTab;\nexport default PatientPortal;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"PatientPortal\");\n$RefreshReg$(_c2, \"LoginPrompt\");\n$RefreshReg$(_c3, \"DashboardTab\");\n$RefreshReg$(_c4, \"ExamReportsTab\");\n$RefreshReg$(_c5, \"AppointmentsTab\");\n$RefreshReg$(_c6, \"PharmacyTab\");", "map": {"version": 3, "names": ["useState", "useEffect", "useAuth", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "PatientPortal", "_s", "user", "showLoginModal", "setShowLoginModal", "examReports", "setExamReports", "appointments", "setAppointments", "medicines", "setMedicines", "cart", "setCart", "API_BASE_URL", "nationalId", "fetchExamReports", "fetchAppointments", "fetchMedicines", "setLoading", "response", "fetch", "data", "json", "success", "error", "console", "addToCart", "medicine", "existingItem", "find", "item", "id", "map", "quantity", "removeFromCart", "medicineId", "filter", "updateCartQuantity", "cartTotal", "reduce", "total", "price", "className", "children", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "setActiveTab", "activeTab", "length", "DashboardTab", "cartItems", "ExamReportsTab", "loading", "LoginPrompt", "title", "description", "onLogin", "AppointmentsTab", "onAppointmentCreated", "PharmacyTab", "userData", "onClose", "_c", "_c2", "upcomingAppointments", "apt", "Date", "appointmentDate", "slice", "recentReports", "report", "index", "examType", "examDate", "toLocaleDateString", "status", "appointment", "<PERSON><PERSON><PERSON>", "_c3", "_s2", "selectedReport", "setSelectedReport", "downloadReport", "reportContent", "patientName", "results", "recommendations", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_report$results", "substring", "_c4", "_s3", "showRequestForm", "setShowRequestForm", "doctors", "setDoctors", "formData", "setFormData", "doctorId", "appointmentTime", "reason", "urgency", "fetchDoctors", "handleInputChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "appointmentData", "patientNationalId", "patientEmail", "email", "patientPhone", "phone", "method", "headers", "JSON", "stringify", "alert", "message", "pastAppointments", "specialty", "onSubmit", "onChange", "required", "doctor", "firstName", "lastName", "min", "rows", "placeholder", "_c5", "_s4", "searchQuery", "setSearch<PERSON>uery", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "showCart", "setShowCart", "filteredMedicines", "_medicine$description", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "category", "categories", "Set", "med", "Boolean", "handleCheckout", "toFixed", "for<PERSON>ach", "stock", "dosage", "disabled", "_c6", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/pages/PatientPortal.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport Login from '../components/Login';\n\nconst PatientPortal = () => {\n  const { user } = useAuth();\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount (only if logged in)\n  useEffect(() => {\n    if (user?.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n    }\n    // Always fetch medicines for pharmacy browsing\n    fetchMedicines();\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = (medicine) => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => \n        item.id === medicine.id \n          ? { ...item, quantity: item.quantity + 1 }\n          : item\n      ));\n    } else {\n      setCart([...cart, { ...medicine, quantity: 1 }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = (medicineId) => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => \n        item.id === medicineId \n          ? { ...item, quantity: quantity }\n          : item\n      ));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50\">\n      <div className=\"max-w-7xl mx-auto px-6 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center gap-4 mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-600 to-green-600 rounded-2xl flex items-center justify-center shadow-lg\">\n              <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n            <div>\n              <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent\">\n                Patient Portal\n              </h1>\n              <p className=\"text-gray-600 text-lg\">\n                {user ? `Welcome back, ${user.name}` : 'Access your health records and services'}\n              </p>\n            </div>\n          </div>\n\n          {/* Login Button for non-authenticated users */}\n          {!user && (\n            <div className=\"mb-6\">\n              <div className=\"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl shadow-xl p-6 text-white\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h2 className=\"text-2xl font-bold mb-2\">Sign In to Access Your Health Records</h2>\n                    <p className=\"text-blue-100\">\n                      Login to view your exam reports, manage appointments, and access personalized healthcare services\n                    </p>\n                  </div>\n                  <button\n                    onClick={() => setShowLoginModal(true)}\n                    className=\"bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n                    </svg>\n                    Sign In\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Navigation Tabs */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-2 border border-gray-100\">\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'dashboard'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                  Dashboard\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('reports')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'reports'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                  Exam Reports\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('appointments')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'appointments'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                  Appointments\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('pharmacy')}\n                className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\n                  activeTab === 'pharmacy'\n                    ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white shadow-lg'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                <span className=\"flex items-center gap-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                  </svg>\n                  Pharmacy\n                  {cart.length > 0 && (\n                    <span className=\"bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n                      {cart.length}\n                    </span>\n                  )}\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"space-y-8\">\n          {activeTab === 'dashboard' && (\n            <DashboardTab \n              user={user}\n              examReports={examReports}\n              appointments={appointments}\n              cartItems={cart.length}\n            />\n          )}\n          \n          {activeTab === 'reports' && (\n            user ? (\n              <ExamReportsTab\n                examReports={examReports}\n                loading={loading}\n              />\n            ) : (\n              <LoginPrompt\n                title=\"View Your Exam Reports\"\n                description=\"Login to access your medical exam reports and test results\"\n                onLogin={() => setShowLoginModal(true)}\n              />\n            )\n          )}\n\n          {activeTab === 'appointments' && (\n            user ? (\n              <AppointmentsTab\n                appointments={appointments}\n                user={user}\n                onAppointmentCreated={fetchAppointments}\n              />\n            ) : (\n              <LoginPrompt\n                title=\"Manage Your Appointments\"\n                description=\"Login to view your appointments and request new ones\"\n                onLogin={() => setShowLoginModal(true)}\n              />\n            )\n          )}\n          \n          {activeTab === 'pharmacy' && (\n            <PharmacyTab \n              medicines={medicines}\n              cart={cart}\n              addToCart={addToCart}\n              removeFromCart={removeFromCart}\n              updateCartQuantity={updateCartQuantity}\n              cartTotal={cartTotal}\n            />\n          )}\n        </div>\n\n        {/* Login Modal */}\n        {showLoginModal && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n            <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                  <h3 className=\"text-2xl font-bold text-gray-900\">Patient Login</h3>\n                  <button\n                    onClick={() => setShowLoginModal(false)}\n                    className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                  >\n                    ×\n                  </button>\n                </div>\n\n                <Login\n                  onLogin={(userData) => {\n                    setShowLoginModal(false);\n                    // The user state will be updated automatically by the auth context\n                  }}\n                  onClose={() => setShowLoginModal(false)}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Login Prompt Component\nconst LoginPrompt = ({ title, description, onLogin }) => {\n  return (\n    <div className=\"bg-white rounded-2xl shadow-lg p-8 border border-gray-100 text-center\">\n      <div className=\"w-24 h-24 bg-gradient-to-br from-blue-600 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6\">\n        <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n        </svg>\n      </div>\n      <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{title}</h3>\n      <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">{description}</p>\n      <button\n        onClick={onLogin}\n        className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2 mx-auto\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n        </svg>\n        Sign In to Continue\n      </button>\n    </div>\n  );\n};\n\n// Dashboard Tab Component\nconst DashboardTab = ({ user, examReports, appointments, cartItems }) => {\n  const upcomingAppointments = appointments.filter(apt =>\n    new Date(apt.appointmentDate) > new Date()\n  ).slice(0, 3);\n\n  const recentReports = examReports.slice(0, 3);\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-green-600 rounded-2xl shadow-xl p-8 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-3xl font-bold mb-2\">Welcome to Your Health Portal</h2>\n            <p className=\"text-blue-100 text-lg\">\n              Manage your health records, appointments, and prescriptions all in one place\n            </p>\n          </div>\n          <div className=\"hidden md:block\">\n            <div className=\"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-12 h-12 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-blue-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Reports</p>\n              <p className=\"text-3xl font-bold text-blue-600\">{examReports.length}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-green-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Appointments</p>\n              <p className=\"text-3xl font-bold text-green-600\">{appointments.length}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-yellow-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Upcoming</p>\n              <p className=\"text-3xl font-bold text-yellow-600\">{upcomingAppointments.length}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-purple-100\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Cart Items</p>\n              <p className=\"text-3xl font-bold text-purple-600\">{cartItems}</p>\n            </div>\n            <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Recent Reports */}\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Recent Exam Reports</h3>\n          {recentReports.length > 0 ? (\n            <div className=\"space-y-3\">\n              {recentReports.map((report, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">{report.examType}</p>\n                    <p className=\"text-sm text-gray-600\">{new Date(report.examDate).toLocaleDateString()}</p>\n                  </div>\n                  <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                    {report.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500 text-center py-8\">No exam reports available</p>\n          )}\n        </div>\n\n        {/* Upcoming Appointments */}\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n          <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Upcoming Appointments</h3>\n          {upcomingAppointments.length > 0 ? (\n            <div className=\"space-y-3\">\n              {upcomingAppointments.map((appointment, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">Dr. {appointment.doctorName}</p>\n                    <p className=\"text-sm text-gray-600\">{new Date(appointment.appointmentDate).toLocaleDateString()}</p>\n                  </div>\n                  <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium\">\n                    {appointment.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500 text-center py-8\">No upcoming appointments</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Exam Reports Tab Component\nconst ExamReportsTab = ({ examReports, loading }) => {\n  const [selectedReport, setSelectedReport] = useState(null);\n\n  const downloadReport = (report) => {\n    // Create a simple text report\n    const reportContent = `\nMEDICAL EXAM REPORT\n==================\n\nPatient: ${report.patientName}\nNational ID: ${report.nationalId}\nExam Type: ${report.examType}\nExam Date: ${new Date(report.examDate).toLocaleDateString()}\nDoctor: ${report.doctorName}\nStatus: ${report.status}\n\nRESULTS:\n${report.results}\n\nRECOMMENDATIONS:\n${report.recommendations || 'No specific recommendations'}\n\nReport generated on: ${new Date().toLocaleDateString()}\nHealthCarePro Medical Center\n    `;\n\n    const blob = new Blob([reportContent], { type: 'text/plain' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `exam-report-${report.examType}-${new Date(report.examDate).toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Your Exam Reports</h2>\n          <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            {examReports.length} Reports Available\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <span className=\"ml-3 text-gray-600\">Loading reports...</span>\n          </div>\n        ) : examReports.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {examReports.map((report, index) => (\n              <div key={index} className=\"bg-gradient-to-br from-blue-50 to-green-50 rounded-xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-bold text-gray-900\">{report.examType}</h3>\n                      <p className=\"text-sm text-gray-600\">{new Date(report.examDate).toLocaleDateString()}</p>\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    report.status === 'Completed' ? 'bg-green-100 text-green-800' :\n                    report.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {report.status}\n                  </span>\n                </div>\n\n                <div className=\"space-y-2 mb-4\">\n                  <p className=\"text-sm\"><span className=\"font-medium\">Doctor:</span> {report.doctorName}</p>\n                  <p className=\"text-sm\"><span className=\"font-medium\">Results:</span> {report.results?.substring(0, 100)}...</p>\n                </div>\n\n                <div className=\"flex gap-2\">\n                  <button\n                    onClick={() => setSelectedReport(report)}\n                    className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                  >\n                    View Details\n                  </button>\n                  <button\n                    onClick={() => downloadReport(report)}\n                    className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Exam Reports</h3>\n            <p className=\"text-gray-600\">You don't have any exam reports yet. They will appear here once available.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Report Details Modal */}\n      {selectedReport && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Exam Report Details</h3>\n                <button\n                  onClick={() => setSelectedReport(null)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Exam Type</label>\n                    <p className=\"text-gray-900 font-semibold\">{selectedReport.examType}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Date</label>\n                    <p className=\"text-gray-900\">{new Date(selectedReport.examDate).toLocaleDateString()}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Doctor</label>\n                    <p className=\"text-gray-900\">{selectedReport.doctorName}</p>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${\n                      selectedReport.status === 'Completed' ? 'bg-green-100 text-green-800' :\n                      selectedReport.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-gray-100 text-gray-800'\n                    }`}>\n                      {selectedReport.status}\n                    </span>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Results</label>\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <p className=\"text-gray-900 whitespace-pre-wrap\">{selectedReport.results}</p>\n                  </div>\n                </div>\n\n                {selectedReport.recommendations && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Recommendations</label>\n                    <div className=\"bg-blue-50 rounded-lg p-4\">\n                      <p className=\"text-gray-900 whitespace-pre-wrap\">{selectedReport.recommendations}</p>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    onClick={() => downloadReport(selectedReport)}\n                    className=\"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                    Download Report\n                  </button>\n                  <button\n                    onClick={() => setSelectedReport(null)}\n                    className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\"\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Appointments Tab Component\nconst AppointmentsTab = ({ appointments, user, onAppointmentCreated }) => {\n  const [showRequestForm, setShowRequestForm] = useState(false);\n  const [doctors, setDoctors] = useState([]);\n  const [formData, setFormData] = useState({\n    doctorId: '',\n    appointmentDate: '',\n    appointmentTime: '',\n    reason: '',\n    urgency: 'Normal'\n  });\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch doctors on component mount\n  useEffect(() => {\n    fetchDoctors();\n  }, []);\n\n  const fetchDoctors = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/doctors`);\n      const data = await response.json();\n      if (data.success) {\n        setDoctors(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching doctors:', error);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const appointmentData = {\n        ...formData,\n        patientNationalId: user.nationalId,\n        patientName: user.name,\n        patientEmail: user.email,\n        patientPhone: user.phone,\n        status: 'Pending'\n      };\n\n      const response = await fetch(`${API_BASE_URL}/appointments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(appointmentData),\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        alert('Appointment request submitted successfully!');\n        setShowRequestForm(false);\n        setFormData({\n          doctorId: '',\n          appointmentDate: '',\n          appointmentTime: '',\n          reason: '',\n          urgency: 'Normal'\n        });\n        onAppointmentCreated();\n      } else {\n        alert(`Error: ${data.message}`);\n      }\n    } catch (error) {\n      console.error('Error submitting appointment:', error);\n      alert('Error submitting appointment request');\n    }\n  };\n\n  const upcomingAppointments = appointments.filter(apt =>\n    new Date(apt.appointmentDate) > new Date()\n  );\n\n  const pastAppointments = appointments.filter(apt =>\n    new Date(apt.appointmentDate) <= new Date()\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Your Appointments</h2>\n          <button\n            onClick={() => setShowRequestForm(true)}\n            className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Request Appointment\n          </button>\n        </div>\n\n        {/* Upcoming Appointments */}\n        <div className=\"mb-8\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Upcoming Appointments</h3>\n          {upcomingAppointments.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {upcomingAppointments.map((appointment, index) => (\n                <div key={index} className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-100\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                        <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-bold text-gray-900\">Dr. {appointment.doctorName}</h4>\n                        <p className=\"text-sm text-gray-600\">{appointment.specialty}</p>\n                      </div>\n                    </div>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      appointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' :\n                      appointment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-red-100 text-red-800'\n                    }`}>\n                      {appointment.status}\n                    </span>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <p className=\"text-sm\"><span className=\"font-medium\">Date:</span> {new Date(appointment.appointmentDate).toLocaleDateString()}</p>\n                    <p className=\"text-sm\"><span className=\"font-medium\">Time:</span> {appointment.appointmentTime}</p>\n                    <p className=\"text-sm\"><span className=\"font-medium\">Reason:</span> {appointment.reason}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 bg-gray-50 rounded-xl\">\n              <p className=\"text-gray-500\">No upcoming appointments</p>\n            </div>\n          )}\n        </div>\n\n        {/* Past Appointments */}\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Past Appointments</h3>\n          {pastAppointments.length > 0 ? (\n            <div className=\"space-y-3\">\n              {pastAppointments.map((appointment, index) => (\n                <div key={index} className=\"bg-gray-50 rounded-lg p-4 flex items-center justify-between\">\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"font-semibold text-gray-900\">Dr. {appointment.doctorName}</p>\n                      <p className=\"text-sm text-gray-600\">{new Date(appointment.appointmentDate).toLocaleDateString()} at {appointment.appointmentTime}</p>\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    appointment.status === 'Completed' ? 'bg-blue-100 text-blue-800' :\n                    appointment.status === 'Cancelled' ? 'bg-red-100 text-red-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {appointment.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 bg-gray-50 rounded-xl\">\n              <p className=\"text-gray-500\">No past appointments</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Request Appointment Modal */}\n      {showRequestForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Request Appointment</h3>\n                <button\n                  onClick={() => setShowRequestForm(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Select Doctor *</label>\n                    <select\n                      name=\"doctorId\"\n                      value={formData.doctorId}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"\">Choose a doctor</option>\n                      {doctors.map((doctor) => (\n                        <option key={doctor.id} value={doctor.id}>\n                          Dr. {doctor.firstName} {doctor.lastName} - {doctor.specialty}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Urgency Level</label>\n                    <select\n                      name=\"urgency\"\n                      value={formData.urgency}\n                      onChange={handleInputChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    >\n                      <option value=\"Normal\">Normal</option>\n                      <option value=\"Urgent\">Urgent</option>\n                      <option value=\"Emergency\">Emergency</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Preferred Date *</label>\n                    <input\n                      type=\"date\"\n                      name=\"appointmentDate\"\n                      value={formData.appointmentDate}\n                      onChange={handleInputChange}\n                      min={new Date().toISOString().split('T')[0]}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Preferred Time *</label>\n                    <input\n                      type=\"time\"\n                      name=\"appointmentTime\"\n                      value={formData.appointmentTime}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Reason for Visit *</label>\n                  <textarea\n                    name=\"reason\"\n                    value={formData.reason}\n                    onChange={handleInputChange}\n                    rows=\"4\"\n                    required\n                    placeholder=\"Please describe your symptoms or reason for the appointment...\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n\n                <div className=\"flex gap-3 pt-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                    Submit Request\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowRequestForm(false)}\n                    className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Pharmacy Tab Component\nconst PharmacyTab = ({ medicines, cart, addToCart, removeFromCart, updateCartQuantity, cartTotal }) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('All');\n  const [showCart, setShowCart] = useState(false);\n\n  // Filter medicines based on search and category\n  const filteredMedicines = medicines.filter(medicine => {\n    const matchesSearch = medicine.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         medicine.description?.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesCategory = categoryFilter === 'All' || medicine.category === categoryFilter;\n    return matchesSearch && matchesCategory;\n  });\n\n  // Get unique categories\n  const categories = ['All', ...new Set(medicines.map(med => med.category).filter(Boolean))];\n\n  const handleCheckout = async () => {\n    if (cart.length === 0) {\n      alert('Your cart is empty');\n      return;\n    }\n\n    try {\n      // Here you would typically process the order\n      alert(`Order placed successfully! Total: ${cartTotal.toFixed(2)} RWF`);\n      // Clear cart after successful order\n      cart.forEach(item => removeFromCart(item.id));\n      setShowCart(false);\n    } catch (error) {\n      console.error('Error processing order:', error);\n      alert('Error processing order. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Online Pharmacy</h2>\n          <button\n            onClick={() => setShowCart(true)}\n            className=\"bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\n            </svg>\n            Cart ({cart.length})\n          </button>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"flex flex-col md:flex-row gap-4 mb-6\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search medicines...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <svg className=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z\" />\n              </svg>\n            </div>\n          </div>\n          <div>\n            <select\n              value={categoryFilter}\n              onChange={(e) => setCategoryFilter(e.target.value)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {categories.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Medicines Grid */}\n        {filteredMedicines.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredMedicines.map((medicine) => (\n              <div key={medicine.id} className=\"bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-6 border border-green-100 hover:shadow-lg transition-all duration-300\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                      <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"font-bold text-gray-900\">{medicine.name}</h3>\n                      <p className=\"text-sm text-gray-600\">{medicine.category}</p>\n                    </div>\n                  </div>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                    medicine.stock > 10 ? 'bg-green-100 text-green-800' :\n                    medicine.stock > 0 ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-red-100 text-red-800'\n                  }`}>\n                    {medicine.stock > 0 ? `${medicine.stock} in stock` : 'Out of stock'}\n                  </span>\n                </div>\n\n                <div className=\"space-y-2 mb-4\">\n                  <p className=\"text-sm text-gray-700\">{medicine.description}</p>\n                  <p className=\"text-lg font-bold text-green-600\">{medicine.price} RWF</p>\n                  {medicine.dosage && <p className=\"text-sm text-gray-600\">Dosage: {medicine.dosage}</p>}\n                </div>\n\n                <button\n                  onClick={() => addToCart(medicine)}\n                  disabled={medicine.stock === 0}\n                  className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${\n                    medicine.stock > 0\n                      ? 'bg-green-600 hover:bg-green-700 text-white'\n                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  }`}\n                >\n                  {medicine.stock > 0 ? 'Add to Cart' : 'Out of Stock'}\n                </button>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Medicines Found</h3>\n            <p className=\"text-gray-600\">Try adjusting your search or filter criteria.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Shopping Cart Modal */}\n      {showCart && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Shopping Cart</h3>\n                <button\n                  onClick={() => setShowCart(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              {cart.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {cart.map((item) => (\n                    <div key={item.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                          <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <h4 className=\"font-semibold text-gray-900\">{item.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{item.price} RWF each</p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"flex items-center gap-2\">\n                          <button\n                            onClick={() => updateCartQuantity(item.id, item.quantity - 1)}\n                            className=\"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\"\n                          >\n                            -\n                          </button>\n                          <span className=\"w-8 text-center font-medium\">{item.quantity}</span>\n                          <button\n                            onClick={() => updateCartQuantity(item.id, item.quantity + 1)}\n                            className=\"w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-lg flex items-center justify-center\"\n                          >\n                            +\n                          </button>\n                        </div>\n                        <p className=\"font-bold text-green-600 w-20 text-right\">{(item.price * item.quantity).toFixed(2)} RWF</p>\n                        <button\n                          onClick={() => removeFromCart(item.id)}\n                          className=\"text-red-500 hover:text-red-700 p-1\"\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                          </svg>\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n\n                  <div className=\"border-t pt-4\">\n                    <div className=\"flex justify-between text-xl font-bold mb-4\">\n                      <span>Total:</span>\n                      <span className=\"text-green-600\">{cartTotal.toFixed(2)} RWF</span>\n                    </div>\n                    <div className=\"flex gap-3\">\n                      <button\n                        onClick={handleCheckout}\n                        className=\"flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300\"\n                      >\n                        Proceed to Checkout\n                      </button>\n                      <button\n                        onClick={() => setShowCart(false)}\n                        className=\"bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors\"\n                      >\n                        Continue Shopping\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center py-12\">\n                  <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v5a2 2 0 01-2 2H9a2 2 0 01-2-2v-5m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Your cart is empty</h3>\n                  <p className=\"text-gray-600\">Add some medicines to get started.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PatientPortal;\n"], "mappings": ";;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAEpC,MAAMmB,YAAY,GAAG,2BAA2B;;EAEhD;EACAlB,SAAS,CAAC,MAAM;IACd,IAAIO,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEY,UAAU,EAAE;MACpBC,gBAAgB,CAAC,CAAC;MAClBC,iBAAiB,CAAC,CAAC;IACrB;IACA;IACAC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMa,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,kBAAkBX,IAAI,CAACY,UAAU,EAAE,CAAC;MAChF,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBjB,cAAc,CAACe,IAAI,CAACA,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMF,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,yBAAyBX,IAAI,CAACY,UAAU,EAAE,CAAC;MACvF,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBf,eAAe,CAACa,IAAI,CAACA,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMP,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,WAAW,CAAC;MACxD,MAAMQ,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBb,YAAY,CAACW,IAAI,CAACA,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAME,SAAS,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,YAAY,GAAGjB,IAAI,CAACkB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,CAAC;IAC/D,IAAIH,YAAY,EAAE;MAChBhB,OAAO,CAACD,IAAI,CAACqB,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,GACnB;QAAE,GAAGD,IAAI;QAAEG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG;MAAE,CAAC,GACxCH,IACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlB,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGgB,QAAQ;QAAEM,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrCvB,OAAO,CAACD,IAAI,CAACyB,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKI,UAAU,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACF,UAAU,EAAEF,QAAQ,KAAK;IACnD,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBC,cAAc,CAACC,UAAU,CAAC;IAC5B,CAAC,MAAM;MACLvB,OAAO,CAACD,IAAI,CAACqB,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKI,UAAU,GAClB;QAAE,GAAGL,IAAI;QAAEG,QAAQ,EAAEA;MAAS,CAAC,GAC/BH,IACN,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAG3B,IAAI,CAAC4B,MAAM,CAAC,CAACC,KAAK,EAAEV,IAAI,KAAKU,KAAK,GAAIV,IAAI,CAACW,KAAK,GAAGX,IAAI,CAACG,QAAS,EAAE,CAAC,CAAC;EAEvF,oBACElC,OAAA;IAAK2C,SAAS,EAAC,mEAAmE;IAAAC,QAAA,eAChF5C,OAAA;MAAK2C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1C5C,OAAA;QAAK2C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB5C,OAAA;UAAK2C,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C5C,OAAA;YAAK2C,SAAS,EAAC,+GAA+G;YAAAC,QAAA,eAC5H5C,OAAA;cAAK2C,SAAS,EAAC,oBAAoB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACvG5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAqE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvD,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAI2C,SAAS,EAAC,8FAA8F;cAAAC,QAAA,EAAC;YAE7G;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvD,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACjCzC,IAAI,GAAG,iBAAiBA,IAAI,CAACqD,IAAI,EAAE,GAAG;YAAyC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAACpD,IAAI,iBACJH,OAAA;UAAK2C,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5C,OAAA;YAAK2C,SAAS,EAAC,kFAAkF;YAAAC,QAAA,eAC/F5C,OAAA;cAAK2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD5C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAI2C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClFvD,OAAA;kBAAG2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAE7B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNvD,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMpD,iBAAiB,CAAC,IAAI,CAAE;gBACvCsC,SAAS,EAAC,oLAAoL;gBAAAC,QAAA,gBAE9L5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAA8F;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnJ,CAAC,WAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDvD,OAAA;UAAK2C,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxE5C,OAAA;YAAK2C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5C,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,WAAW,CAAE;cACzCf,SAAS,EAAE,kEACTgB,SAAS,KAAK,WAAW,GACrB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAf,QAAA,eAEH5C,OAAA;gBAAM2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3P,CAAC,aAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACTvD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,SAAS,CAAE;cACvCf,SAAS,EAAE,kEACTgB,SAAS,KAAK,SAAS,GACnB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAf,QAAA,eAEH5C,OAAA;gBAAM2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3K,CAAC,gBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACTvD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,cAAc,CAAE;cAC5Cf,SAAS,EAAE,kEACTgB,SAAS,KAAK,cAAc,GACxB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAf,QAAA,eAEH5C,OAAA;gBAAM2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAwF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC,gBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACTvD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,UAAU,CAAE;cACxCf,SAAS,EAAE,kEACTgB,SAAS,KAAK,UAAU,GACpB,kEAAkE,GAClE,iCAAiC,EACpC;cAAAf,QAAA,eAEH5C,OAAA;gBAAM2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACvC5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5T,CAAC,YAEN,EAAC3C,IAAI,CAACgD,MAAM,GAAG,CAAC,iBACd5D,OAAA;kBAAM2C,SAAS,EAAC,qFAAqF;kBAAAC,QAAA,EAClGhC,IAAI,CAACgD;gBAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvD,OAAA;QAAK2C,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBe,SAAS,KAAK,WAAW,iBACxB3D,OAAA,CAAC6D,YAAY;UACX1D,IAAI,EAAEA,IAAK;UACXG,WAAW,EAAEA,WAAY;UACzBE,YAAY,EAAEA,YAAa;UAC3BsD,SAAS,EAAElD,IAAI,CAACgD;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACF,EAEAI,SAAS,KAAK,SAAS,KACtBxD,IAAI,gBACFH,OAAA,CAAC+D,cAAc;UACbzD,WAAW,EAAEA,WAAY;UACzB0D,OAAO,EAAEA;QAAQ;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,gBAEFvD,OAAA,CAACiE,WAAW;UACVC,KAAK,EAAC,wBAAwB;UAC9BC,WAAW,EAAC,4DAA4D;UACxEC,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC,IAAI;QAAE;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF,CACF,EAEAI,SAAS,KAAK,cAAc,KAC3BxD,IAAI,gBACFH,OAAA,CAACqE,eAAe;UACd7D,YAAY,EAAEA,YAAa;UAC3BL,IAAI,EAAEA,IAAK;UACXmE,oBAAoB,EAAErD;QAAkB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,gBAEFvD,OAAA,CAACiE,WAAW;UACVC,KAAK,EAAC,0BAA0B;UAChCC,WAAW,EAAC,sDAAsD;UAClEC,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC,IAAI;QAAE;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF,CACF,EAEAI,SAAS,KAAK,UAAU,iBACvB3D,OAAA,CAACuE,WAAW;UACV7D,SAAS,EAAEA,SAAU;UACrBE,IAAI,EAAEA,IAAK;UACXe,SAAS,EAAEA,SAAU;UACrBQ,cAAc,EAAEA,cAAe;UAC/BG,kBAAkB,EAAEA,kBAAmB;UACvCC,SAAS,EAAEA;QAAU;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLnD,cAAc,iBACbJ,OAAA;QAAK2C,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7F5C,OAAA;UAAK2C,SAAS,EAAC,iDAAiD;UAAAC,QAAA,eAC9D5C,OAAA;YAAK2C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB5C,OAAA;cAAK2C,SAAS,EAAC,sEAAsE;cAAAC,QAAA,gBACnF5C,OAAA;gBAAI2C,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEvD,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMpD,iBAAiB,CAAC,KAAK,CAAE;gBACxCsC,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,EAC7H;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENvD,OAAA,CAACF,KAAK;cACJsE,OAAO,EAAGI,QAAQ,IAAK;gBACrBnE,iBAAiB,CAAC,KAAK,CAAC;gBACxB;cACF,CAAE;cACFoE,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAAC,KAAK;YAAE;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAArD,EAAA,CA5SMD,aAAa;EAAA,QACAJ,OAAO;AAAA;AAAA6E,EAAA,GADpBzE,aAAa;AA6SnB,MAAMgE,WAAW,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAQ,CAAC,KAAK;EACvD,oBACEpE,OAAA;IAAK2C,SAAS,EAAC,uEAAuE;IAAAC,QAAA,gBACpF5C,OAAA;MAAK2C,SAAS,EAAC,mHAAmH;MAAAC,QAAA,eAChI5C,OAAA;QAAK2C,SAAS,EAAC,sBAAsB;QAACE,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,OAAO,EAAC,WAAW;QAAAJ,QAAA,eACzG5C,OAAA;UAAMiD,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,CAAC,EAAC;QAAsG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3J;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvD,OAAA;MAAI2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAEsB;IAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAClEvD,OAAA;MAAG2C,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAAEuB;IAAW;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpEvD,OAAA;MACEyD,OAAO,EAAEW,OAAQ;MACjBzB,SAAS,EAAC,kPAAkP;MAAAC,QAAA,gBAE5P5C,OAAA;QAAK2C,SAAS,EAAC,SAAS;QAACE,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,OAAO,EAAC,WAAW;QAAAJ,QAAA,eAC5F5C,OAAA;UAAMiD,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,CAAC,EAAC;QAA8F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnJ,CAAC,uBAER;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAoB,GAAA,GAvBMV,WAAW;AAwBjB,MAAMJ,YAAY,GAAGA,CAAC;EAAE1D,IAAI;EAAEG,WAAW;EAAEE,YAAY;EAAEsD;AAAU,CAAC,KAAK;EACvE,MAAMc,oBAAoB,GAAGpE,YAAY,CAAC6B,MAAM,CAACwC,GAAG,IAClD,IAAIC,IAAI,CAACD,GAAG,CAACE,eAAe,CAAC,GAAG,IAAID,IAAI,CAAC,CAC3C,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEb,MAAMC,aAAa,GAAG3E,WAAW,CAAC0E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAE7C,oBACEhF,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5C,OAAA;MAAK2C,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAC/F5C,OAAA;QAAK2C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5C,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAI2C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAA6B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EvD,OAAA;YAAG2C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNvD,OAAA;UAAK2C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B5C,OAAA;YAAK2C,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF5C,OAAA;cAAK2C,SAAS,EAAC,sBAAsB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eACzG5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA6H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD5C,OAAA;QAAK2C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE5C,OAAA;UAAK2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAG2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClEvD,OAAA;cAAG2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEtC,WAAW,CAACsD;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACNvD,OAAA;YAAK2C,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF5C,OAAA;cAAK2C,SAAS,EAAC,uBAAuB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC1G5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAsH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvD,OAAA;QAAK2C,SAAS,EAAC,4DAA4D;QAAAC,QAAA,eACzE5C,OAAA;UAAK2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAG2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEvD,OAAA;cAAG2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEpC,YAAY,CAACoD;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNvD,OAAA;YAAK2C,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjF5C,OAAA;cAAK2C,SAAS,EAAC,wBAAwB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC3G5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAwF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvD,OAAA;QAAK2C,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1E5C,OAAA;UAAK2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAG2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7DvD,OAAA;cAAG2C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEgC,oBAAoB,CAAChB;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACNvD,OAAA;YAAK2C,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF5C,OAAA;cAAK2C,SAAS,EAAC,yBAAyB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5G5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvD,OAAA;QAAK2C,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1E5C,OAAA;UAAK2C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAG2C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/DvD,OAAA;cAAG2C,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEkB;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNvD,OAAA;YAAK2C,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF5C,OAAA;cAAK2C,SAAS,EAAC,yBAAyB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5G5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAwI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7L;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD5C,OAAA;QAAK2C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE5C,OAAA;UAAI2C,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5E0B,aAAa,CAACrB,MAAM,GAAG,CAAC,gBACvB5D,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBqC,aAAa,CAAChD,GAAG,CAAC,CAACiD,MAAM,EAAEC,KAAK,kBAC/BnF,OAAA;YAAiB2C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBACtF5C,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAG2C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAEsC,MAAM,CAACE;cAAQ;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEvD,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACNvD,OAAA;cAAM2C,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EACnFsC,MAAM,CAACK;YAAM;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA,GAPC4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENvD,OAAA;UAAG2C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNvD,OAAA;QAAK2C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE5C,OAAA;UAAI2C,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAqB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9EqB,oBAAoB,CAAChB,MAAM,GAAG,CAAC,gBAC9B5D,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBgC,oBAAoB,CAAC3C,GAAG,CAAC,CAACuD,WAAW,EAAEL,KAAK,kBAC3CnF,OAAA;YAAiB2C,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBACvF5C,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAG2C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GAAC,MAAI,EAAC4C,WAAW,CAACC,UAAU;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3EvD,OAAA;gBAAG2C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACU,WAAW,CAACT,eAAe,CAAC,CAACO,kBAAkB,CAAC;cAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNvD,OAAA;cAAM2C,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EACrF4C,WAAW,CAACD;YAAM;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA,GAPC4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENvD,OAAA;UAAG2C,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAAwB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC1E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAmC,GAAA,GAzIM7B,YAAY;AA0IlB,MAAME,cAAc,GAAGA,CAAC;EAAEzD,WAAW;EAAE0D;AAAQ,CAAC,KAAK;EAAA2B,GAAA;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlG,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMmG,cAAc,GAAIZ,MAAM,IAAK;IACjC;IACA,MAAMa,aAAa,GAAG;AAC1B;AACA;AACA;AACA,WAAWb,MAAM,CAACc,WAAW;AAC7B,eAAed,MAAM,CAACnE,UAAU;AAChC,aAAamE,MAAM,CAACE,QAAQ;AAC5B,aAAa,IAAIN,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAACC,kBAAkB,CAAC,CAAC;AAC3D,UAAUJ,MAAM,CAACO,UAAU;AAC3B,UAAUP,MAAM,CAACK,MAAM;AACvB;AACA;AACA,EAAEL,MAAM,CAACe,OAAO;AAChB;AACA;AACA,EAAEf,MAAM,CAACgB,eAAe,IAAI,6BAA6B;AACzD;AACA,uBAAuB,IAAIpB,IAAI,CAAC,CAAC,CAACQ,kBAAkB,CAAC,CAAC;AACtD;AACA,KAAK;IAED,MAAMa,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,aAAa,CAAC,EAAE;MAAEM,IAAI,EAAE;IAAa,CAAC,CAAC;IAC9D,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,eAAe5B,MAAM,CAACE,QAAQ,IAAI,IAAIN,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAAC0B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IAC1GL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,CAAC,CAAC;IAC5BA,CAAC,CAACS,KAAK,CAAC,CAAC;IACTR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,CAAC,CAAC;IAC5BH,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;EACjC,CAAC;EAED,oBACEtG,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAK2C,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxE5C,OAAA;QAAK2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5C,OAAA;UAAI2C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEvD,OAAA;UAAK2C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5D5C,OAAA;YAAK2C,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5F5C,OAAA;cAAMiD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K,CAAC,EACLjD,WAAW,CAACsD,MAAM,EAAC,oBACtB;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELS,OAAO,gBACNhE,OAAA;QAAK2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5C,OAAA;UAAK2C,SAAS,EAAC;QAA8D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFvD,OAAA;UAAM2C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GACJjD,WAAW,CAACsD,MAAM,GAAG,CAAC,gBACxB5D,OAAA;QAAK2C,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEtC,WAAW,CAAC2B,GAAG,CAAC,CAACiD,MAAM,EAAEC,KAAK;UAAA,IAAAmC,eAAA;UAAA,oBAC7BtH,OAAA;YAAiB2C,SAAS,EAAC,8HAA8H;YAAAC,QAAA,gBACvJ5C,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5C,OAAA;gBAAK2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC5C,OAAA;kBAAK2C,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,eAChF5C,OAAA;oBAAK2C,SAAS,EAAC,uBAAuB;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC1G5C,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAsH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3K;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAI2C,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAEsC,MAAM,CAACE;kBAAQ;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9DvD,OAAA;oBAAG2C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACI,MAAM,CAACG,QAAQ,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAM2C,SAAS,EAAE,8CACfuC,MAAM,CAACK,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC7DL,MAAM,CAACK,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAC7D,2BAA2B,EAC1B;gBAAA3C,QAAA,EACAsC,MAAM,CAACK;cAAM;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENvD,OAAA;cAAK2C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5C,OAAA;gBAAG2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC5C,OAAA;kBAAM2C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC2B,MAAM,CAACO,UAAU;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FvD,OAAA;gBAAG2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC5C,OAAA;kBAAM2C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,GAAA+D,eAAA,GAACpC,MAAM,CAACe,OAAO,cAAAqB,eAAA,uBAAdA,eAAA,CAAgBC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eAENvD,OAAA;cAAK2C,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB5C,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMoC,iBAAiB,CAACX,MAAM,CAAE;gBACzCvC,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,EACvH;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvD,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMqC,cAAc,CAACZ,MAAM,CAAE;gBACtCvC,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,eAEjH5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAiI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GA1CE4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2CV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENvD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UAAK2C,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/F5C,OAAA;YAAK2C,SAAS,EAAC,yBAAyB;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5G5C,OAAA;cAAMiD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvD,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EvD,OAAA;UAAG2C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0E;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLqC,cAAc,iBACb5F,OAAA;MAAK2C,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5C,OAAA;QAAK2C,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F5C,OAAA;UAAK2C,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5C,OAAA;YAAK2C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF5C,OAAA;cAAI2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEvD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMoC,iBAAiB,CAAC,IAAI,CAAE;cACvClD,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENvD,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5C,OAAA;cAAK2C,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC5C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5EvD,OAAA;kBAAG2C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEgD,cAAc,CAACR;gBAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvEvD,OAAA;kBAAG2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAE,IAAIkC,IAAI,CAACc,cAAc,CAACP,QAAQ,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEvD,OAAA;kBAAG2C,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEgD,cAAc,CAACH;gBAAU;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEvD,OAAA;kBAAM2C,SAAS,EAAE,2DACfiD,cAAc,CAACL,MAAM,KAAK,WAAW,GAAG,6BAA6B,GACrEK,cAAc,CAACL,MAAM,KAAK,SAAS,GAAG,+BAA+B,GACrE,2BAA2B,EAC1B;kBAAA3C,QAAA,EACAgD,cAAc,CAACL;gBAAM;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvD,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAO2C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EvD,OAAA;gBAAK2C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC5C,OAAA;kBAAG2C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEgD,cAAc,CAACK;gBAAO;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELqC,cAAc,CAACM,eAAe,iBAC7BlG,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAO2C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFvD,OAAA;gBAAK2C,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC5C,OAAA;kBAAG2C,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEgD,cAAc,CAACM;gBAAe;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAEDvD,OAAA;cAAK2C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5C,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMqC,cAAc,CAACF,cAAc,CAAE;gBAC9CjD,SAAS,EAAC,uHAAuH;gBAAAC,QAAA,gBAEjI5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAiI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtL,CAAC,mBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvD,OAAA;gBACEyD,OAAO,EAAEA,CAAA,KAAMoC,iBAAiB,CAAC,IAAI,CAAE;gBACvClD,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAC3G;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAoC,GAAA,CAxMM5B,cAAc;AAAAyD,GAAA,GAAdzD,cAAc;AAyMpB,MAAMM,eAAe,GAAGA,CAAC;EAAE7D,YAAY;EAAEL,IAAI;EAAEmE;AAAqB,CAAC,KAAK;EAAAmD,GAAA;EACxE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhI,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiI,OAAO,EAAEC,UAAU,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmI,QAAQ,EAAEC,WAAW,CAAC,GAAGpI,QAAQ,CAAC;IACvCqI,QAAQ,EAAE,EAAE;IACZjD,eAAe,EAAE,EAAE;IACnBkD,eAAe,EAAE,EAAE;IACnBC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMrH,YAAY,GAAG,2BAA2B;;EAEhD;EACAlB,SAAS,CAAC,MAAM;IACdwI,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMhH,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,UAAU,CAAC;MACvD,MAAMQ,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBqG,UAAU,CAACvG,IAAI,CAACA,IAAI,CAAC;MACvB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM4G,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAE9E,IAAI;MAAE+E;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCT,WAAW,CAACU,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACjF,IAAI,GAAG+E;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,eAAe,GAAG;QACtB,GAAGd,QAAQ;QACXe,iBAAiB,EAAE1I,IAAI,CAACY,UAAU;QAClCiF,WAAW,EAAE7F,IAAI,CAACqD,IAAI;QACtBsF,YAAY,EAAE3I,IAAI,CAAC4I,KAAK;QACxBC,YAAY,EAAE7I,IAAI,CAAC8I,KAAK;QACxB1D,MAAM,EAAE;MACV,CAAC;MAED,MAAMnE,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,eAAe,EAAE;QAC3DoI,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDlC,IAAI,EAAEmC,IAAI,CAACC,SAAS,CAACT,eAAe;MACtC,CAAC,CAAC;MAEF,MAAMtH,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB8H,KAAK,CAAC,6CAA6C,CAAC;QACpD3B,kBAAkB,CAAC,KAAK,CAAC;QACzBI,WAAW,CAAC;UACVC,QAAQ,EAAE,EAAE;UACZjD,eAAe,EAAE,EAAE;UACnBkD,eAAe,EAAE,EAAE;UACnBC,MAAM,EAAE,EAAE;UACVC,OAAO,EAAE;QACX,CAAC,CAAC;QACF7D,oBAAoB,CAAC,CAAC;MACxB,CAAC,MAAM;QACLgF,KAAK,CAAC,UAAUhI,IAAI,CAACiI,OAAO,EAAE,CAAC;MACjC;IACF,CAAC,CAAC,OAAO9H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD6H,KAAK,CAAC,sCAAsC,CAAC;IAC/C;EACF,CAAC;EAED,MAAM1E,oBAAoB,GAAGpE,YAAY,CAAC6B,MAAM,CAACwC,GAAG,IAClD,IAAIC,IAAI,CAACD,GAAG,CAACE,eAAe,CAAC,GAAG,IAAID,IAAI,CAAC,CAC3C,CAAC;EAED,MAAM0E,gBAAgB,GAAGhJ,YAAY,CAAC6B,MAAM,CAACwC,GAAG,IAC9C,IAAIC,IAAI,CAACD,GAAG,CAACE,eAAe,CAAC,IAAI,IAAID,IAAI,CAAC,CAC5C,CAAC;EAED,oBACE9E,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAK2C,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxE5C,OAAA;QAAK2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5C,OAAA;UAAI2C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEvD,OAAA;UACEyD,OAAO,EAAEA,CAAA,KAAMkE,kBAAkB,CAAC,IAAI,CAAE;UACxChF,SAAS,EAAC,0OAA0O;UAAAC,QAAA,gBAEpP5C,OAAA;YAAK2C,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5F5C,OAAA;cAAMiD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,uBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNvD,OAAA;QAAK2C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClFqB,oBAAoB,CAAChB,MAAM,GAAG,CAAC,gBAC9B5D,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDgC,oBAAoB,CAAC3C,GAAG,CAAC,CAACuD,WAAW,EAAEL,KAAK,kBAC3CnF,OAAA;YAAiB2C,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAC3G5C,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5C,OAAA;gBAAK2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC5C,OAAA;kBAAK2C,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,eACjF5C,OAAA;oBAAK2C,SAAS,EAAC,wBAAwB;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC3G5C,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAqE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAI2C,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,GAAC,MAAI,EAAC4C,WAAW,CAACC,UAAU;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzEvD,OAAA;oBAAG2C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE4C,WAAW,CAACiE;kBAAS;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAM2C,SAAS,EAAE,8CACf6C,WAAW,CAACD,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAClEC,WAAW,CAACD,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAClE,yBAAyB,EACxB;gBAAA3C,QAAA,EACA4C,WAAW,CAACD;cAAM;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNvD,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5C,OAAA;gBAAG2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC5C,OAAA;kBAAM2C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC,IAAIuB,IAAI,CAACU,WAAW,CAACT,eAAe,CAAC,CAACO,kBAAkB,CAAC,CAAC;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClIvD,OAAA;gBAAG2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC5C,OAAA;kBAAM2C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACiC,WAAW,CAACyC,eAAe;cAAA;gBAAA7E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnGvD,OAAA;gBAAG2C,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAAC5C,OAAA;kBAAM2C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACiC,WAAW,CAAC0C,MAAM;cAAA;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA,GAzBE4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENvD,OAAA;UAAK2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD5C,OAAA;YAAG2C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNvD,OAAA;QAAA4C,QAAA,gBACE5C,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9EiG,gBAAgB,CAAC5F,MAAM,GAAG,CAAC,gBAC1B5D,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB4G,gBAAgB,CAACvH,GAAG,CAAC,CAACuD,WAAW,EAAEL,KAAK,kBACvCnF,OAAA;YAAiB2C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBACtF5C,OAAA;cAAK2C,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC5C,OAAA;gBAAK2C,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChF5C,OAAA;kBAAK2C,SAAS,EAAC,uBAAuB;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC1G5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAG2C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GAAC,MAAI,EAAC4C,WAAW,CAACC,UAAU;gBAAA;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3EvD,OAAA;kBAAG2C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAE,IAAIkC,IAAI,CAACU,WAAW,CAACT,eAAe,CAAC,CAACO,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAACE,WAAW,CAACyC,eAAe;gBAAA;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAM2C,SAAS,EAAE,8CACf6C,WAAW,CAACD,MAAM,KAAK,WAAW,GAAG,2BAA2B,GAChEC,WAAW,CAACD,MAAM,KAAK,WAAW,GAAG,yBAAyB,GAC9D,2BAA2B,EAC1B;cAAA3C,QAAA,EACA4C,WAAW,CAACD;YAAM;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA,GAlBC4B,KAAK;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENvD,OAAA;UAAK2C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD5C,OAAA;YAAG2C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLmE,eAAe,iBACd1H,OAAA;MAAK2C,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5C,OAAA;QAAK2C,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F5C,OAAA;UAAK2C,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5C,OAAA;YAAK2C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF5C,OAAA;cAAI2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEvD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMkE,kBAAkB,CAAC,KAAK,CAAE;cACzChF,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENvD,OAAA;YAAM0J,QAAQ,EAAEhB,YAAa;YAAC/F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjD5C,OAAA;cAAK2C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvFvD,OAAA;kBACEwD,IAAI,EAAC,UAAU;kBACf+E,KAAK,EAAET,QAAQ,CAACE,QAAS;kBACzB2B,QAAQ,EAAEtB,iBAAkB;kBAC5BuB,QAAQ;kBACRjH,SAAS,EAAC,8GAA8G;kBAAAC,QAAA,gBAExH5C,OAAA;oBAAQuI,KAAK,EAAC,EAAE;oBAAA3F,QAAA,EAAC;kBAAe;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxCqE,OAAO,CAAC3F,GAAG,CAAE4H,MAAM,iBAClB7J,OAAA;oBAAwBuI,KAAK,EAAEsB,MAAM,CAAC7H,EAAG;oBAAAY,QAAA,GAAC,MACpC,EAACiH,MAAM,CAACC,SAAS,EAAC,GAAC,EAACD,MAAM,CAACE,QAAQ,EAAC,KAAG,EAACF,MAAM,CAACJ,SAAS;kBAAA,GADjDI,MAAM,CAAC7H,EAAE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrFvD,OAAA;kBACEwD,IAAI,EAAC,SAAS;kBACd+E,KAAK,EAAET,QAAQ,CAACK,OAAQ;kBACxBwB,QAAQ,EAAEtB,iBAAkB;kBAC5B1F,SAAS,EAAC,8GAA8G;kBAAAC,QAAA,gBAExH5C,OAAA;oBAAQuI,KAAK,EAAC,QAAQ;oBAAA3F,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCvD,OAAA;oBAAQuI,KAAK,EAAC,QAAQ;oBAAA3F,QAAA,EAAC;kBAAM;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCvD,OAAA;oBAAQuI,KAAK,EAAC,WAAW;oBAAA3F,QAAA,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFvD,OAAA;kBACEqG,IAAI,EAAC,MAAM;kBACX7C,IAAI,EAAC,iBAAiB;kBACtB+E,KAAK,EAAET,QAAQ,CAAC/C,eAAgB;kBAChC4E,QAAQ,EAAEtB,iBAAkB;kBAC5B2B,GAAG,EAAE,IAAIlF,IAAI,CAAC,CAAC,CAACiC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;kBAC5C4C,QAAQ;kBACRjH,SAAS,EAAC;gBAA8G;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAO2C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFvD,OAAA;kBACEqG,IAAI,EAAC,MAAM;kBACX7C,IAAI,EAAC,iBAAiB;kBACtB+E,KAAK,EAAET,QAAQ,CAACG,eAAgB;kBAChC0B,QAAQ,EAAEtB,iBAAkB;kBAC5BuB,QAAQ;kBACRjH,SAAS,EAAC;gBAA8G;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAO2C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1FvD,OAAA;gBACEwD,IAAI,EAAC,QAAQ;gBACb+E,KAAK,EAAET,QAAQ,CAACI,MAAO;gBACvByB,QAAQ,EAAEtB,iBAAkB;gBAC5B4B,IAAI,EAAC,GAAG;gBACRL,QAAQ;gBACRM,WAAW,EAAC,gEAAgE;gBAC5EvH,SAAS,EAAC;cAA8G;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvD,OAAA;cAAK2C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5C,OAAA;gBACEqG,IAAI,EAAC,QAAQ;gBACb1D,SAAS,EAAC,oLAAoL;gBAAAC,QAAA,gBAE9L5C,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC5F5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAwF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I,CAAC,kBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvD,OAAA;gBACEqG,IAAI,EAAC,QAAQ;gBACb5C,OAAO,EAAEA,CAAA,KAAMkE,kBAAkB,CAAC,KAAK,CAAE;gBACzChF,SAAS,EAAC,gGAAgG;gBAAAC,QAAA,EAC3G;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAkE,GAAA,CApSMpD,eAAe;AAAA8F,GAAA,GAAf9F,eAAe;AAqSrB,MAAME,WAAW,GAAGA,CAAC;EAAE7D,SAAS;EAAEE,IAAI;EAAEe,SAAS;EAAEQ,cAAc;EAAEG,kBAAkB;EAAEC;AAAU,CAAC,KAAK;EAAA6H,GAAA;EACrG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3K,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4K,cAAc,EAAEC,iBAAiB,CAAC,GAAG7K,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8K,QAAQ,EAAEC,WAAW,CAAC,GAAG/K,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAMgL,iBAAiB,GAAGjK,SAAS,CAAC2B,MAAM,CAACT,QAAQ,IAAI;IAAA,IAAAgJ,qBAAA;IACrD,MAAMC,aAAa,GAAGjJ,QAAQ,CAAC4B,IAAI,CAACsH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,WAAW,CAACS,WAAW,CAAC,CAAC,CAAC,MAAAF,qBAAA,GAChEhJ,QAAQ,CAACuC,WAAW,cAAAyG,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACV,WAAW,CAACS,WAAW,CAAC,CAAC,CAAC;IAC5F,MAAME,eAAe,GAAGT,cAAc,KAAK,KAAK,IAAI3I,QAAQ,CAACqJ,QAAQ,KAAKV,cAAc;IACxF,OAAOM,aAAa,IAAIG,eAAe;EACzC,CAAC,CAAC;;EAEF;EACA,MAAME,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,IAAIC,GAAG,CAACzK,SAAS,CAACuB,GAAG,CAACmJ,GAAG,IAAIA,GAAG,CAACH,QAAQ,CAAC,CAAC5I,MAAM,CAACgJ,OAAO,CAAC,CAAC,CAAC;EAE1F,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI1K,IAAI,CAACgD,MAAM,KAAK,CAAC,EAAE;MACrB0F,KAAK,CAAC,oBAAoB,CAAC;MAC3B;IACF;IAEA,IAAI;MACF;MACAA,KAAK,CAAC,qCAAqC/G,SAAS,CAACgJ,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;MACtE;MACA3K,IAAI,CAAC4K,OAAO,CAACzJ,IAAI,IAAII,cAAc,CAACJ,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7C0I,WAAW,CAAC,KAAK,CAAC;IACpB,CAAC,CAAC,OAAOjJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C6H,KAAK,CAAC,2CAA2C,CAAC;IACpD;EACF,CAAC;EAED,oBACEtJ,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5C,OAAA;MAAK2C,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBACxE5C,OAAA;QAAK2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5C,OAAA;UAAI2C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEvD,OAAA;UACEyD,OAAO,EAAEA,CAAA,KAAMiH,WAAW,CAAC,IAAI,CAAE;UACjC/H,SAAS,EAAC,0OAA0O;UAAAC,QAAA,gBAEpP5C,OAAA;YAAK2C,SAAS,EAAC,SAAS;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5F5C,OAAA;cAAMiD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAwI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7L,CAAC,UACA,EAAC3C,IAAI,CAACgD,MAAM,EAAC,GACrB;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNvD,OAAA;QAAK2C,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD5C,OAAA;UAAK2C,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrB5C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB5C,OAAA;cACEqG,IAAI,EAAC,MAAM;cACX6D,WAAW,EAAC,qBAAqB;cACjC3B,KAAK,EAAE8B,WAAY;cACnBV,QAAQ,EAAGrB,CAAC,IAAKgC,cAAc,CAAChC,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;cAChD5F,SAAS,EAAC;YAAoH;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC,eACFvD,OAAA;cAAK2C,SAAS,EAAC,+CAA+C;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAClI5C,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAoD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvD,OAAA;UAAA4C,QAAA,eACE5C,OAAA;YACEuI,KAAK,EAAEgC,cAAe;YACtBZ,QAAQ,EAAGrB,CAAC,IAAKkC,iBAAiB,CAAClC,CAAC,CAACE,MAAM,CAACD,KAAK,CAAE;YACnD5F,SAAS,EAAC,uGAAuG;YAAAC,QAAA,EAEhHsI,UAAU,CAACjJ,GAAG,CAACgJ,QAAQ,iBACtBjL,OAAA;cAAuBuI,KAAK,EAAE0C,QAAS;cAAArI,QAAA,EAAEqI;YAAQ,GAApCA,QAAQ;cAAA7H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLoH,iBAAiB,CAAC/G,MAAM,GAAG,CAAC,gBAC3B5D,OAAA;QAAK2C,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjF+H,iBAAiB,CAAC1I,GAAG,CAAEL,QAAQ,iBAC9B5B,OAAA;UAAuB2C,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAC9J5C,OAAA;YAAK2C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD5C,OAAA;cAAK2C,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC5C,OAAA;gBAAK2C,SAAS,EAAC,oEAAoE;gBAAAC,QAAA,eACjF5C,OAAA;kBAAK2C,SAAS,EAAC,wBAAwB;kBAACE,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAC3G5C,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5T;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAI2C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAEhB,QAAQ,CAAC4B;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5DvD,OAAA;kBAAG2C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEhB,QAAQ,CAACqJ;gBAAQ;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAM2C,SAAS,EAAE,8CACff,QAAQ,CAAC6J,KAAK,GAAG,EAAE,GAAG,6BAA6B,GACnD7J,QAAQ,CAAC6J,KAAK,GAAG,CAAC,GAAG,+BAA+B,GACpD,yBAAyB,EACxB;cAAA7I,QAAA,EACAhB,QAAQ,CAAC6J,KAAK,GAAG,CAAC,GAAG,GAAG7J,QAAQ,CAAC6J,KAAK,WAAW,GAAG;YAAc;cAAArI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENvD,OAAA;YAAK2C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B5C,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEhB,QAAQ,CAACuC;YAAW;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DvD,OAAA;cAAG2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAEhB,QAAQ,CAACc,KAAK,EAAC,MAAI;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACvE3B,QAAQ,CAAC8J,MAAM,iBAAI1L,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,UAAQ,EAAChB,QAAQ,CAAC8J,MAAM;YAAA;cAAAtI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAENvD,OAAA;YACEyD,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAACC,QAAQ,CAAE;YACnC+J,QAAQ,EAAE/J,QAAQ,CAAC6J,KAAK,KAAK,CAAE;YAC/B9I,SAAS,EAAE,6DACTf,QAAQ,CAAC6J,KAAK,GAAG,CAAC,GACd,4CAA4C,GAC5C,8CAA8C,EACjD;YAAA7I,QAAA,EAEFhB,QAAQ,CAAC6J,KAAK,GAAG,CAAC,GAAG,aAAa,GAAG;UAAc;YAAArI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA,GAtCD3B,QAAQ,CAACI,EAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuChB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENvD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UAAK2C,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/F5C,OAAA;YAAK2C,SAAS,EAAC,yBAAyB;YAACE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5G5C,OAAA;cAAMiD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAuQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5T;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvD,OAAA;UAAI2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFvD,OAAA;UAAG2C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLkH,QAAQ,iBACPzK,OAAA;MAAK2C,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5C,OAAA;QAAK2C,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5F5C,OAAA;UAAK2C,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5C,OAAA;YAAK2C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF5C,OAAA;cAAI2C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEvD,OAAA;cACEyD,OAAO,EAAEA,CAAA,KAAMiH,WAAW,CAAC,KAAK,CAAE;cAClC/H,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL3C,IAAI,CAACgD,MAAM,GAAG,CAAC,gBACd5D,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvBhC,IAAI,CAACqB,GAAG,CAAEF,IAAI,iBACb/B,OAAA;cAAmB2C,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBACxF5C,OAAA;gBAAK2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC5C,OAAA;kBAAK2C,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,eACjF5C,OAAA;oBAAK2C,SAAS,EAAC,wBAAwB;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC3G5C,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAuQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5T;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNvD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAI2C,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEb,IAAI,CAACyB;kBAAI;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5DvD,OAAA;oBAAG2C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAEb,IAAI,CAACW,KAAK,EAAC,WAAS;kBAAA;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAK2C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC5C,OAAA;kBAAK2C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC5C,OAAA;oBACEyD,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,IAAI,CAACC,EAAE,EAAED,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAE;oBAC9DS,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,EAC9F;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTvD,OAAA;oBAAM2C,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEb,IAAI,CAACG;kBAAQ;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpEvD,OAAA;oBACEyD,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,IAAI,CAACC,EAAE,EAAED,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAE;oBAC9DS,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,EAC9F;kBAED;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNvD,OAAA;kBAAG2C,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAE,CAACb,IAAI,CAACW,KAAK,GAAGX,IAAI,CAACG,QAAQ,EAAEqJ,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAnI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACzGvD,OAAA;kBACEyD,OAAO,EAAEA,CAAA,KAAMtB,cAAc,CAACJ,IAAI,CAACC,EAAE,CAAE;kBACvCW,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAE/C5C,OAAA;oBAAK2C,SAAS,EAAC,SAAS;oBAACE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAC5F5C,OAAA;sBAAMiD,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA8H;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GArCExB,IAAI,CAACC,EAAE;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCZ,CACN,CAAC,eAEFvD,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5C,OAAA;gBAAK2C,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1D5C,OAAA;kBAAA4C,QAAA,EAAM;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBvD,OAAA;kBAAM2C,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAEL,SAAS,CAACgJ,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAnI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNvD,OAAA;gBAAK2C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB5C,OAAA;kBACEyD,OAAO,EAAE6H,cAAe;kBACxB3I,SAAS,EAAC,mKAAmK;kBAAAC,QAAA,EAC9K;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvD,OAAA;kBACEyD,OAAO,EAAEA,CAAA,KAAMiH,WAAW,CAAC,KAAK,CAAE;kBAClC/H,SAAS,EAAC,gGAAgG;kBAAAC,QAAA,EAC3G;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENvD,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC5C,OAAA;cAAK2C,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F5C,OAAA;gBAAK2C,SAAS,EAAC,yBAAyB;gBAACE,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAJ,QAAA,eAC5G5C,OAAA;kBAAMiD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAwI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAI2C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChFvD,OAAA;cAAG2C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAkC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC6G,GAAA,CA3OI7F,WAAW;AAAAqH,GAAA,GAAXrH,WAAW;AA6OjB,eAAetE,aAAa;AAAC,IAAAyE,EAAA,EAAAC,GAAA,EAAAe,GAAA,EAAA8B,GAAA,EAAA2C,GAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
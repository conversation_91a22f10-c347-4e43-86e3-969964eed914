import { useState } from 'react';
import { useFlutterwave, closePaymentModal } from 'flutterwave-react-v3';

const FlutterwavePayment = ({ 
  amount, 
  customerEmail, 
  customerPhone, 
  customerName, 
  billId, 
  onSuccess, 
  onClose 
}) => {
  const [loading, setLoading] = useState(false);

  // Flutterwave configuration
  const config = {
    public_key: process.env.REACT_APP_FLUTTERWAVE_PUBLIC_KEY || 'FLWPUBK_TEST-SANDBOXDEMOKEY-X', // Replace with your public key
    tx_ref: `HEALTHCARE_${billId}_${Date.now()}`,
    amount: amount,
    currency: 'RWF',
    payment_options: 'card,mobilemoney,ussd,bank_transfer',
    customer: {
      email: customerEmail,
      phone_number: customerPhone,
      name: customerName,
    },
    customizations: {
      title: 'Healthcare Payment',
      description: `Payment for medical bill #${billId}`,
      logo: '/logo.png', // Add your logo here
    },
  };

  const handleFlutterPayment = useFlutterwave(config);

  const initiatePayment = () => {
    setLoading(true);
    
    handleFlutterPayment({
      callback: (response) => {
        console.log('Flutterwave payment response:', response);
        
        if (response.status === 'successful') {
          // Payment successful
          onSuccess({
            transactionId: response.transaction_id,
            flwRef: response.flw_ref,
            amount: response.amount,
            currency: response.currency,
            status: response.status,
            paymentType: response.payment_type,
            chargedAmount: response.charged_amount
          });
        } else {
          // Payment failed or cancelled
          alert('Payment was not successful. Please try again.');
        }
        
        closePaymentModal();
        setLoading(false);
      },
      onClose: () => {
        console.log('Payment modal closed');
        setLoading(false);
        onClose();
      },
    });
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">Secure Payment with Flutterwave</h3>
        <p className="text-gray-600">Complete your medical bill payment securely</p>
      </div>

      {/* Payment Details */}
      <div className="bg-gray-50 rounded-xl p-4 mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Payment Details</h4>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Patient:</span>
            <span className="font-medium">{customerName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Bill ID:</span>
            <span className="font-medium">#{billId}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Email:</span>
            <span className="font-medium">{customerEmail}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Phone:</span>
            <span className="font-medium">{customerPhone}</span>
          </div>
          <div className="border-t pt-2 mt-3">
            <div className="flex justify-between text-lg font-bold">
              <span>Amount to Pay:</span>
              <span className="text-green-600">{amount.toLocaleString()} RWF</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Available Payment Methods</h4>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
            <div className="text-blue-600 font-medium text-sm">💳 Card Payment</div>
            <div className="text-xs text-gray-600">Visa, Mastercard</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
            <div className="text-green-600 font-medium text-sm">📱 Mobile Money</div>
            <div className="text-xs text-gray-600">MTN, Airtel</div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
            <div className="text-purple-600 font-medium text-sm">🏦 Bank Transfer</div>
            <div className="text-xs text-gray-600">Direct transfer</div>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
            <div className="text-orange-600 font-medium text-sm">📞 USSD</div>
            <div className="text-xs text-gray-600">Dial code</div>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-500 mr-2 mt-0.5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <div>
            <h5 className="font-medium text-blue-900 text-sm">Secure Payment</h5>
            <p className="text-xs text-blue-700 mt-1">
              Your payment is secured by Flutterwave's industry-standard encryption and security measures.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button
          onClick={onClose}
          className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-xl transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={initiatePayment}
          disabled={loading}
          className="flex-1 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
              Processing...
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              Pay Now
            </div>
          )}
        </button>
      </div>
    </div>
  );
};

export default FlutterwavePayment;

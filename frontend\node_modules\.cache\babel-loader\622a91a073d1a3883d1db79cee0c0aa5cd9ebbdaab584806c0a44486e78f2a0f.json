{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\context\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Remove session timeout - sessions should only end when browser closes\n  // const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // Disabled\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        const loginTime = sessionStorage.getItem('loginTime');\n        if (token && userData && loginTime) {\n          // No timeout check - session persists until browser closes\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          console.log('Session restored from sessionStorage:', parsedUser.name);\n        } else {\n          console.log('No valid session found');\n        }\n      } catch (error) {\n        console.error('Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n        sessionStorage.removeItem('loginTime');\n        setUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n\n    // Session management - sessionStorage automatically clears when browser/tab closes\n    // sessionStorage will persist across page refreshes but clear when tab/browser closes\n\n    // Add debugging to track session behavior\n    const handleBeforeUnload = event => {\n      // This fires on both refresh and browser close\n      // We don't want to clear session on refresh, only on browser close\n      // sessionStorage will handle this automatically, but let's log for debugging\n      console.log('Page unloading - session will persist if it\\'s a refresh, clear if browser closes');\n    };\n\n    // Add event listener for debugging\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  // Login function\n  const login = userData => {\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    const loginTime = new Date().getTime();\n    sessionStorage.setItem('authToken', userData.token || 'authenticated');\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    sessionStorage.setItem('loginTime', loginTime.toString());\n    console.log('User logged in and session stored:', userData.name);\n    console.log('Session will persist until browser/tab is closed');\n  };\n\n  // Logout function\n  const logout = redirectCallback => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    sessionStorage.removeItem('loginTime');\n    setUser(null);\n    console.log('User logged out and session cleared');\n\n    // Call redirect callback if provided\n    if (redirectCallback && typeof redirectCallback === 'function') {\n      redirectCallback();\n    }\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = role => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "checkAuth", "token", "sessionStorage", "getItem", "userData", "loginTime", "parsedUser", "JSON", "parse", "console", "log", "name", "error", "removeItem", "handleBeforeUnload", "event", "window", "addEventListener", "removeEventListener", "login", "Date", "getTime", "setItem", "stringify", "toString", "logout", "redirectCallback", "isAuthenticated", "hasRole", "role", "isAdmin", "userType", "isStaff", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/context/AuthContext.jsx"], "sourcesContent": ["import { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Remove session timeout - sessions should only end when browser closes\n  // const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // Disabled\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        const loginTime = sessionStorage.getItem('loginTime');\n\n        if (token && userData && loginTime) {\n          // No timeout check - session persists until browser closes\n          const parsedUser = JSON.parse(userData);\n          setUser(parsedUser);\n          console.log('Session restored from sessionStorage:', parsedUser.name);\n        } else {\n          console.log('No valid session found');\n        }\n      } catch (error) {\n        console.error('Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n        sessionStorage.removeItem('loginTime');\n        setUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n\n    // Session management - sessionStorage automatically clears when browser/tab closes\n    // sessionStorage will persist across page refreshes but clear when tab/browser closes\n\n    // Add debugging to track session behavior\n    const handleBeforeUnload = (event) => {\n      // This fires on both refresh and browser close\n      // We don't want to clear session on refresh, only on browser close\n      // sessionStorage will handle this automatically, but let's log for debugging\n      console.log('Page unloading - session will persist if it\\'s a refresh, clear if browser closes');\n    };\n\n    // Add event listener for debugging\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  // Login function\n  const login = (userData) => {\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    const loginTime = new Date().getTime();\n    sessionStorage.setItem('authToken', userData.token || 'authenticated');\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    sessionStorage.setItem('loginTime', loginTime.toString());\n    console.log('User logged in and session stored:', userData.name);\n    console.log('Session will persist until browser/tab is closed');\n  };\n\n  // Logout function\n  const logout = (redirectCallback) => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    sessionStorage.removeItem('loginTime');\n    setUser(null);\n    console.log('User logged out and session cleared');\n\n    // Call redirect callback if provided\n    if (redirectCallback && typeof redirectCallback === 'function') {\n      redirectCallback();\n    }\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = (role) => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA;;EAEA;EACAC,SAAS,CAAC,MAAM;IACd,MAAMe,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF;QACA;QACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QACjD,MAAMC,QAAQ,GAAGF,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC;QACnD,MAAME,SAAS,GAAGH,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QAErD,IAAIF,KAAK,IAAIG,QAAQ,IAAIC,SAAS,EAAE;UAClC;UACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UACvCP,OAAO,CAACS,UAAU,CAAC;UACnBG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEJ,UAAU,CAACK,IAAI,CAAC;QACvE,CAAC,MAAM;UACLF,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACvC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACAV,cAAc,CAACW,UAAU,CAAC,WAAW,CAAC;QACtCX,cAAc,CAACW,UAAU,CAAC,UAAU,CAAC;QACrCX,cAAc,CAACW,UAAU,CAAC,WAAW,CAAC;QACtChB,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,SAAS,CAAC,CAAC;;IAEX;IACA;;IAEA;IACA,MAAMc,kBAAkB,GAAIC,KAAK,IAAK;MACpC;MACA;MACA;MACAN,OAAO,CAACC,GAAG,CAAC,mFAAmF,CAAC;IAClG,CAAC;;IAED;IACAM,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;;IAE3D;IACA,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEJ,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,KAAK,GAAIf,QAAQ,IAAK;IAC1BP,OAAO,CAACO,QAAQ,CAAC;IACjB;IACA,MAAMC,SAAS,GAAG,IAAIe,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtCnB,cAAc,CAACoB,OAAO,CAAC,WAAW,EAAElB,QAAQ,CAACH,KAAK,IAAI,eAAe,CAAC;IACtEC,cAAc,CAACoB,OAAO,CAAC,UAAU,EAAEf,IAAI,CAACgB,SAAS,CAACnB,QAAQ,CAAC,CAAC;IAC5DF,cAAc,CAACoB,OAAO,CAAC,WAAW,EAAEjB,SAAS,CAACmB,QAAQ,CAAC,CAAC,CAAC;IACzDf,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEN,QAAQ,CAACO,IAAI,CAAC;IAChEF,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EACjE,CAAC;;EAED;EACA,MAAMe,MAAM,GAAIC,gBAAgB,IAAK;IACnC;IACAxB,cAAc,CAACW,UAAU,CAAC,WAAW,CAAC;IACtCX,cAAc,CAACW,UAAU,CAAC,UAAU,CAAC;IACrCX,cAAc,CAACW,UAAU,CAAC,WAAW,CAAC;IACtChB,OAAO,CAAC,IAAI,CAAC;IACbY,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAElD;IACA,IAAIgB,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;MAC9DA,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAAC/B,IAAI;EACf,CAAC;;EAED;EACA,MAAMgC,OAAO,GAAIC,IAAI,IAAK;IACxB,OAAOjC,IAAI,IAAIA,IAAI,CAACiC,IAAI,KAAKA,IAAI;EACnC,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOlC,IAAI,KAAKA,IAAI,CAACiC,IAAI,KAAK,OAAO,IAAIjC,IAAI,CAACmC,QAAQ,KAAK,OAAO,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOpC,IAAI,KAAKA,IAAI,CAACiC,IAAI,KAAK,QAAQ,IAAIjC,IAAI,CAACiC,IAAI,KAAK,OAAO,IAAIjC,IAAI,CAACmC,QAAQ,KAAK,OAAO,CAAC;EAC/F,CAAC;EAED,MAAME,KAAK,GAAG;IACZrC,IAAI;IACJuB,KAAK;IACLM,MAAM;IACNE,eAAe;IACfC,OAAO;IACPE,OAAO;IACPE,OAAO;IACPlC;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAAC8C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EAChCA;EAAQ;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC3C,GAAA,CA1HWF,YAAY;AAAA8C,EAAA,GAAZ9C,YAAY;AA4HzB,eAAeL,WAAW;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\PatientPortal.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport Login from '../components/Login';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PatientPortal = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [medicalRecord, setMedicalRecord] = useState(null);\n  const [showMedicalRecordsModal, setShowMedicalRecordsModal] = useState(false);\n  const [medicalRecordsLoading, setMedicalRecordsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount (only if logged in)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n    }\n    // Always fetch medicines for pharmacy browsing\n    fetchMedicines();\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = medicine => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => item.id === medicine.id ? {\n        ...item,\n        quantity: item.quantity + 1\n      } : item));\n    } else {\n      setCart([...cart, {\n        ...medicine,\n        quantity: 1\n      }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = medicineId => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => item.id === medicineId ? {\n        ...item,\n        quantity: quantity\n      } : item));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + item.price * item.quantity, 0);\n\n  // Fetch medical records for the logged-in patient\n  const fetchMedicalRecords = async () => {\n    if (!(user !== null && user !== void 0 && user.nationalId)) {\n      console.error('No user national ID available');\n      return;\n    }\n    setMedicalRecordsLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/medical-records/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicalRecord(data.data);\n        console.log('Medical records fetched successfully');\n      } else {\n        console.error('Failed to fetch medical records:', data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching medical records:', error);\n    } finally {\n      setMedicalRecordsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const openExamReports = () => {\n    // Create a modal or navigate to exam reports section\n    alert('Opening Exam Reports - Feature coming soon!');\n  };\n  const openAppointments = () => {\n    // Create a modal or navigate to appointments section\n    alert('Opening Appointments - Feature coming soon!');\n  };\n  const openPharmacy = () => {\n    // Create a modal or navigate to pharmacy section\n    alert('Opening Pharmacy - Feature coming soon!');\n  };\n  const openHealthRecords = () => {\n    // Create a modal or navigate to health records section\n    alert('Opening Health Records - Feature coming soon!');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-gray-50 to-blue-50 font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center\",\n      style: {\n        backgroundImage: \"url('/image/Screenshot 2025-04-21 200615.png')\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black/40\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 text-center max-w-5xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4\",\n            children: \"\\uD83C\\uDFE5 Your Personal Health Portal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-7xl font-extrabold mb-6 leading-tight\",\n          children: [\"Your Health,\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 block\",\n            children: \"Your Way\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\",\n          children: user ? `Welcome back, ${user.name}! Access your health records, manage appointments, and order medicines.` : 'Access your health records, book appointments, and manage your healthcare journey all in one place.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('services').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n              children: \"View My Health Records\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('pharmacy').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\",\n              children: \"Order Medicines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLoginModal(true),\n              className: \"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n              children: \"Sign In to Your Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => document.getElementById('services').scrollIntoView({\n                behavior: 'smooth'\n              }),\n              className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\",\n              children: \"Explore Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      id: \"services\",\n      className: \"py-20 px-6 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4\",\n            children: \"Your Health Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n            children: \"Patient Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Access your personal health information and manage your healthcare journey\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => user ? openExamReports() : setShowLoginModal(true),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"My Exam Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"View and download your medical test results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this), !user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-600 mt-2 font-medium\",\n                children: \"Login required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 27\n              }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: [examReports.length, \" reports available\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => user ? openAppointments() : setShowLoginModal(true),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"My Appointments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Book and manage your doctor appointments\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), !user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-600 mt-2 font-medium\",\n                children: \"Login required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 27\n              }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: [appointments.length, \" appointments\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => openPharmacy(),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Online Pharmacy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Order medicines and health products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: [medicines.length, \" medicines available\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), cart.length > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-orange-600 mt-1 font-medium\",\n                children: [cart.length, \" items in cart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            onClick: () => user ? openHealthRecords() : setShowLoginModal(true),\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Health Records\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Access your complete medical history\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), !user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-600 mt-2 font-medium\",\n                children: \"Login required\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 27\n              }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-green-600 mt-2 font-medium\",\n                children: \"Complete records available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Emergency Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"24/7 emergency medical assistance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-red-600 mt-2 font-medium\",\n                children: \"Call: 112\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Health Tips\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Daily health tips and wellness advice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-orange-600 mt-2 font-medium\",\n                children: \"Updated daily\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-1 md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"Patient Portal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300 mb-6 max-w-md leading-relaxed\",\n              children: \"Your personal healthcare portal providing secure access to medical records, appointments, and health services.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Quick Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => user ? openExamReports() : setShowLoginModal(true),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"My Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => user ? openAppointments() : setShowLoginModal(true),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => openPharmacy(),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Pharmacy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => user ? openHealthRecords() : setShowLoginModal(true),\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Health Records\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Help Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Contact Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Emergency: 112\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-700 pt-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"\\xA9 2025 HealthCarePro Patient Portal. All rights reserved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), showLoginModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Patient Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLoginModal(false),\n              className: \"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Login, {\n            onLogin: userData => {\n              setShowLoginModal(false);\n              // Redirect to home page after successful login\n              navigate('/home');\n            },\n            onClose: () => setShowLoginModal(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientPortal, \"7aWYBaQETtzBDIdS0H3CpJiai/c=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = PatientPortal;\nexport default PatientPortal;\nvar _c;\n$RefreshReg$(_c, \"PatientPortal\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "useAuth", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PatientPortal", "_s", "user", "navigate", "showLoginModal", "setShowLoginModal", "examReports", "setExamReports", "appointments", "setAppointments", "medicines", "setMedicines", "cart", "setCart", "medicalRecord", "setMedicalRecord", "showMedicalRecordsModal", "setShowMedicalRecordsModal", "medicalRecordsLoading", "setMedicalRecordsLoading", "activeTab", "setActiveTab", "API_BASE_URL", "nationalId", "fetchExamReports", "fetchAppointments", "fetchMedicines", "response", "fetch", "data", "json", "success", "error", "console", "addToCart", "medicine", "existingItem", "find", "item", "id", "map", "quantity", "removeFromCart", "medicineId", "filter", "updateCartQuantity", "cartTotal", "reduce", "total", "price", "fetchMedicalRecords", "log", "message", "openExamReports", "alert", "openAppointments", "openPharmacy", "openHealthRecords", "className", "children", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "document", "getElementById", "scrollIntoView", "behavior", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "length", "href", "onLogin", "userData", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/pages/PatientPortal.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport Login from '../components/Login';\n\nconst PatientPortal = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [examReports, setExamReports] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [medicines, setMedicines] = useState([]);\n  const [cart, setCart] = useState([]);\n  const [medicalRecord, setMedicalRecord] = useState(null);\n  const [showMedicalRecordsModal, setShowMedicalRecordsModal] = useState(false);\n  const [medicalRecordsLoading, setMedicalRecordsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch patient data on component mount (only if logged in)\n  useEffect(() => {\n    if (user?.nationalId) {\n      fetchExamReports();\n      fetchAppointments();\n    }\n    // Always fetch medicines for pharmacy browsing\n    fetchMedicines();\n  }, [user]);\n\n  // Fetch exam reports for the patient\n  const fetchExamReports = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setExamReports(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching exam reports:', error);\n    }\n  };\n\n  // Fetch patient appointments\n  const fetchAppointments = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);\n      const data = await response.json();\n      if (data.success) {\n        setAppointments(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching appointments:', error);\n    }\n  };\n\n  // Fetch available medicines\n  const fetchMedicines = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/pharmacy`);\n      const data = await response.json();\n      if (data.success) {\n        setMedicines(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching medicines:', error);\n    }\n  };\n\n  // Add medicine to cart\n  const addToCart = (medicine) => {\n    const existingItem = cart.find(item => item.id === medicine.id);\n    if (existingItem) {\n      setCart(cart.map(item => \n        item.id === medicine.id \n          ? { ...item, quantity: item.quantity + 1 }\n          : item\n      ));\n    } else {\n      setCart([...cart, { ...medicine, quantity: 1 }]);\n    }\n  };\n\n  // Remove from cart\n  const removeFromCart = (medicineId) => {\n    setCart(cart.filter(item => item.id !== medicineId));\n  };\n\n  // Update cart quantity\n  const updateCartQuantity = (medicineId, quantity) => {\n    if (quantity <= 0) {\n      removeFromCart(medicineId);\n    } else {\n      setCart(cart.map(item => \n        item.id === medicineId \n          ? { ...item, quantity: quantity }\n          : item\n      ));\n    }\n  };\n\n  // Calculate cart total\n  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);\n\n  // Fetch medical records for the logged-in patient\n  const fetchMedicalRecords = async () => {\n    if (!user?.nationalId) {\n      console.error('No user national ID available');\n      return;\n    }\n\n    setMedicalRecordsLoading(true);\n    try {\n      const response = await fetch(`${API_BASE_URL}/medical-records/${user.nationalId}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setMedicalRecord(data.data);\n        console.log('Medical records fetched successfully');\n      } else {\n        console.error('Failed to fetch medical records:', data.message);\n      }\n    } catch (error) {\n      console.error('Error fetching medical records:', error);\n    } finally {\n      setMedicalRecordsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const openExamReports = () => {\n    // Create a modal or navigate to exam reports section\n    alert('Opening Exam Reports - Feature coming soon!');\n  };\n\n  const openAppointments = () => {\n    // Create a modal or navigate to appointments section\n    alert('Opening Appointments - Feature coming soon!');\n  };\n\n  const openPharmacy = () => {\n    // Create a modal or navigate to pharmacy section\n    alert('Opening Pharmacy - Feature coming soon!');\n  };\n\n  const openHealthRecords = () => {\n    // Create a modal or navigate to health records section\n    alert('Opening Health Records - Feature coming soon!');\n  };\n\n  return (\n    <div className=\"bg-gradient-to-br from-gray-50 to-blue-50 font-sans\">\n      {/* Hero Section */}\n      <section\n        className=\"relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center\"\n        style={{\n          backgroundImage: \"url('/image/Screenshot 2025-04-21 200615.png')\"\n        }}\n      >\n        <div className=\"absolute inset-0 bg-black/40\"></div>\n        <div className=\"relative z-10 text-center max-w-5xl mx-auto\">\n          <div className=\"mb-6\">\n            <span className=\"inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4\">\n              🏥 Your Personal Health Portal\n            </span>\n          </div>\n          <h1 className=\"text-4xl md:text-7xl font-extrabold mb-6 leading-tight\">\n            Your Health,\n            <span className=\"text-yellow-400 block\">Your Way</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\">\n            {user \n              ? `Welcome back, ${user.name}! Access your health records, manage appointments, and order medicines.`\n              : 'Access your health records, book appointments, and manage your healthcare journey all in one place.'\n            }\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            {user ? (\n              <>\n                <button \n                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n                >\n                  View My Health Records\n                </button>\n                <button \n                  onClick={() => document.getElementById('pharmacy').scrollIntoView({ behavior: 'smooth' })}\n                  className=\"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\"\n                >\n                  Order Medicines\n                </button>\n              </>\n            ) : (\n              <>\n                <button \n                  onClick={() => setShowLoginModal(true)}\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n                >\n                  Sign In to Your Portal\n                </button>\n                <button \n                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}\n                  className=\"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\"\n                >\n                  Explore Services\n                </button>\n              </>\n            )}\n          </div>\n        </div>\n      </section>\n\n      {/* Patient Services Section */}\n      <section id=\"services\" className=\"py-20 px-6 bg-white\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4\">\n              Your Health Services\n            </span>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">Patient Services</h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Access your personal health information and manage your healthcare journey\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* Exam Reports */}\n            <div \n              onClick={() => user ? openExamReports() : setShowLoginModal(true)}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">My Exam Reports</h3>\n                <p className=\"text-sm text-gray-600\">View and download your medical test results</p>\n                {!user && <p className=\"text-xs text-blue-600 mt-2 font-medium\">Login required</p>}\n                {user && <p className=\"text-xs text-green-600 mt-2 font-medium\">{examReports.length} reports available</p>}\n              </div>\n            </div>\n\n            {/* Appointments */}\n            <div \n              onClick={() => user ? openAppointments() : setShowLoginModal(true)}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">My Appointments</h3>\n                <p className=\"text-sm text-gray-600\">Book and manage your doctor appointments</p>\n                {!user && <p className=\"text-xs text-blue-600 mt-2 font-medium\">Login required</p>}\n                {user && <p className=\"text-xs text-green-600 mt-2 font-medium\">{appointments.length} appointments</p>}\n              </div>\n            </div>\n\n            {/* Online Pharmacy */}\n            <div\n              onClick={() => openPharmacy()}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Online Pharmacy</h3>\n                <p className=\"text-sm text-gray-600\">Order medicines and health products</p>\n                <p className=\"text-xs text-green-600 mt-2 font-medium\">{medicines.length} medicines available</p>\n                {cart.length > 0 && (\n                  <p className=\"text-xs text-orange-600 mt-1 font-medium\">{cart.length} items in cart</p>\n                )}\n              </div>\n            </div>\n\n            {/* Health Records */}\n            <div\n              onClick={() => user ? openHealthRecords() : setShowLoginModal(true)}\n              className=\"group cursor-pointer\"\n            >\n              <div className=\"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Health Records</h3>\n                <p className=\"text-sm text-gray-600\">Access your complete medical history</p>\n                {!user && <p className=\"text-xs text-blue-600 mt-2 font-medium\">Login required</p>}\n                {user && <p className=\"text-xs text-green-600 mt-2 font-medium\">Complete records available</p>}\n              </div>\n            </div>\n\n            {/* Emergency Contact */}\n            <div className=\"group cursor-pointer\">\n              <div className=\"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Emergency Contact</h3>\n                <p className=\"text-sm text-gray-600\">24/7 emergency medical assistance</p>\n                <p className=\"text-xs text-red-600 mt-2 font-medium\">Call: 112</p>\n              </div>\n            </div>\n\n            {/* Health Tips */}\n            <div className=\"group cursor-pointer\">\n              <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"font-bold text-gray-900 mb-2\">Health Tips</h3>\n                <p className=\"text-sm text-gray-600\">Daily health tips and wellness advice</p>\n                <p className=\"text-xs text-orange-600 mt-2 font-medium\">Updated daily</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-2xl font-bold\">Patient Portal</h3>\n              </div>\n              <p className=\"text-gray-300 mb-6 max-w-md leading-relaxed\">\n                Your personal healthcare portal providing secure access to medical records, appointments, and health services.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Quick Access</h4>\n              <ul className=\"space-y-2\">\n                <li><button onClick={() => user ? openExamReports() : setShowLoginModal(true)} className=\"text-gray-300 hover:text-white transition-colors\">My Reports</button></li>\n                <li><button onClick={() => user ? openAppointments() : setShowLoginModal(true)} className=\"text-gray-300 hover:text-white transition-colors\">Appointments</button></li>\n                <li><button onClick={() => openPharmacy()} className=\"text-gray-300 hover:text-white transition-colors\">Pharmacy</button></li>\n                <li><button onClick={() => user ? openHealthRecords() : setShowLoginModal(true)} className=\"text-gray-300 hover:text-white transition-colors\">Health Records</button></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Support</h4>\n              <ul className=\"space-y-2\">\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Help Center</a></li>\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Contact Us</a></li>\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Privacy Policy</a></li>\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Emergency: 112</a></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-700 pt-8 text-center\">\n            <p className=\"text-gray-400\">\n              &copy; 2025 HealthCarePro Patient Portal. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n\n      {/* Login Modal */}\n      {showLoginModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6 border-b border-gray-200 pb-4\">\n                <h3 className=\"text-2xl font-bold text-gray-900\">Patient Login</h3>\n                <button\n                  onClick={() => setShowLoginModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <Login\n                onLogin={(userData) => {\n                  setShowLoginModal(false);\n                  // Redirect to home page after successful login\n                  navigate('/home');\n                }}\n                onClose={() => setShowLoginModal(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PatientPortal;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqB,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAAC2B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAM+B,YAAY,GAAG,2BAA2B;;EAEhD;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIU,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,UAAU,EAAE;MACpBC,gBAAgB,CAAC,CAAC;MAClBC,iBAAiB,CAAC,CAAC;IACrB;IACA;IACAC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACxB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMsB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,kBAAkBpB,IAAI,CAACqB,UAAU,EAAE,CAAC;MAChF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBxB,cAAc,CAACsB,IAAI,CAACA,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMP,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,yBAAyBpB,IAAI,CAACqB,UAAU,EAAE,CAAC;MACvF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBtB,eAAe,CAACoB,IAAI,CAACA,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMN,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,WAAW,CAAC;MACxD,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBpB,YAAY,CAACkB,IAAI,CAACA,IAAI,CAAC;MACzB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAME,SAAS,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,YAAY,GAAGxB,IAAI,CAACyB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,CAAC;IAC/D,IAAIH,YAAY,EAAE;MAChBvB,OAAO,CAACD,IAAI,CAAC4B,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKJ,QAAQ,CAACI,EAAE,GACnB;QAAE,GAAGD,IAAI;QAAEG,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAG;MAAE,CAAC,GACxCH,IACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzB,OAAO,CAAC,CAAC,GAAGD,IAAI,EAAE;QAAE,GAAGuB,QAAQ;QAAEM,QAAQ,EAAE;MAAE,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrC9B,OAAO,CAACD,IAAI,CAACgC,MAAM,CAACN,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKI,UAAU,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACF,UAAU,EAAEF,QAAQ,KAAK;IACnD,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBC,cAAc,CAACC,UAAU,CAAC;IAC5B,CAAC,MAAM;MACL9B,OAAO,CAACD,IAAI,CAAC4B,GAAG,CAACF,IAAI,IACnBA,IAAI,CAACC,EAAE,KAAKI,UAAU,GAClB;QAAE,GAAGL,IAAI;QAAEG,QAAQ,EAAEA;MAAS,CAAC,GAC/BH,IACN,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMQ,SAAS,GAAGlC,IAAI,CAACmC,MAAM,CAAC,CAACC,KAAK,EAAEV,IAAI,KAAKU,KAAK,GAAIV,IAAI,CAACW,KAAK,GAAGX,IAAI,CAACG,QAAS,EAAE,CAAC,CAAC;;EAEvF;EACA,MAAMS,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,EAAChD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,UAAU,GAAE;MACrBU,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF;IAEAb,wBAAwB,CAAC,IAAI,CAAC;IAC9B,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,YAAY,oBAAoBpB,IAAI,CAACqB,UAAU,EAAE,CAAC;MAClF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBhB,gBAAgB,CAACc,IAAI,CAACA,IAAI,CAAC;QAC3BI,OAAO,CAACkB,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,MAAM;QACLlB,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEH,IAAI,CAACuB,OAAO,CAAC;MACjE;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACRb,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAC,KAAK,CAAC,6CAA6C,CAAC;EACtD,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACAD,KAAK,CAAC,6CAA6C,CAAC;EACtD,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAF,KAAK,CAAC,yCAAyC,CAAC;EAClD,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAH,KAAK,CAAC,+CAA+C,CAAC;EACxD,CAAC;EAED,oBACEzD,OAAA;IAAK6D,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElE9D,OAAA;MACE6D,SAAS,EAAC,mFAAmF;MAC7FE,KAAK,EAAE;QACLC,eAAe,EAAE;MACnB,CAAE;MAAAF,QAAA,gBAEF9D,OAAA;QAAK6D,SAAS,EAAC;MAA8B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpDpE,OAAA;QAAK6D,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1D9D,OAAA;UAAK6D,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB9D,OAAA;YAAM6D,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAAC;UAE5G;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpE,OAAA;UAAI6D,SAAS,EAAC,wDAAwD;UAAAC,QAAA,GAAC,cAErE,eAAA9D,OAAA;YAAM6D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACLpE,OAAA;UAAG6D,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EACpFzD,IAAI,GACD,iBAAiBA,IAAI,CAACgE,IAAI,yEAAyE,GACnG;QAAqG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAExG,CAAC,eACJpE,OAAA;UAAK6D,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAC5DzD,IAAI,gBACHL,OAAA,CAAAE,SAAA;YAAA4D,QAAA,gBACE9D,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Fb,SAAS,EAAC,4NAA4N;cAAAC,QAAA,EACvO;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Fb,SAAS,EAAC,iJAAiJ;cAAAC,QAAA,EAC5J;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHpE,OAAA,CAAAE,SAAA;YAAA4D,QAAA,gBACE9D,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAAC,IAAI,CAAE;cACvCqD,SAAS,EAAC,4NAA4N;cAAAC,QAAA,EACvO;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAACC,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAE;cAC1Fb,SAAS,EAAC,iJAAiJ;cAAAC,QAAA,EAC5J;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpE,OAAA;MAAS0C,EAAE,EAAC,UAAU;MAACmB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACpD9D,OAAA;QAAK6D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9D,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9D,OAAA;YAAM6D,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EAAC;UAE3G;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPpE,OAAA;YAAI6D,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFpE,OAAA;YAAG6D,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENpE,OAAA;UAAK6D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBAEnE9D,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMjE,IAAI,GAAGmD,eAAe,CAAC,CAAC,GAAGhD,iBAAiB,CAAC,IAAI,CAAE;YAClEqD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC9D,OAAA;cAAK6D,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/K9D,OAAA;gBAAK6D,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7K9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsH;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEpE,OAAA;gBAAG6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA2C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACnF,CAAC/D,IAAI,iBAAIL,OAAA;gBAAG6D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjF/D,IAAI,iBAAIL,OAAA;gBAAG6D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAErD,WAAW,CAACyE,MAAM,EAAC,oBAAkB;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpE,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMjE,IAAI,GAAGqD,gBAAgB,CAAC,CAAC,GAAGlD,iBAAiB,CAAC,IAAI,CAAE;YACnEqD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC9D,OAAA;cAAK6D,SAAS,EAAC,qKAAqK;cAAAC,QAAA,gBAClL9D,OAAA;gBAAK6D,SAAS,EAAC,kKAAkK;gBAAAC,QAAA,eAC/K9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAwF;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7I;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEpE,OAAA;gBAAG6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAwC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAChF,CAAC/D,IAAI,iBAAIL,OAAA;gBAAG6D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjF/D,IAAI,iBAAIL,OAAA;gBAAG6D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAEnD,YAAY,CAACuE,MAAM,EAAC,eAAa;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpE,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMX,YAAY,CAAC,CAAE;YAC9BE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC9D,OAAA;cAAK6D,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrL9D,OAAA;gBAAK6D,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjL9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuQ;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5T;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEpE,OAAA;gBAAG6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5EpE,OAAA;gBAAG6D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,GAAEjD,SAAS,CAACqE,MAAM,EAAC,sBAAoB;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAChGrD,IAAI,CAACmE,MAAM,GAAG,CAAC,iBACdlF,OAAA;gBAAG6D,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,GAAE/C,IAAI,CAACmE,MAAM,EAAC,gBAAc;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACvF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpE,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMjE,IAAI,GAAGuD,iBAAiB,CAAC,CAAC,GAAGpD,iBAAiB,CAAC,IAAI,CAAE;YACpEqD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAEhC9D,OAAA;cAAK6D,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/K9D,OAAA;gBAAK6D,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7K9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAsH;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3K;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEpE,OAAA;gBAAG6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAoC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EAC5E,CAAC/D,IAAI,iBAAIL,OAAA;gBAAG6D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjF/D,IAAI,iBAAIL,OAAA;gBAAG6D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAA0B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpE,OAAA;YAAK6D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC9D,OAAA;cAAK6D,SAAS,EAAC,+JAA+J;cAAAC,QAAA,gBAC5K9D,OAAA;gBAAK6D,SAAS,EAAC,8JAA8J;gBAAAC,QAAA,eAC3K9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuN;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5Q;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEpE,OAAA;gBAAG6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1EpE,OAAA;gBAAG6D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNpE,OAAA;YAAK6D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC9D,OAAA;cAAK6D,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrL9D,OAAA;gBAAK6D,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjL9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAA6H;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7DpE,OAAA;gBAAG6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9EpE,OAAA;gBAAG6D,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpE,OAAA;MAAQ6D,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAClF9D,OAAA;QAAK6D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9D,OAAA;UAAK6D,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD9D,OAAA;YAAK6D,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvC9D,OAAA;cAAK6D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC9D,OAAA;gBAAK6D,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,eACrH9D,OAAA;kBAAK6D,SAAS,EAAC,oBAAoB;kBAACc,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eACvG9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAA6H;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAI6D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNpE,OAAA;cAAG6D,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENpE,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAI6D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DpE,OAAA;cAAI6D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvB9D,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAQsE,OAAO,EAAEA,CAAA,KAAMjE,IAAI,GAAGmD,eAAe,CAAC,CAAC,GAAGhD,iBAAiB,CAAC,IAAI,CAAE;kBAACqD,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpKpE,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAQsE,OAAO,EAAEA,CAAA,KAAMjE,IAAI,GAAGqD,gBAAgB,CAAC,CAAC,GAAGlD,iBAAiB,CAAC,IAAI,CAAE;kBAACqD,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvKpE,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAQsE,OAAO,EAAEA,CAAA,KAAMX,YAAY,CAAC,CAAE;kBAACE,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9HpE,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAQsE,OAAO,EAAEA,CAAA,KAAMjE,IAAI,GAAGuD,iBAAiB,CAAC,CAAC,GAAGpD,iBAAiB,CAAC,IAAI,CAAE;kBAACqD,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENpE,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAI6D,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDpE,OAAA;cAAI6D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvB9D,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAGmF,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGpE,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAGmF,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChGpE,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAGmF,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpGpE,OAAA;gBAAA8D,QAAA,eAAI9D,OAAA;kBAAGmF,IAAI,EAAC,GAAG;kBAACtB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpE,OAAA;UAAK6D,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxD9D,OAAA;YAAG6D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGR7D,cAAc,iBACbP,OAAA;MAAK6D,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9D,OAAA;QAAK6D,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9D9D,OAAA;UAAK6D,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB9D,OAAA;YAAK6D,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF9D,OAAA;cAAI6D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEpE,OAAA;cACEsE,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAAC,KAAK,CAAE;cACxCqD,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpE,OAAA,CAACF,KAAK;YACJsF,OAAO,EAAGC,QAAQ,IAAK;cACrB7E,iBAAiB,CAAC,KAAK,CAAC;cACxB;cACAF,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAE;YACFgF,OAAO,EAAEA,CAAA,KAAM9E,iBAAiB,CAAC,KAAK;UAAE;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChE,EAAA,CAlZID,aAAa;EAAA,QACAN,OAAO,EACPD,WAAW;AAAA;AAAA2F,EAAA,GAFxBpF,aAAa;AAoZnB,eAAeA,aAAa;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Login from '../components/Login';

const PatientPortal = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [examReports, setExamReports] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [medicines, setMedicines] = useState([]);
  const [cart, setCart] = useState([]);
  const [medicalRecord, setMedicalRecord] = useState(null);
  const [showMedicalRecordsModal, setShowMedicalRecordsModal] = useState(false);
  const [medicalRecordsLoading, setMedicalRecordsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const API_BASE_URL = 'http://localhost:5000/api';

  // Fetch patient data on component mount (only if logged in)
  useEffect(() => {
    if (user?.nationalId) {
      fetchExamReports();
      fetchAppointments();
    }
    // Always fetch medicines for pharmacy browsing
    fetchMedicines();
  }, [user]);

  // Fetch exam reports for the patient
  const fetchExamReports = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);
      const data = await response.json();
      if (data.success) {
        setExamReports(data.data);
      }
    } catch (error) {
      console.error('Error fetching exam reports:', error);
    }
  };

  // Fetch patient appointments
  const fetchAppointments = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);
      const data = await response.json();
      if (data.success) {
        setAppointments(data.data);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    }
  };

  // Fetch available medicines
  const fetchMedicines = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/pharmacy`);
      const data = await response.json();
      if (data.success) {
        setMedicines(data.data);
      }
    } catch (error) {
      console.error('Error fetching medicines:', error);
    }
  };

  // Add medicine to cart
  const addToCart = (medicine) => {
    const existingItem = cart.find(item => item.id === medicine.id);
    if (existingItem) {
      setCart(cart.map(item => 
        item.id === medicine.id 
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { ...medicine, quantity: 1 }]);
    }
  };

  // Remove from cart
  const removeFromCart = (medicineId) => {
    setCart(cart.filter(item => item.id !== medicineId));
  };

  // Update cart quantity
  const updateCartQuantity = (medicineId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(medicineId);
    } else {
      setCart(cart.map(item => 
        item.id === medicineId 
          ? { ...item, quantity: quantity }
          : item
      ));
    }
  };

  // Calculate cart total
  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

  // Fetch medical records for the logged-in patient
  const fetchMedicalRecords = async () => {
    if (!user?.nationalId) {
      console.error('No user national ID available');
      return;
    }

    setMedicalRecordsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/medical-records/${user.nationalId}`);
      const data = await response.json();

      if (data.success) {
        setMedicalRecord(data.data);
        console.log('Medical records fetched successfully');
      } else {
        console.error('Failed to fetch medical records:', data.message);
      }
    } catch (error) {
      console.error('Error fetching medical records:', error);
    } finally {
      setMedicalRecordsLoading(false);
    }
  };

  // Navigation functions
  const openExamReports = () => {
    // Create a modal or navigate to exam reports section
    alert('Opening Exam Reports - Feature coming soon!');
  };

  const openAppointments = () => {
    // Create a modal or navigate to appointments section
    alert('Opening Appointments - Feature coming soon!');
  };

  const openPharmacy = () => {
    // Create a modal or navigate to pharmacy section
    alert('Opening Pharmacy - Feature coming soon!');
  };

  const openHealthRecords = async () => {
    if (!user) {
      setShowLoginModal(true);
      return;
    }

    setShowMedicalRecordsModal(true);
    await fetchMedicalRecords();
  };

  // Helper functions for medical records
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-gradient-to-br from-gray-50 to-blue-50 font-sans">
      {/* Hero Section */}
      <section
        className="relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center"
        style={{
          backgroundImage: "url('/image/Screenshot 2025-04-21 200615.png')"
        }}
      >
        <div className="absolute inset-0 bg-black/40"></div>
        <div className="relative z-10 text-center max-w-5xl mx-auto">
          <div className="mb-6">
            <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4">
              🏥 Your Personal Health Portal
            </span>
          </div>
          <h1 className="text-4xl md:text-7xl font-extrabold mb-6 leading-tight">
            Your Health,
            <span className="text-yellow-400 block">Your Way</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
            {user 
              ? `Welcome back, ${user.name}! Access your health records, manage appointments, and order medicines.`
              : 'Access your health records, book appointments, and manage your healthcare journey all in one place.'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {user ? (
              <>
                <button 
                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  View My Health Records
                </button>
                <button 
                  onClick={() => document.getElementById('pharmacy').scrollIntoView({ behavior: 'smooth' })}
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30"
                >
                  Order Medicines
                </button>
              </>
            ) : (
              <>
                <button 
                  onClick={() => setShowLoginModal(true)}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  Sign In to Your Portal
                </button>
                <button 
                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30"
                >
                  Explore Services
                </button>
              </>
            )}
          </div>
        </div>
      </section>

      {/* Patient Services Section */}
      <section id="services" className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4">
              Your Health Services
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Patient Services</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Access your personal health information and manage your healthcare journey
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Exam Reports */}
            <div 
              onClick={() => user ? openExamReports() : setShowLoginModal(true)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">My Exam Reports</h3>
                <p className="text-sm text-gray-600">View and download your medical test results</p>
                {!user && <p className="text-xs text-blue-600 mt-2 font-medium">Login required</p>}
                {user && <p className="text-xs text-green-600 mt-2 font-medium">{examReports.length} reports available</p>}
              </div>
            </div>

            {/* Appointments */}
            <div 
              onClick={() => user ? openAppointments() : setShowLoginModal(true)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">My Appointments</h3>
                <p className="text-sm text-gray-600">Book and manage your doctor appointments</p>
                {!user && <p className="text-xs text-blue-600 mt-2 font-medium">Login required</p>}
                {user && <p className="text-xs text-green-600 mt-2 font-medium">{appointments.length} appointments</p>}
              </div>
            </div>

            {/* Online Pharmacy */}
            <div
              onClick={() => openPharmacy()}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Online Pharmacy</h3>
                <p className="text-sm text-gray-600">Order medicines and health products</p>
                <p className="text-xs text-green-600 mt-2 font-medium">{medicines.length} medicines available</p>
                {cart.length > 0 && (
                  <p className="text-xs text-orange-600 mt-1 font-medium">{cart.length} items in cart</p>
                )}
              </div>
            </div>

            {/* Health Records */}
            <div
              onClick={() => user ? openHealthRecords() : setShowLoginModal(true)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Health Records</h3>
                <p className="text-sm text-gray-600">Access your complete medical history</p>
                {!user && <p className="text-xs text-blue-600 mt-2 font-medium">Login required</p>}
                {user && <p className="text-xs text-green-600 mt-2 font-medium">Complete records available</p>}
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Emergency Contact</h3>
                <p className="text-sm text-gray-600">24/7 emergency medical assistance</p>
                <p className="text-xs text-red-600 mt-2 font-medium">Call: 112</p>
              </div>
            </div>

            {/* Health Tips */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Health Tips</h3>
                <p className="text-sm text-gray-600">Daily health tips and wellness advice</p>
                <p className="text-xs text-orange-600 mt-2 font-medium">Updated daily</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold">Patient Portal</h3>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Your personal healthcare portal providing secure access to medical records, appointments, and health services.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Access</h4>
              <ul className="space-y-2">
                <li><button onClick={() => user ? openExamReports() : setShowLoginModal(true)} className="text-gray-300 hover:text-white transition-colors">My Reports</button></li>
                <li><button onClick={() => user ? openAppointments() : setShowLoginModal(true)} className="text-gray-300 hover:text-white transition-colors">Appointments</button></li>
                <li><button onClick={() => openPharmacy()} className="text-gray-300 hover:text-white transition-colors">Pharmacy</button></li>
                <li><button onClick={() => user ? openHealthRecords() : setShowLoginModal(true)} className="text-gray-300 hover:text-white transition-colors">Health Records</button></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Emergency: 112</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-gray-400">
              &copy; 2025 HealthCarePro Patient Portal. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Medical Records Modal */}
      {showMedicalRecordsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6 border-b border-gray-200 pb-4">
                <h3 className="text-2xl font-bold text-gray-900">My Medical Records</h3>
                <button
                  onClick={() => setShowMedicalRecordsModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100"
                >
                  ×
                </button>
              </div>

              {medicalRecordsLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-300 border-t-blue-600"></div>
                  <span className="ml-3 text-gray-600">Loading your medical records...</span>
                </div>
              ) : medicalRecord ? (
                <div className="space-y-6">
                  {/* Patient Info Header */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                        <span className="text-white font-bold text-lg">
                          {user?.name?.charAt(0) || 'P'}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">{user?.name}</h4>
                        <p className="text-gray-600">National ID: {user?.nationalId}</p>
                      </div>
                    </div>
                  </div>

                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-white border border-gray-200 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="text-sm font-medium text-gray-600">Exams</h5>
                          <p className="text-2xl font-bold text-blue-600">{medicalRecord.summary?.totalExams || 0}</p>
                        </div>
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <span className="text-blue-600">🔬</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white border border-gray-200 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="text-sm font-medium text-gray-600">Prescriptions</h5>
                          <p className="text-2xl font-bold text-green-600">{medicalRecord.summary?.totalPrescriptions || 0}</p>
                        </div>
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <span className="text-green-600">💊</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white border border-gray-200 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="text-sm font-medium text-gray-600">Appointments</h5>
                          <p className="text-2xl font-bold text-purple-600">{medicalRecord.summary?.totalAppointments || 0}</p>
                        </div>
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                          <span className="text-purple-600">📅</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white border border-gray-200 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h5 className="text-sm font-medium text-gray-600">Room Stays</h5>
                          <p className="text-2xl font-bold text-orange-600">{medicalRecord.summary?.totalRoomAssignments || 0}</p>
                        </div>
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                          <span className="text-orange-600">🏥</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tab Navigation */}
                  <div className="border-b border-gray-200">
                    <nav className="flex space-x-8">
                      {[
                        { id: 'overview', label: '📋 Overview' },
                        { id: 'exams', label: '🔬 Exams' },
                        { id: 'prescriptions', label: '💊 Prescriptions' },
                        { id: 'appointments', label: '📅 Appointments' }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`py-3 px-1 border-b-2 font-medium text-sm ${
                            activeTab === tab.id
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          {tab.label}
                        </button>
                      ))}
                    </nav>
                  </div>

                  {/* Tab Content */}
                  <div className="min-h-[300px]">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-900">📋 Medical Overview</h4>

                        {/* Recent Exams */}
                        {medicalRecord.exams && medicalRecord.exams.length > 0 && (
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h5 className="font-medium text-blue-900 mb-3">🔬 Recent Exams</h5>
                            <div className="space-y-2">
                              {medicalRecord.exams.slice(0, 3).map((exam) => (
                                <div key={exam.id} className="flex justify-between items-center">
                                  <div>
                                    <span className="font-medium text-blue-800">{exam.examType}</span>
                                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(exam.status)}`}>
                                      {exam.status}
                                    </span>
                                  </div>
                                  <span className="text-blue-600 text-sm">{formatDate(exam.examDate)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Recent Prescriptions */}
                        {medicalRecord.prescriptions && medicalRecord.prescriptions.length > 0 && (
                          <div className="bg-green-50 p-4 rounded-lg">
                            <h5 className="font-medium text-green-900 mb-3">💊 Recent Prescriptions</h5>
                            <div className="space-y-2">
                              {medicalRecord.prescriptions.slice(0, 3).map((prescription) => (
                                <div key={prescription.id} className="flex justify-between items-center">
                                  <div>
                                    <span className="font-medium text-green-800">{prescription.diagnosis}</span>
                                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(prescription.status)}`}>
                                      {prescription.status}
                                    </span>
                                  </div>
                                  <span className="text-green-600 text-sm">{formatDate(prescription.prescriptionDate)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Exams Tab */}
                    {activeTab === 'exams' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-900">🔬 Medical Exams</h4>

                        {!medicalRecord.exams || medicalRecord.exams.length === 0 ? (
                          <div className="text-center py-8">
                            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                              <span className="text-gray-400 text-2xl">🔬</span>
                            </div>
                            <p className="text-gray-500">No exams recorded</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {medicalRecord.exams.map((exam) => (
                              <div key={exam.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                <div className="flex justify-between items-start mb-3">
                                  <div>
                                    <h5 className="font-semibold text-gray-900">{exam.examType}</h5>
                                    <p className="text-sm text-gray-600">Date: {formatDate(exam.examDate)}</p>
                                  </div>
                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(exam.status)}`}>
                                    {exam.status}
                                  </span>
                                </div>
                                <div className="space-y-2">
                                  <div>
                                    <h6 className="font-medium text-gray-700">Results:</h6>
                                    <p className="text-gray-600">{exam.results}</p>
                                  </div>
                                  {exam.notes && (
                                    <div>
                                      <h6 className="font-medium text-gray-700">Notes:</h6>
                                      <p className="text-gray-600">{exam.notes}</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Prescriptions Tab */}
                    {activeTab === 'prescriptions' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-900">💊 Prescriptions</h4>

                        {!medicalRecord.prescriptions || medicalRecord.prescriptions.length === 0 ? (
                          <div className="text-center py-8">
                            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                              <span className="text-gray-400 text-2xl">💊</span>
                            </div>
                            <p className="text-gray-500">No prescriptions recorded</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {medicalRecord.prescriptions.map((prescription) => (
                              <div key={prescription.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                <div className="flex justify-between items-start mb-3">
                                  <div>
                                    <h5 className="font-semibold text-gray-900">Prescription #{prescription.prescriptionId}</h5>
                                    <p className="text-sm text-gray-600">
                                      Date: {formatDate(prescription.prescriptionDate)}
                                      {prescription.doctorFirstName && (
                                        <> • Dr. {prescription.doctorFirstName} {prescription.doctorLastName}</>
                                      )}
                                    </p>
                                  </div>
                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(prescription.status)}`}>
                                    {prescription.status}
                                  </span>
                                </div>
                                <div className="space-y-2">
                                  <div>
                                    <h6 className="font-medium text-gray-700">Diagnosis:</h6>
                                    <p className="text-gray-600">{prescription.diagnosis}</p>
                                  </div>
                                  <div>
                                    <h6 className="font-medium text-gray-700">Medication:</h6>
                                    <p className="text-gray-600">{prescription.medication}</p>
                                  </div>
                                  <div>
                                    <h6 className="font-medium text-gray-700">Dosage:</h6>
                                    <p className="text-gray-600">{prescription.dosage}</p>
                                  </div>
                                  <div>
                                    <h6 className="font-medium text-gray-700">Instructions:</h6>
                                    <p className="text-gray-600">{prescription.instructions}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Appointments Tab */}
                    {activeTab === 'appointments' && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-900">📅 Appointments</h4>

                        {!medicalRecord.appointments || medicalRecord.appointments.length === 0 ? (
                          <div className="text-center py-8">
                            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                              <span className="text-gray-400 text-2xl">📅</span>
                            </div>
                            <p className="text-gray-500">No appointments recorded</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {medicalRecord.appointments.map((appointment) => (
                              <div key={appointment.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                <div className="flex justify-between items-start mb-3">
                                  <div>
                                    <h5 className="font-semibold text-gray-900">{appointment.appointmentType}</h5>
                                    <p className="text-sm text-gray-600">
                                      Date: {formatDate(appointment.appointmentDate)}
                                      {appointment.doctorFirstName && (
                                        <> • Dr. {appointment.doctorFirstName} {appointment.doctorLastName}</>
                                      )}
                                    </p>
                                  </div>
                                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(appointment.status)}`}>
                                    {appointment.status}
                                  </span>
                                </div>
                                {appointment.notes && (
                                  <div>
                                    <h6 className="font-medium text-gray-700">Notes:</h6>
                                    <p className="text-gray-600">{appointment.notes}</p>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">📋</span>
                  </div>
                  <p className="text-gray-500">No medical records found</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Login Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6 border-b border-gray-200 pb-4">
                <h3 className="text-2xl font-bold text-gray-900">Patient Login</h3>
                <button
                  onClick={() => setShowLoginModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100"
                >
                  ×
                </button>
              </div>

              <Login
                onLogin={() => {
                  setShowLoginModal(false);
                  // Redirect to home page after successful login
                  navigate('/home');
                }}
                onClose={() => setShowLoginModal(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientPortal;

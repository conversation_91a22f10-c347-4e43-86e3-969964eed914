import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import Login from '../components/Login';

const PatientPortal = () => {
  const { user } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [examReports, setExamReports] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [medicines, setMedicines] = useState([]);
  const [cart, setCart] = useState([]);

  const API_BASE_URL = 'http://localhost:5000/api';

  // Fetch patient data on component mount (only if logged in)
  useEffect(() => {
    if (user?.nationalId) {
      fetchExamReports();
      fetchAppointments();
    }
    // Always fetch medicines for pharmacy browsing
    fetchMedicines();
  }, [user]);

  // Fetch exam reports for the patient
  const fetchExamReports = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/exams/patient/${user.nationalId}`);
      const data = await response.json();
      if (data.success) {
        setExamReports(data.data);
      }
    } catch (error) {
      console.error('Error fetching exam reports:', error);
    }
  };

  // Fetch patient appointments
  const fetchAppointments = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/appointments/patient/${user.nationalId}`);
      const data = await response.json();
      if (data.success) {
        setAppointments(data.data);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    }
  };

  // Fetch available medicines
  const fetchMedicines = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/pharmacy`);
      const data = await response.json();
      if (data.success) {
        setMedicines(data.data);
      }
    } catch (error) {
      console.error('Error fetching medicines:', error);
    }
  };

  // Add medicine to cart
  const addToCart = (medicine) => {
    const existingItem = cart.find(item => item.id === medicine.id);
    if (existingItem) {
      setCart(cart.map(item => 
        item.id === medicine.id 
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { ...medicine, quantity: 1 }]);
    }
  };

  // Remove from cart
  const removeFromCart = (medicineId) => {
    setCart(cart.filter(item => item.id !== medicineId));
  };

  // Update cart quantity
  const updateCartQuantity = (medicineId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(medicineId);
    } else {
      setCart(cart.map(item => 
        item.id === medicineId 
          ? { ...item, quantity: quantity }
          : item
      ));
    }
  };

  // Calculate cart total
  const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

  // Navigation functions
  const openExamReports = () => {
    // Create a modal or navigate to exam reports section
    alert('Opening Exam Reports - Feature coming soon!');
  };

  const openAppointments = () => {
    // Create a modal or navigate to appointments section
    alert('Opening Appointments - Feature coming soon!');
  };

  const openPharmacy = () => {
    // Create a modal or navigate to pharmacy section
    alert('Opening Pharmacy - Feature coming soon!');
  };

  const openHealthRecords = () => {
    // Create a modal or navigate to health records section
    alert('Opening Health Records - Feature coming soon!');
  };

  return (
    <div className="bg-gradient-to-br from-gray-50 to-blue-50 font-sans">
      {/* Hero Section */}
      <section
        className="relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center"
        style={{
          backgroundImage: "url('/image/Screenshot 2025-04-21 200615.png')"
        }}
      >
        <div className="absolute inset-0 bg-black/40"></div>
        <div className="relative z-10 text-center max-w-5xl mx-auto">
          <div className="mb-6">
            <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4">
              🏥 Your Personal Health Portal
            </span>
          </div>
          <h1 className="text-4xl md:text-7xl font-extrabold mb-6 leading-tight">
            Your Health,
            <span className="text-yellow-400 block">Your Way</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
            {user 
              ? `Welcome back, ${user.name}! Access your health records, manage appointments, and order medicines.`
              : 'Access your health records, book appointments, and manage your healthcare journey all in one place.'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {user ? (
              <>
                <button 
                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  View My Health Records
                </button>
                <button 
                  onClick={() => document.getElementById('pharmacy').scrollIntoView({ behavior: 'smooth' })}
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30"
                >
                  Order Medicines
                </button>
              </>
            ) : (
              <>
                <button 
                  onClick={() => setShowLoginModal(true)}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  Sign In to Your Portal
                </button>
                <button 
                  onClick={() => document.getElementById('services').scrollIntoView({ behavior: 'smooth' })}
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30"
                >
                  Explore Services
                </button>
              </>
            )}
          </div>
        </div>
      </section>

      {/* Patient Services Section */}
      <section id="services" className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4">
              Your Health Services
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Patient Services</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Access your personal health information and manage your healthcare journey
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Exam Reports */}
            <div 
              onClick={() => user ? openExamReports() : setShowLoginModal(true)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">My Exam Reports</h3>
                <p className="text-sm text-gray-600">View and download your medical test results</p>
                {!user && <p className="text-xs text-blue-600 mt-2 font-medium">Login required</p>}
                {user && <p className="text-xs text-green-600 mt-2 font-medium">{examReports.length} reports available</p>}
              </div>
            </div>

            {/* Appointments */}
            <div 
              onClick={() => user ? openAppointments() : setShowLoginModal(true)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">My Appointments</h3>
                <p className="text-sm text-gray-600">Book and manage your doctor appointments</p>
                {!user && <p className="text-xs text-blue-600 mt-2 font-medium">Login required</p>}
                {user && <p className="text-xs text-green-600 mt-2 font-medium">{appointments.length} appointments</p>}
              </div>
            </div>

            {/* Online Pharmacy */}
            <div
              onClick={() => openPharmacy()}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Online Pharmacy</h3>
                <p className="text-sm text-gray-600">Order medicines and health products</p>
                <p className="text-xs text-green-600 mt-2 font-medium">{medicines.length} medicines available</p>
                {cart.length > 0 && (
                  <p className="text-xs text-orange-600 mt-1 font-medium">{cart.length} items in cart</p>
                )}
              </div>
            </div>

            {/* Health Records */}
            <div
              onClick={() => user ? openHealthRecords() : setShowLoginModal(true)}
              className="group cursor-pointer"
            >
              <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Health Records</h3>
                <p className="text-sm text-gray-600">Access your complete medical history</p>
                {!user && <p className="text-xs text-blue-600 mt-2 font-medium">Login required</p>}
                {user && <p className="text-xs text-green-600 mt-2 font-medium">Complete records available</p>}
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Emergency Contact</h3>
                <p className="text-sm text-gray-600">24/7 emergency medical assistance</p>
                <p className="text-xs text-red-600 mt-2 font-medium">Call: 112</p>
              </div>
            </div>

            {/* Health Tips */}
            <div className="group cursor-pointer">
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Health Tips</h3>
                <p className="text-sm text-gray-600">Daily health tips and wellness advice</p>
                <p className="text-xs text-orange-600 mt-2 font-medium">Updated daily</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold">Patient Portal</h3>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Your personal healthcare portal providing secure access to medical records, appointments, and health services.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Access</h4>
              <ul className="space-y-2">
                <li><button onClick={() => user ? openExamReports() : setShowLoginModal(true)} className="text-gray-300 hover:text-white transition-colors">My Reports</button></li>
                <li><button onClick={() => user ? openAppointments() : setShowLoginModal(true)} className="text-gray-300 hover:text-white transition-colors">Appointments</button></li>
                <li><button onClick={() => openPharmacy()} className="text-gray-300 hover:text-white transition-colors">Pharmacy</button></li>
                <li><button onClick={() => user ? openHealthRecords() : setShowLoginModal(true)} className="text-gray-300 hover:text-white transition-colors">Health Records</button></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Emergency: 112</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-gray-400">
              &copy; 2025 HealthCarePro Patient Portal. All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Login Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6 border-b border-gray-200 pb-4">
                <h3 className="text-2xl font-bold text-gray-900">Patient Login</h3>
                <button
                  onClick={() => setShowLoginModal(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100"
                >
                  ×
                </button>
              </div>

              <Login
                onLogin={(userData) => {
                  setShowLoginModal(false);
                  // The user state will be updated automatically by the auth context
                }}
                onClose={() => setShowLoginModal(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientPortal;

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\Home.jsx\";\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-gray-50 to-blue-50 font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center\",\n      style: {\n        backgroundImage: \"url('/image/Screenshot 2025-04-21 200615.png')\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black/40\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 text-center max-w-5xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4\",\n            children: \"\\uD83C\\uDFE5 Trusted Healthcare Platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-7xl font-extrabold mb-6 leading-tight\",\n          children: [\"Quality healthcare for\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-yellow-400 block\",\n            children: \"community\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Experience seamless healthcare management with our comprehensive digital platform designed for modern medical care.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\",\n            children: \"Explore Our Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 px-6 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4\",\n            children: \"Our Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n            children: \"Healthcare Categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Access comprehensive healthcare services through our integrated digital platform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/medical-records\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/website (1).png\",\n                  alt: \"Medical Records\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Medical Records\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Secure digital health records\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/pharmacy\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/syringe (1).png\",\n                  alt: \"Pharmacy\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Pharmacy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Online prescription services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/doctor\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/doctor.png\",\n                  alt: \"Doctor\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Doctor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Connect with specialists\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/patients\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/user.png\",\n                  alt: \"Patients\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Patients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Patient management system\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/hospital-transfer\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/website.png\",\n                  alt: \"Hospital Transfer\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Hospital Transfer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Seamless facility transfers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/exams\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/exam.png\",\n                  alt: \"Exams\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Exams\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Medical examinations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/messages\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-pink-50 to-pink-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-pink-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/communication.png\",\n                  alt: \"Messages\",\n                  className: \"w-8 h-8 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Messages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Secure communication\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/room\",\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-indigo-200/50\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-gray-900 mb-2\",\n                children: \"Room Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Hospital room & bed tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-semibold mb-4\",\n            children: \"\\u2B50 Featured This Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n            children: \"Top Services this Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n            children: \"Discover our most popular healthcare services trusted by thousands of patients\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-56 relative overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/image/jc-gellidon-UIp163xCV6w-unsplash.jpg\",\n                  alt: \"Emergency Surgery\",\n                  className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 left-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold\",\n                    children: \"URGENT\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-4 left-4 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium opacity-90\",\n                    children: \"Emergency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\",\n                  children: \"Emergency Surgery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-4\",\n                  children: \"24/7 urgent care and emergency surgical procedures\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Available Now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-500 transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-red-500 group-hover:text-white\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-56 relative overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/image/bennett-tobias-YMpvL5eAtg0-unsplash.jpg\",\n                  alt: \"Child Care\",\n                  className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 left-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold\",\n                    children: \"PEDIATRIC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-4 left-4 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium opacity-90\",\n                    children: \"Children\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\",\n                  children: \"Child Care\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-4\",\n                  children: \"Specialized pediatric care from 12-1 PM daily\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"12:00 - 13:00\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-500 transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-blue-500 group-hover:text-white\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-56 relative overflow-hidden\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/image/towfiqu-barbhuiya-FsVEqeiOtPo-unsplash.jpg\",\n                  alt: \"Medical Supplies\",\n                  className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-4 left-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold\",\n                    children: \"FREE DELIVERY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-4 left-4 text-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium opacity-90\",\n                    children: \"Medical\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\",\n                  children: \"Medical Supplies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-4\",\n                  children: \"Essential medical supplies with free home delivery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"Free Delivery\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-500 transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4 text-green-500 group-hover:text-white\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 px-6 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-semibold mb-4\",\n            children: \"\\uD83D\\uDE80 Platform Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\",\n            children: \"Healthcare platform has multitude of features and modules to ensure all relevant activities and processes in hospitals are digitized.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/patient-program.png\",\n                  alt: \"Medical Records\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Medical Records\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Automated medical reports generated by data input of doctors per patient all in one place in a particular hospital.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/surgeon.png\",\n                  alt: \"Doctor\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Doctor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Each doctor has a profile with all necessary data with a dashboard of all activities and appointments.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/immune-system.png\",\n                  alt: \"Immunisation Services\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Immunisation Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Immunization protects individuals and communities from infectious diseases, reducing illness, saving lives, and promoting public health worldwide.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/pharmacy.png\",\n                  alt: \"Online Pharmacy\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Online Pharmacy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Online pharmacies offer convenient, accessible medication services, improving healthcare delivery, saving time, and enhancing patient compliance globally.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/appointment-request.png\",\n                  alt: \"Appointment Scheduling\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Appointment Scheduling\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Appointment scheduling streamlines healthcare access, reduces wait times, improves efficiency, enhances patient satisfaction, and supports organized medical practices.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-pink-50 to-pink-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-pink-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/medical-team.png\",\n                  alt: \"Collaboration\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Collaboration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Our platform allows better collaboration and coordination of staff and patient hence increasing productivity and efficiency.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-indigo-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/efficiency.png\",\n                  alt: \"Productivity Tracking\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Productivity Tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Tracks efficiency and output, but risks burnout and care quality if not balanced with context and compassion.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-emerald-50 to-emerald-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-emerald-200/50 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-20 h-20 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/icons/paperless.png\",\n                  alt: \"Paperless Program\",\n                  className: \"w-10 h-10 object-contain filter brightness-0 invert\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-3\",\n                children: \"Paperless Program\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: \"Our platform allows hospitals to move from traditional paper ways to digitized paperless program.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(Billing, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-1 md:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"HEALTHCARE Portal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-300 mb-6 max-w-md leading-relaxed\",\n              children: \"Your trusted healthcare management platform providing comprehensive digital solutions for modern medical care and patient management.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"\\uD83D\\uDCE7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"\\uD83D\\uDCF1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"\\uD83C\\uDF10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Quick Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"About Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Doctors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Contact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold mb-4\",\n              children: \"Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Help Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Terms of Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"text-gray-300 hover:text-white transition-colors\",\n                  children: \"Emergency: 112\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t border-gray-700 pt-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400\",\n            children: \"\\xA9 2025 HealthCarePro. All rights reserved. | Terms & Privacy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["Link", "jsxDEV", "_jsxDEV", "Home", "className", "children", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "src", "alt", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "fillRule", "clipRule", "Billing", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/pages/Home.jsx"], "sourcesContent": ["import { Link } from 'react-router-dom';\r\n\r\nfunction Home() {\r\n  return(\r\n    <div className=\"bg-gradient-to-br from-gray-50 to-blue-50 font-sans\">\r\n      {/* Hero Section */}\r\n      <section\r\n        className=\"relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center\"\r\n        style={{\r\n          backgroundImage: \"url('/image/Screenshot 2025-04-21 200615.png')\"\r\n        }}\r\n      >\r\n        <div className=\"absolute inset-0 bg-black/40\"></div>\r\n        <div className=\"relative z-10 text-center max-w-5xl mx-auto\">\r\n          <div className=\"mb-6\">\r\n            <span className=\"inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4\">\r\n              🏥 Trusted Healthcare Platform\r\n            </span>\r\n          </div>\r\n          <h1 className=\"text-4xl md:text-7xl font-extrabold mb-6 leading-tight\">\r\n            Quality healthcare for\r\n            <span className=\"text-yellow-400 block\">community</span>\r\n          </h1>\r\n          <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\">\r\n            Experience seamless healthcare management with our comprehensive digital platform designed for modern medical care.\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <button className=\"bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\">\r\n              Explore Our Services\r\n            </button>\r\n            <button className=\"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30\">\r\n              Learn More\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Healthcare Categories */}\r\n      <section className=\"py-20 px-6 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center mb-16\">\r\n            <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4\">\r\n              Our Services\r\n            </span>\r\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">Healthcare Categories</h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Access comprehensive healthcare services through our integrated digital platform\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8\">\r\n            <Link to=\"/medical-records\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/website (1).png\" alt=\"Medical Records\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Medical Records</h3>\r\n                <p className=\"text-sm text-gray-600\">Secure digital health records</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/pharmacy\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/syringe (1).png\" alt=\"Pharmacy\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Pharmacy</h3>\r\n                <p className=\"text-sm text-gray-600\">Online prescription services</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/doctor\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/doctor.png\" alt=\"Doctor\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Doctor</h3>\r\n                <p className=\"text-sm text-gray-600\">Connect with specialists</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/patients\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/user.png\" alt=\"Patients\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Patients</h3>\r\n                <p className=\"text-sm text-gray-600\">Patient management system</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/hospital-transfer\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/website.png\" alt=\"Hospital Transfer\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Hospital Transfer</h3>\r\n                <p className=\"text-sm text-gray-600\">Seamless facility transfers</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/exams\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/exam.png\" alt=\"Exams\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Exams</h3>\r\n                <p className=\"text-sm text-gray-600\">Medical examinations</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/messages\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-pink-50 to-pink-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-pink-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/communication.png\" alt=\"Messages\" className=\"w-8 h-8 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Messages</h3>\r\n                <p className=\"text-sm text-gray-600\">Secure communication</p>\r\n              </div>\r\n            </Link>\r\n\r\n            <Link to=\"/room\" className=\"group cursor-pointer\">\r\n              <div className=\"bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-indigo-200/50\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\r\n                  <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"font-bold text-gray-900 mb-2\">Room Management</h3>\r\n                <p className=\"text-sm text-gray-600\">Hospital room & bed tracking</p>\r\n              </div>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Top Services Section */}\r\n      <section className=\"py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center mb-16\">\r\n            <span className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-semibold mb-4\">\r\n              ⭐ Featured This Week\r\n            </span>\r\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">Top Services this Week</h1>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Discover our most popular healthcare services trusted by thousands of patients\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            <div className=\"group cursor-pointer\">\r\n              <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100\">\r\n                <div className=\"h-56 relative overflow-hidden\">\r\n                  <img\r\n                    src=\"/image/jc-gellidon-UIp163xCV6w-unsplash.jpg\"\r\n                    alt=\"Emergency Surgery\"\r\n                    className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent\"></div>\r\n                  <div className=\"absolute top-4 left-4\">\r\n                    <span className=\"bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold\">URGENT</span>\r\n                  </div>\r\n                  <div className=\"absolute bottom-4 left-4 text-white\">\r\n                    <span className=\"text-sm font-medium opacity-90\">Emergency</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\">Emergency Surgery</h3>\r\n                  <p className=\"text-gray-600 mb-4\">24/7 urgent care and emergency surgical procedures</p>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span className=\"text-sm text-gray-500\">Available Now</span>\r\n                    <div className=\"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-500 transition-colors\">\r\n                      <svg className=\"w-4 h-4 text-red-500 group-hover:text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                        <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group cursor-pointer\">\r\n              <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100\">\r\n                <div className=\"h-56 relative overflow-hidden\">\r\n                  <img\r\n                    src=\"/image/bennett-tobias-YMpvL5eAtg0-unsplash.jpg\"\r\n                    alt=\"Child Care\"\r\n                    className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent\"></div>\r\n                  <div className=\"absolute top-4 left-4\">\r\n                    <span className=\"bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold\">PEDIATRIC</span>\r\n                  </div>\r\n                  <div className=\"absolute bottom-4 left-4 text-white\">\r\n                    <span className=\"text-sm font-medium opacity-90\">Children</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\">Child Care</h3>\r\n                  <p className=\"text-gray-600 mb-4\">Specialized pediatric care from 12-1 PM daily</p>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span className=\"text-sm text-gray-500\">12:00 - 13:00</span>\r\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-500 transition-colors\">\r\n                      <svg className=\"w-4 h-4 text-blue-500 group-hover:text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                        <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group cursor-pointer\">\r\n              <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100\">\r\n                <div className=\"h-56 relative overflow-hidden\">\r\n                  <img\r\n                    src=\"/image/towfiqu-barbhuiya-FsVEqeiOtPo-unsplash.jpg\"\r\n                    alt=\"Medical Supplies\"\r\n                    className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent\"></div>\r\n                  <div className=\"absolute top-4 left-4\">\r\n                    <span className=\"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold\">FREE DELIVERY</span>\r\n                  </div>\r\n                  <div className=\"absolute bottom-4 left-4 text-white\">\r\n                    <span className=\"text-sm font-medium opacity-90\">Medical</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors\">Medical Supplies</h3>\r\n                  <p className=\"text-gray-600 mb-4\">Essential medical supplies with free home delivery</p>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span className=\"text-sm text-gray-500\">Free Delivery</span>\r\n                    <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-500 transition-colors\">\r\n                      <svg className=\"w-4 h-4 text-green-500 group-hover:text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                        <path fillRule=\"evenodd\" d=\"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-20 px-6 bg-white\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"text-center mb-16\">\r\n            <span className=\"inline-block px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-semibold mb-4\">\r\n              🚀 Platform Features\r\n            </span>\r\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">Features</h1>\r\n            <p className=\"text-xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\">\r\n              Healthcare platform has multitude of features and modules to ensure all relevant activities and processes in hospitals are digitized.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/patient-program.png\" alt=\"Medical Records\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Medical Records</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Automated medical reports generated by data input of doctors per patient all in one place in a particular hospital.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/surgeon.png\" alt=\"Doctor\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Doctor</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Each doctor has a profile with all necessary data with a dashboard of all activities and appointments.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/immune-system.png\" alt=\"Immunisation Services\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Immunisation Services</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Immunization protects individuals and communities from infectious diseases, reducing illness, saving lives, and promoting public health worldwide.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/pharmacy.png\" alt=\"Online Pharmacy\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Online Pharmacy</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Online pharmacies offer convenient, accessible medication services, improving healthcare delivery, saving time, and enhancing patient compliance globally.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/appointment-request.png\" alt=\"Appointment Scheduling\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Appointment Scheduling</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Appointment scheduling streamlines healthcare access, reduces wait times, improves efficiency, enhances patient satisfaction, and supports organized medical practices.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-pink-50 to-pink-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-pink-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/medical-team.png\" alt=\"Collaboration\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Collaboration</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Our platform allows better collaboration and coordination of staff and patient hence increasing productivity and efficiency.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-indigo-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/efficiency.png\" alt=\"Productivity Tracking\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Productivity Tracking</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Tracks efficiency and output, but risks burnout and care quality if not balanced with context and compassion.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"group\">\r\n              <div className=\"bg-gradient-to-br from-emerald-50 to-emerald-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-emerald-200/50 text-center\">\r\n                <div className=\"w-20 h-20 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\">\r\n                  <img src=\"/icons/paperless.png\" alt=\"Paperless Program\" className=\"w-10 h-10 object-contain filter brightness-0 invert\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Paperless Program</h3>\r\n                <p className=\"text-gray-600 leading-relaxed\">\r\n                  Our platform allows hospitals to move from traditional paper ways to digitized paperless program.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Billing Management Section */}\r\n      <section className=\"py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <Billing />\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\">\r\n            <div className=\"col-span-1 md:col-span-2\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3\">\r\n                  <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold\">HEALTHCARE Portal</h3>\r\n              </div>\r\n              <p className=\"text-gray-300 mb-6 max-w-md leading-relaxed\">\r\n                Your trusted healthcare management platform providing comprehensive digital solutions for modern medical care and patient management.\r\n              </p>\r\n              <div className=\"flex space-x-4\">\r\n                <div className=\"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\">\r\n                  <span className=\"text-sm\">📧</span>\r\n                </div>\r\n                <div className=\"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\">\r\n                  <span className=\"text-sm\">📱</span>\r\n                </div>\r\n                <div className=\"w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer\">\r\n                  <span className=\"text-sm\">🌐</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\r\n              <ul className=\"space-y-2\">\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">About Us</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Services</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Doctors</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Contact</a></li>\r\n              </ul>\r\n            </div>\r\n\r\n            <div>\r\n              <h4 className=\"text-lg font-semibold mb-4\">Support</h4>\r\n              <ul className=\"space-y-2\">\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Help Center</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Privacy Policy</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Terms of Service</a></li>\r\n                <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Emergency: 112</a></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"border-t border-gray-700 pt-8 text-center\">\r\n            <p className=\"text-gray-400\">\r\n              &copy; 2025 HealthCarePro. All rights reserved. | Terms & Privacy\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,IAAIA,CAAA,EAAG;EACd,oBACED,OAAA;IAAKE,SAAS,EAAC,qDAAqD;IAAAC,QAAA,gBAElEH,OAAA;MACEE,SAAS,EAAC,mFAAmF;MAC7FE,KAAK,EAAE;QACLC,eAAe,EAAE;MACnB,CAAE;MAAAF,QAAA,gBAEFH,OAAA;QAAKE,SAAS,EAAC;MAA8B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpDT,OAAA;QAAKE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DH,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBH,OAAA;YAAME,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAAC;UAE5G;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNT,OAAA;UAAIE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,GAAC,wBAErE,eAAAH,OAAA;YAAME,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACLT,OAAA;UAAGE,SAAS,EAAC,0EAA0E;UAAAC,QAAA,EAAC;QAExF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJT,OAAA;UAAKE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DH,OAAA;YAAQE,SAAS,EAAC,4NAA4N;YAAAC,QAAA,EAAC;UAE/O;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTT,OAAA;YAAQE,SAAS,EAAC,iJAAiJ;YAAAC,QAAA,EAAC;UAEpK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAME,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EAAC;UAE3G;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5FT,OAAA;YAAGE,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENT,OAAA;UAAKE,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnEH,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,kBAAkB;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAC1DH,OAAA;cAAKE,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/KH,OAAA;gBAAKE,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7KH,OAAA;kBAAKW,GAAG,EAAC,wBAAwB;kBAACC,GAAG,EAAC,iBAAiB;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjET,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnDH,OAAA;cAAKE,SAAS,EAAC,qKAAqK;cAAAC,QAAA,gBAClLH,OAAA;gBAAKE,SAAS,EAAC,kKAAkK;gBAAAC,QAAA,eAC/KH,OAAA;kBAAKW,GAAG,EAAC,wBAAwB;kBAACC,GAAG,EAAC,UAAU;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DT,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA4B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,SAAS;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjDH,OAAA;cAAKE,SAAS,EAAC,+JAA+J;cAAAC,QAAA,gBAC5KH,OAAA;gBAAKE,SAAS,EAAC,8JAA8J;gBAAAC,QAAA,eAC3KH,OAAA;kBAAKW,GAAG,EAAC,mBAAmB;kBAACC,GAAG,EAAC,QAAQ;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDT,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnDH,OAAA;cAAKE,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrLH,OAAA;gBAAKE,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjLH,OAAA;kBAAKW,GAAG,EAAC,iBAAiB;kBAACC,GAAG,EAAC,UAAU;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DT,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,oBAAoB;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAC5DH,OAAA;cAAKE,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrLH,OAAA;gBAAKE,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjLH,OAAA;kBAAKW,GAAG,EAAC,oBAAoB;kBAACC,GAAG,EAAC,mBAAmB;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnET,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA2B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,QAAQ;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAChDH,OAAA;cAAKE,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/KH,OAAA;gBAAKE,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7KH,OAAA;kBAAKW,GAAG,EAAC,iBAAiB;kBAACC,GAAG,EAAC,OAAO;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDT,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnDH,OAAA;cAAKE,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/KH,OAAA;gBAAKE,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,eAC7KH,OAAA;kBAAKW,GAAG,EAAC,0BAA0B;kBAACC,GAAG,EAAC,UAAU;kBAACV,SAAS,EAAC;gBAAmD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DT,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPT,OAAA,CAACF,IAAI;YAACY,EAAE,EAAC,OAAO;YAACR,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eAC/CH,OAAA;cAAKE,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBACrLH,OAAA;gBAAKE,SAAS,EAAC,oKAAoK;gBAAAC,QAAA,eACjLH,OAAA;kBAAKE,SAAS,EAAC,oBAAoB;kBAACW,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,GAAG;kBAACC,OAAO,EAAC,WAAW;kBAAAb,QAAA,eACvGH,OAAA;oBAAMiB,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAA2I;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjET,OAAA;gBAAGE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA4B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACvEH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAME,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EAAC;UAE5I;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAsB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7FT,OAAA;YAAGE,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENT,OAAA;UAAKE,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnEH,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCH,OAAA;cAAKE,SAAS,EAAC,+IAA+I;cAAAC,QAAA,gBAC5JH,OAAA;gBAAKE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAC5CH,OAAA;kBACEW,GAAG,EAAC,6CAA6C;kBACjDC,GAAG,EAAC,mBAAmB;kBACvBV,SAAS,EAAC;gBAAoF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACFT,OAAA;kBAAKE,SAAS,EAAC;gBAA6E;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnGT,OAAA;kBAAKE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,eACpCH,OAAA;oBAAME,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eACNT,OAAA;kBAAKE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDH,OAAA;oBAAME,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKE,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBH,OAAA;kBAAIE,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvHT,OAAA;kBAAGE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAkD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxFT,OAAA;kBAAKE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDH,OAAA;oBAAME,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DT,OAAA;oBAAKE,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,eACxHH,OAAA;sBAAKE,SAAS,EAAC,6CAA6C;sBAACW,IAAI,EAAC,cAAc;sBAACG,OAAO,EAAC,WAAW;sBAAAb,QAAA,eAClGH,OAAA;wBAAMoB,QAAQ,EAAC,SAAS;wBAACD,CAAC,EAAC,0IAA0I;wBAACE,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCH,OAAA;cAAKE,SAAS,EAAC,+IAA+I;cAAAC,QAAA,gBAC5JH,OAAA;gBAAKE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAC5CH,OAAA;kBACEW,GAAG,EAAC,gDAAgD;kBACpDC,GAAG,EAAC,YAAY;kBAChBV,SAAS,EAAC;gBAAoF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACFT,OAAA;kBAAKE,SAAS,EAAC;gBAA6E;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnGT,OAAA;kBAAKE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,eACpCH,OAAA;oBAAME,SAAS,EAAC,iEAAiE;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACNT,OAAA;kBAAKE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDH,OAAA;oBAAME,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKE,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBH,OAAA;kBAAIE,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChHT,OAAA;kBAAGE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAA6C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnFT,OAAA;kBAAKE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDH,OAAA;oBAAME,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DT,OAAA;oBAAKE,SAAS,EAAC,6GAA6G;oBAAAC,QAAA,eAC1HH,OAAA;sBAAKE,SAAS,EAAC,8CAA8C;sBAACW,IAAI,EAAC,cAAc;sBAACG,OAAO,EAAC,WAAW;sBAAAb,QAAA,eACnGH,OAAA;wBAAMoB,QAAQ,EAAC,SAAS;wBAACD,CAAC,EAAC,0IAA0I;wBAACE,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCH,OAAA;cAAKE,SAAS,EAAC,+IAA+I;cAAAC,QAAA,gBAC5JH,OAAA;gBAAKE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAC5CH,OAAA;kBACEW,GAAG,EAAC,mDAAmD;kBACvDC,GAAG,EAAC,kBAAkB;kBACtBV,SAAS,EAAC;gBAAoF;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACFT,OAAA;kBAAKE,SAAS,EAAC;gBAA6E;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnGT,OAAA;kBAAKE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,eACpCH,OAAA;oBAAME,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpG,CAAC,eACNT,OAAA;kBAAKE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDH,OAAA;oBAAME,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKE,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBH,OAAA;kBAAIE,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtHT,OAAA;kBAAGE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAkD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACxFT,OAAA;kBAAKE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDH,OAAA;oBAAME,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DT,OAAA;oBAAKE,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,eAC5HH,OAAA;sBAAKE,SAAS,EAAC,+CAA+C;sBAACW,IAAI,EAAC,cAAc;sBAACG,OAAO,EAAC,WAAW;sBAAAb,QAAA,eACpGH,OAAA;wBAAMoB,QAAQ,EAAC,SAAS;wBAACD,CAAC,EAAC,0IAA0I;wBAACE,QAAQ,EAAC;sBAAS;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAME,SAAS,EAAC,6HAA6H;YAAAC,QAAA,EAAC;UAE9I;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/ET,OAAA;YAAGE,SAAS,EAAC,+DAA+D;YAAAC,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENT,OAAA;UAAKE,SAAS,EAAC,sDAAsD;UAAAC,QAAA,gBACnEH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,8KAA8K;cAAAC,QAAA,gBAC3LH,OAAA;gBAAKE,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,eACtLH,OAAA;kBAAKW,GAAG,EAAC,4BAA4B;kBAACC,GAAG,EAAC,iBAAiB;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,iLAAiL;cAAAC,QAAA,gBAC9LH,OAAA;gBAAKE,SAAS,EAAC,2KAA2K;gBAAAC,QAAA,eACxLH,OAAA;kBAAKW,GAAG,EAAC,oBAAoB;kBAACC,GAAG,EAAC,QAAQ;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,oLAAoL;cAAAC,QAAA,gBACjMH,OAAA;gBAAKE,SAAS,EAAC,6KAA6K;gBAAAC,QAAA,eAC1LH,OAAA;kBAAKW,GAAG,EAAC,0BAA0B;kBAACC,GAAG,EAAC,uBAAuB;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/ET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,oLAAoL;cAAAC,QAAA,gBACjMH,OAAA;gBAAKE,SAAS,EAAC,6KAA6K;gBAAAC,QAAA,eAC1LH,OAAA;kBAAKW,GAAG,EAAC,qBAAqB;kBAACC,GAAG,EAAC,iBAAiB;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,8KAA8K;cAAAC,QAAA,gBAC3LH,OAAA;gBAAKE,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,eACtLH,OAAA;kBAAKW,GAAG,EAAC,gCAAgC;kBAACC,GAAG,EAAC,wBAAwB;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtI,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFT,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,8KAA8K;cAAAC,QAAA,gBAC3LH,OAAA;gBAAKE,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,eACtLH,OAAA;kBAAKW,GAAG,EAAC,yBAAyB;kBAACC,GAAG,EAAC,eAAe;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,oLAAoL;cAAAC,QAAA,gBACjMH,OAAA;gBAAKE,SAAS,EAAC,6KAA6K;gBAAAC,QAAA,eAC1LH,OAAA;kBAAKW,GAAG,EAAC,uBAAuB;kBAACC,GAAG,EAAC,uBAAuB;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5H,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/ET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,uLAAuL;cAAAC,QAAA,gBACpMH,OAAA;gBAAKE,SAAS,EAAC,+KAA+K;gBAAAC,QAAA,eAC5LH,OAAA;kBAAKW,GAAG,EAAC,sBAAsB;kBAACC,GAAG,EAAC,mBAAmB;kBAACV,SAAS,EAAC;gBAAqD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ET,OAAA;gBAAGE,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACvEH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCH,OAAA,CAACsB,OAAO;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAAQE,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAClFH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAKE,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDH,OAAA;YAAKE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCH,OAAA;cAAKE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCH,OAAA;gBAAKE,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,eACrHH,OAAA;kBAAKE,SAAS,EAAC,oBAAoB;kBAACW,IAAI,EAAC,cAAc;kBAACG,OAAO,EAAC,WAAW;kBAAAb,QAAA,eACzEH,OAAA;oBAAMoB,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,qLAAqL;oBAACE,QAAQ,EAAC;kBAAS;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAIE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNT,OAAA;cAAGE,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJT,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BH,OAAA;gBAAKE,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,eACrIH,OAAA;kBAAME,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNT,OAAA;gBAAKE,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,eACrIH,OAAA;kBAAME,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNT,OAAA;gBAAKE,SAAS,EAAC,wHAAwH;gBAAAC,QAAA,eACrIH,OAAA;kBAAME,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENT,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DT,OAAA;cAAIE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBH,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9FT,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9FT,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FT,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENT,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAIE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDT,OAAA;cAAIE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBH,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGT,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpGT,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtGT,OAAA;gBAAAG,QAAA,eAAIH,OAAA;kBAAGuB,IAAI,EAAC,GAAG;kBAACrB,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENT,OAAA;UAAKE,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxDH,OAAA;YAAGE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACe,EAAA,GAvaQvB,IAAI;AAyab,eAAeA,IAAI;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
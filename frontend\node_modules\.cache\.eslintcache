[{"C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx": "4", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx": "5", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx": "6", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx": "7", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx": "8", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx": "9", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx": "10", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx": "11", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx": "12", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx": "13", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx": "14", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx": "15", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx": "16", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx": "17", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx": "18", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx": "19", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx": "20", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx": "21", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx": "22", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx": "23", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingForm.jsx": "24"}, {"size": 535, "mtime": 1747922952389, "results": "25", "hashOfConfig": "26"}, {"size": 19430, "mtime": 1749632173268, "results": "27", "hashOfConfig": "26"}, {"size": 362, "mtime": 1747922953071, "results": "28", "hashOfConfig": "26"}, {"size": 30386, "mtime": 1749631648315, "results": "29", "hashOfConfig": "26"}, {"size": 17390, "mtime": 1748251195090, "results": "30", "hashOfConfig": "26"}, {"size": 13771, "mtime": 1748251298811, "results": "31", "hashOfConfig": "26"}, {"size": 30818, "mtime": 1748265227619, "results": "32", "hashOfConfig": "26"}, {"size": 42181, "mtime": 1748725421880, "results": "33", "hashOfConfig": "26"}, {"size": 30709, "mtime": 1748272681020, "results": "34", "hashOfConfig": "26"}, {"size": 23474, "mtime": 1748712959569, "results": "35", "hashOfConfig": "26"}, {"size": 21102, "mtime": 1748338142433, "results": "36", "hashOfConfig": "26"}, {"size": 31971, "mtime": 1748631188267, "results": "37", "hashOfConfig": "26"}, {"size": 30067, "mtime": 1748630180954, "results": "38", "hashOfConfig": "26"}, {"size": 39141, "mtime": 1748336567706, "results": "39", "hashOfConfig": "26"}, {"size": 24095, "mtime": 1748427457298, "results": "40", "hashOfConfig": "26"}, {"size": 5671, "mtime": 1749635460947, "results": "41", "hashOfConfig": "26"}, {"size": 3959, "mtime": 1749636129216, "results": "42", "hashOfConfig": "26"}, {"size": 6790, "mtime": 1748766718790, "results": "43", "hashOfConfig": "26"}, {"size": 141106, "mtime": 1749631173170, "results": "44", "hashOfConfig": "26"}, {"size": 20352, "mtime": 1749026176261, "results": "45", "hashOfConfig": "26"}, {"size": 32655, "mtime": 1749632794077, "results": "46", "hashOfConfig": "26"}, {"size": 11421, "mtime": 1749545947834, "results": "47", "hashOfConfig": "26"}, {"size": 16876, "mtime": 1749633513567, "results": "48", "hashOfConfig": "26"}, {"size": 20396, "mtime": 1749633489921, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1soy7zt", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx", ["122", "123", "124", "125", "126", "127", "128", "129"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx", ["130", "131", "132", "133", "134", "135", "136", "137"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx", ["138", "139", "140", "141", "142", "143", "144", "145"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx", ["146", "147", "148", "149", "150", "151", "152", "153", "154"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx", ["155"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx", ["156"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx", ["157"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx", ["158"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx", ["159", "160", "161", "162"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx", ["163"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx", ["164"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingForm.jsx", [], [], {"ruleId": "165", "severity": 1, "message": "166", "line": 404, "column": 21, "nodeType": "167", "endLine": 404, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 405, "column": 21, "nodeType": "167", "endLine": 405, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 406, "column": 21, "nodeType": "167", "endLine": 406, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 407, "column": 21, "nodeType": "167", "endLine": 407, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 414, "column": 21, "nodeType": "167", "endLine": 414, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 415, "column": 21, "nodeType": "167", "endLine": 415, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 416, "column": 21, "nodeType": "167", "endLine": 416, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 417, "column": 21, "nodeType": "167", "endLine": 417, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 266, "column": 21, "nodeType": "167", "endLine": 266, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 267, "column": 21, "nodeType": "167", "endLine": 267, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 268, "column": 21, "nodeType": "167", "endLine": 268, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 269, "column": 21, "nodeType": "167", "endLine": 269, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 276, "column": 21, "nodeType": "167", "endLine": 276, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 277, "column": 21, "nodeType": "167", "endLine": 277, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 278, "column": 21, "nodeType": "167", "endLine": 278, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 279, "column": 21, "nodeType": "167", "endLine": 279, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 212, "column": 21, "nodeType": "167", "endLine": 212, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 213, "column": 21, "nodeType": "167", "endLine": 213, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 214, "column": 21, "nodeType": "167", "endLine": 214, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 215, "column": 21, "nodeType": "167", "endLine": 215, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 222, "column": 21, "nodeType": "167", "endLine": 222, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 223, "column": 21, "nodeType": "167", "endLine": 223, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 224, "column": 21, "nodeType": "167", "endLine": 224, "endColumn": 94}, {"ruleId": "165", "severity": 1, "message": "166", "line": 225, "column": 21, "nodeType": "167", "endLine": 225, "endColumn": 94}, {"ruleId": "168", "severity": 1, "message": "169", "line": 7, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 7, "endColumn": 22}, {"ruleId": "165", "severity": 1, "message": "166", "line": 435, "column": 21, "nodeType": "167", "endLine": 435, "endColumn": 80}, {"ruleId": "165", "severity": 1, "message": "166", "line": 436, "column": 21, "nodeType": "167", "endLine": 436, "endColumn": 80}, {"ruleId": "165", "severity": 1, "message": "166", "line": 437, "column": 21, "nodeType": "167", "endLine": 437, "endColumn": 80}, {"ruleId": "165", "severity": 1, "message": "166", "line": 438, "column": 21, "nodeType": "167", "endLine": 438, "endColumn": 80}, {"ruleId": "165", "severity": 1, "message": "166", "line": 439, "column": 21, "nodeType": "167", "endLine": 439, "endColumn": 80}, {"ruleId": "165", "severity": 1, "message": "166", "line": 473, "column": 15, "nodeType": "167", "endLine": 473, "endColumn": 74}, {"ruleId": "165", "severity": 1, "message": "166", "line": 474, "column": 15, "nodeType": "167", "endLine": 474, "endColumn": 74}, {"ruleId": "165", "severity": 1, "message": "166", "line": 475, "column": 15, "nodeType": "167", "endLine": 475, "endColumn": 74}, {"ruleId": "172", "severity": 1, "message": "173", "line": 32, "column": 6, "nodeType": "174", "endLine": 32, "endColumn": 23, "suggestions": "175"}, {"ruleId": "168", "severity": 1, "message": "176", "line": 18, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 18, "endColumn": 22}, {"ruleId": "168", "severity": 1, "message": "177", "line": 9, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 9, "endColumn": 18}, {"ruleId": "172", "severity": 1, "message": "178", "line": 74, "column": 6, "nodeType": "174", "endLine": 74, "endColumn": 8, "suggestions": "179"}, {"ruleId": "168", "severity": 1, "message": "180", "line": 115, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 115, "endColumn": 20}, {"ruleId": "168", "severity": 1, "message": "181", "line": 133, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 133, "endColumn": 23}, {"ruleId": "168", "severity": 1, "message": "182", "line": 141, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 141, "endColumn": 23}, {"ruleId": "168", "severity": 1, "message": "183", "line": 241, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 241, "endColumn": 15}, {"ruleId": "168", "severity": 1, "message": "184", "line": 218, "column": 9, "nodeType": "170", "messageId": "171", "endLine": 218, "endColumn": 21}, {"ruleId": "168", "severity": 1, "message": "185", "line": 8, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 8, "endColumn": 18}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'showCheckout' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchMessages', 'fetchUnreadCount', and 'fetchUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["186"], "'selectedRoom' is assigned a value but never used.", "'showForm' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'SESSION_TIMEOUT'. Either include it or remove the dependency array.", ["187"], "'addBillItem' is assigned a value but never used.", "'removeBillItem' is assigned a value but never used.", "'updateBillItem' is assigned a value but never used.", "'totals' is assigned a value but never used.", "'printInvoice' is assigned a value but never used.", "'patients' is assigned a value but never used.", {"desc": "188", "fix": "189"}, {"desc": "190", "fix": "191"}, "Update the dependencies array to be: [user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", {"range": "192", "text": "193"}, "Update the dependencies array to be: [SESSION_TIMEOUT]", {"range": "194", "text": "195"}, [916, 933], "[user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", [2462, 2464], "[SESSION_TIMEOUT]"]
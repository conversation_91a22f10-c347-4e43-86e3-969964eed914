[{"C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx": "4", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx": "5", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx": "6", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx": "7", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx": "8", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx": "9", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx": "10", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx": "11", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx": "12", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx": "13", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx": "14", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx": "15", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx": "16", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx": "17", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx": "18", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx": "19", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx": "20", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx": "21", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx": "22", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx": "23", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingForm.jsx": "24", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PatientPortal.jsx": "25"}, {"size": 651, "mtime": 1749732545518, "results": "26", "hashOfConfig": "27"}, {"size": 22203, "mtime": 1749724093954, "results": "28", "hashOfConfig": "27"}, {"size": 362, "mtime": 1747922953071, "results": "29", "hashOfConfig": "27"}, {"size": 30386, "mtime": 1749631648315, "results": "30", "hashOfConfig": "27"}, {"size": 17390, "mtime": 1748251195090, "results": "31", "hashOfConfig": "27"}, {"size": 13771, "mtime": 1748251298811, "results": "32", "hashOfConfig": "27"}, {"size": 30818, "mtime": 1748265227619, "results": "33", "hashOfConfig": "27"}, {"size": 42181, "mtime": 1748725421880, "results": "34", "hashOfConfig": "27"}, {"size": 30709, "mtime": 1748272681020, "results": "35", "hashOfConfig": "27"}, {"size": 23474, "mtime": 1748712959569, "results": "36", "hashOfConfig": "27"}, {"size": 21102, "mtime": 1748338142433, "results": "37", "hashOfConfig": "27"}, {"size": 31971, "mtime": 1748631188267, "results": "38", "hashOfConfig": "27"}, {"size": 30067, "mtime": 1748630180954, "results": "39", "hashOfConfig": "27"}, {"size": 39141, "mtime": 1748336567706, "results": "40", "hashOfConfig": "27"}, {"size": 24095, "mtime": 1748427457298, "results": "41", "hashOfConfig": "27"}, {"size": 5673, "mtime": 1749732001525, "results": "42", "hashOfConfig": "27"}, {"size": 5954, "mtime": 1749732599998, "results": "43", "hashOfConfig": "27"}, {"size": 6790, "mtime": 1748766718790, "results": "44", "hashOfConfig": "27"}, {"size": 139033, "mtime": 1749731056831, "results": "45", "hashOfConfig": "27"}, {"size": 20352, "mtime": 1749026176261, "results": "46", "hashOfConfig": "27"}, {"size": 32655, "mtime": 1749632794077, "results": "47", "hashOfConfig": "27"}, {"size": 11421, "mtime": 1749545947834, "results": "48", "hashOfConfig": "27"}, {"size": 16876, "mtime": 1749633513567, "results": "49", "hashOfConfig": "27"}, {"size": 20396, "mtime": 1749633489921, "results": "50", "hashOfConfig": "27"}, {"size": 19743, "mtime": 1749724155528, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1soy7zt", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx", ["127", "128", "129", "130", "131", "132", "133", "134"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx", ["135", "136", "137", "138", "139", "140", "141", "142"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx", ["143", "144", "145", "146", "147", "148", "149", "150"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx", ["151", "152", "153", "154", "155", "156", "157", "158", "159"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx", ["160"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx", ["161"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx", ["162"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx", ["163", "164", "165", "166"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx", ["167"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx", ["168"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PatientPortal.jsx", ["169", "170", "171", "172", "173", "174", "175", "176"], [], {"ruleId": "177", "severity": 1, "message": "178", "line": 404, "column": 21, "nodeType": "179", "endLine": 404, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 405, "column": 21, "nodeType": "179", "endLine": 405, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 406, "column": 21, "nodeType": "179", "endLine": 406, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 407, "column": 21, "nodeType": "179", "endLine": 407, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 414, "column": 21, "nodeType": "179", "endLine": 414, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 415, "column": 21, "nodeType": "179", "endLine": 415, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 416, "column": 21, "nodeType": "179", "endLine": 416, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 417, "column": 21, "nodeType": "179", "endLine": 417, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 266, "column": 21, "nodeType": "179", "endLine": 266, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 267, "column": 21, "nodeType": "179", "endLine": 267, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 268, "column": 21, "nodeType": "179", "endLine": 268, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 269, "column": 21, "nodeType": "179", "endLine": 269, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 276, "column": 21, "nodeType": "179", "endLine": 276, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 277, "column": 21, "nodeType": "179", "endLine": 277, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 278, "column": 21, "nodeType": "179", "endLine": 278, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 279, "column": 21, "nodeType": "179", "endLine": 279, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 212, "column": 21, "nodeType": "179", "endLine": 212, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 213, "column": 21, "nodeType": "179", "endLine": 213, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 214, "column": 21, "nodeType": "179", "endLine": 214, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 215, "column": 21, "nodeType": "179", "endLine": 215, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 222, "column": 21, "nodeType": "179", "endLine": 222, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 223, "column": 21, "nodeType": "179", "endLine": 223, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 224, "column": 21, "nodeType": "179", "endLine": 224, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 225, "column": 21, "nodeType": "179", "endLine": 225, "endColumn": 94}, {"ruleId": "180", "severity": 1, "message": "181", "line": 7, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 7, "endColumn": 22}, {"ruleId": "177", "severity": 1, "message": "178", "line": 435, "column": 21, "nodeType": "179", "endLine": 435, "endColumn": 80}, {"ruleId": "177", "severity": 1, "message": "178", "line": 436, "column": 21, "nodeType": "179", "endLine": 436, "endColumn": 80}, {"ruleId": "177", "severity": 1, "message": "178", "line": 437, "column": 21, "nodeType": "179", "endLine": 437, "endColumn": 80}, {"ruleId": "177", "severity": 1, "message": "178", "line": 438, "column": 21, "nodeType": "179", "endLine": 438, "endColumn": 80}, {"ruleId": "177", "severity": 1, "message": "178", "line": 439, "column": 21, "nodeType": "179", "endLine": 439, "endColumn": 80}, {"ruleId": "177", "severity": 1, "message": "178", "line": 473, "column": 15, "nodeType": "179", "endLine": 473, "endColumn": 74}, {"ruleId": "177", "severity": 1, "message": "178", "line": 474, "column": 15, "nodeType": "179", "endLine": 474, "endColumn": 74}, {"ruleId": "177", "severity": 1, "message": "178", "line": 475, "column": 15, "nodeType": "179", "endLine": 475, "endColumn": 74}, {"ruleId": "184", "severity": 1, "message": "185", "line": 32, "column": 6, "nodeType": "186", "endLine": 32, "endColumn": 23, "suggestions": "187"}, {"ruleId": "180", "severity": 1, "message": "188", "line": 18, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 18, "endColumn": 22}, {"ruleId": "180", "severity": 1, "message": "189", "line": 9, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 9, "endColumn": 18}, {"ruleId": "180", "severity": 1, "message": "190", "line": 115, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 115, "endColumn": 20}, {"ruleId": "180", "severity": 1, "message": "191", "line": 133, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 133, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "192", "line": 141, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 141, "endColumn": 23}, {"ruleId": "180", "severity": 1, "message": "193", "line": 241, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 241, "endColumn": 15}, {"ruleId": "180", "severity": 1, "message": "194", "line": 218, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 218, "endColumn": 21}, {"ruleId": "180", "severity": 1, "message": "195", "line": 8, "column": 10, "nodeType": "182", "messageId": "183", "endLine": 8, "endColumn": 18}, {"ruleId": "184", "severity": 1, "message": "196", "line": 25, "column": 6, "nodeType": "186", "endLine": 25, "endColumn": 12, "suggestions": "197"}, {"ruleId": "180", "severity": 1, "message": "198", "line": 67, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 67, "endColumn": 18}, {"ruleId": "180", "severity": 1, "message": "199", "line": 86, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 86, "endColumn": 27}, {"ruleId": "180", "severity": 1, "message": "200", "line": 99, "column": 9, "nodeType": "182", "messageId": "183", "endLine": 99, "endColumn": 18}, {"ruleId": "177", "severity": 1, "message": "178", "line": 334, "column": 21, "nodeType": "179", "endLine": 334, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 335, "column": 21, "nodeType": "179", "endLine": 335, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 336, "column": 21, "nodeType": "179", "endLine": 336, "endColumn": 94}, {"ruleId": "177", "severity": 1, "message": "178", "line": 337, "column": 21, "nodeType": "179", "endLine": 337, "endColumn": 94}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'showCheckout' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchMessages', 'fetchUnreadCount', and 'fetchUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["201"], "'selectedRoom' is assigned a value but never used.", "'showForm' is assigned a value but never used.", "'addBillItem' is assigned a value but never used.", "'removeBillItem' is assigned a value but never used.", "'updateBillItem' is assigned a value but never used.", "'totals' is assigned a value but never used.", "'printInvoice' is assigned a value but never used.", "'patients' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAppointments' and 'fetchExamReports'. Either include them or remove the dependency array.", ["202"], "'addToCart' is assigned a value but never used.", "'updateCartQuantity' is assigned a value but never used.", "'cartTotal' is assigned a value but never used.", {"desc": "203", "fix": "204"}, {"desc": "205", "fix": "206"}, "Update the dependencies array to be: [user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", {"range": "207", "text": "208"}, "Update the dependencies array to be: [fetchAppointments, fetchExamReports, user]", {"range": "209", "text": "210"}, [916, 933], "[user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", [845, 851], "[fetchAppointments, fetchExamReports, user]"]
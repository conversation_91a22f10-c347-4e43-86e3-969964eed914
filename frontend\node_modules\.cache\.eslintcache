[{"C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx": "4", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx": "5", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx": "6", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx": "7", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx": "8", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx": "9", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx": "10", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx": "11", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx": "12", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx": "13", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx": "14", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx": "15", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx": "16", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx": "17", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx": "18", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx": "19", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx": "20", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx": "21", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx": "22", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx": "23", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingForm.jsx": "24", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PatientPortal.jsx": "25", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicMedicalRecords.jsx": "26", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicPharmacy.jsx": "27", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicAppointment.jsx": "28", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicExamResults.jsx": "29"}, {"size": 651, "mtime": 1749732545518, "results": "30", "hashOfConfig": "31"}, {"size": 23394, "mtime": 1749738880700, "results": "32", "hashOfConfig": "31"}, {"size": 362, "mtime": 1747922953071, "results": "33", "hashOfConfig": "31"}, {"size": 30386, "mtime": 1749631648315, "results": "34", "hashOfConfig": "31"}, {"size": 17390, "mtime": 1748251195090, "results": "35", "hashOfConfig": "31"}, {"size": 13771, "mtime": 1748251298811, "results": "36", "hashOfConfig": "31"}, {"size": 30818, "mtime": 1748265227619, "results": "37", "hashOfConfig": "31"}, {"size": 42181, "mtime": 1748725421880, "results": "38", "hashOfConfig": "31"}, {"size": 30709, "mtime": 1748272681020, "results": "39", "hashOfConfig": "31"}, {"size": 23474, "mtime": 1748712959569, "results": "40", "hashOfConfig": "31"}, {"size": 21102, "mtime": 1748338142433, "results": "41", "hashOfConfig": "31"}, {"size": 31971, "mtime": 1748631188267, "results": "42", "hashOfConfig": "31"}, {"size": 30067, "mtime": 1748630180954, "results": "43", "hashOfConfig": "31"}, {"size": 39141, "mtime": 1748336567706, "results": "44", "hashOfConfig": "31"}, {"size": 24095, "mtime": 1748427457298, "results": "45", "hashOfConfig": "31"}, {"size": 5673, "mtime": 1749732001525, "results": "46", "hashOfConfig": "31"}, {"size": 5954, "mtime": 1749732599998, "results": "47", "hashOfConfig": "31"}, {"size": 6790, "mtime": 1748766718790, "results": "48", "hashOfConfig": "31"}, {"size": 139033, "mtime": 1749731056831, "results": "49", "hashOfConfig": "31"}, {"size": 20352, "mtime": 1749026176261, "results": "50", "hashOfConfig": "31"}, {"size": 32655, "mtime": 1749632794077, "results": "51", "hashOfConfig": "31"}, {"size": 11421, "mtime": 1749545947834, "results": "52", "hashOfConfig": "31"}, {"size": 16876, "mtime": 1749633513567, "results": "53", "hashOfConfig": "31"}, {"size": 20396, "mtime": 1749633489921, "results": "54", "hashOfConfig": "31"}, {"size": 15073, "mtime": 1749738333317, "results": "55", "hashOfConfig": "31"}, {"size": 40386, "mtime": 1749740419550, "results": "56", "hashOfConfig": "31"}, {"size": 14692, "mtime": 1749739352779, "results": "57", "hashOfConfig": "31"}, {"size": 14015, "mtime": 1749739242590, "results": "58", "hashOfConfig": "31"}, {"size": 14660, "mtime": 1749739163239, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1soy7zt", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js", ["147"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx", ["148", "149", "150", "151", "152", "153", "154", "155"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx", ["156", "157", "158", "159", "160", "161", "162", "163"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx", ["164", "165", "166", "167", "168", "169", "170", "171"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx", ["172", "173", "174", "175", "176", "177", "178", "179", "180"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx", ["181"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx", ["182"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx", ["183"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx", ["184", "185", "186", "187"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx", ["188"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx", ["189"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PatientPortal.jsx", ["190", "191", "192", "193"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicMedicalRecords.jsx", ["194"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicPharmacy.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicAppointment.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\PublicExamResults.jsx", [], [], {"ruleId": "195", "severity": 1, "message": "196", "line": 54, "column": 9, "nodeType": "197", "messageId": "198", "endLine": 54, "endColumn": 24}, {"ruleId": "199", "severity": 1, "message": "200", "line": 404, "column": 21, "nodeType": "201", "endLine": 404, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 405, "column": 21, "nodeType": "201", "endLine": 405, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 406, "column": 21, "nodeType": "201", "endLine": 406, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 407, "column": 21, "nodeType": "201", "endLine": 407, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 414, "column": 21, "nodeType": "201", "endLine": 414, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 415, "column": 21, "nodeType": "201", "endLine": 415, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 416, "column": 21, "nodeType": "201", "endLine": 416, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 417, "column": 21, "nodeType": "201", "endLine": 417, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 266, "column": 21, "nodeType": "201", "endLine": 266, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 267, "column": 21, "nodeType": "201", "endLine": 267, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 268, "column": 21, "nodeType": "201", "endLine": 268, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 269, "column": 21, "nodeType": "201", "endLine": 269, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 276, "column": 21, "nodeType": "201", "endLine": 276, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 277, "column": 21, "nodeType": "201", "endLine": 277, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 278, "column": 21, "nodeType": "201", "endLine": 278, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 279, "column": 21, "nodeType": "201", "endLine": 279, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 212, "column": 21, "nodeType": "201", "endLine": 212, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 213, "column": 21, "nodeType": "201", "endLine": 213, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 214, "column": 21, "nodeType": "201", "endLine": 214, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 215, "column": 21, "nodeType": "201", "endLine": 215, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 222, "column": 21, "nodeType": "201", "endLine": 222, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 223, "column": 21, "nodeType": "201", "endLine": 223, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 224, "column": 21, "nodeType": "201", "endLine": 224, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 225, "column": 21, "nodeType": "201", "endLine": 225, "endColumn": 94}, {"ruleId": "195", "severity": 1, "message": "202", "line": 7, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 7, "endColumn": 22}, {"ruleId": "199", "severity": 1, "message": "200", "line": 435, "column": 21, "nodeType": "201", "endLine": 435, "endColumn": 80}, {"ruleId": "199", "severity": 1, "message": "200", "line": 436, "column": 21, "nodeType": "201", "endLine": 436, "endColumn": 80}, {"ruleId": "199", "severity": 1, "message": "200", "line": 437, "column": 21, "nodeType": "201", "endLine": 437, "endColumn": 80}, {"ruleId": "199", "severity": 1, "message": "200", "line": 438, "column": 21, "nodeType": "201", "endLine": 438, "endColumn": 80}, {"ruleId": "199", "severity": 1, "message": "200", "line": 439, "column": 21, "nodeType": "201", "endLine": 439, "endColumn": 80}, {"ruleId": "199", "severity": 1, "message": "200", "line": 473, "column": 15, "nodeType": "201", "endLine": 473, "endColumn": 74}, {"ruleId": "199", "severity": 1, "message": "200", "line": 474, "column": 15, "nodeType": "201", "endLine": 474, "endColumn": 74}, {"ruleId": "199", "severity": 1, "message": "200", "line": 475, "column": 15, "nodeType": "201", "endLine": 475, "endColumn": 74}, {"ruleId": "203", "severity": 1, "message": "204", "line": 32, "column": 6, "nodeType": "205", "endLine": 32, "endColumn": 23, "suggestions": "206"}, {"ruleId": "195", "severity": 1, "message": "207", "line": 18, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 18, "endColumn": 22}, {"ruleId": "195", "severity": 1, "message": "208", "line": 9, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 9, "endColumn": 18}, {"ruleId": "195", "severity": 1, "message": "209", "line": 115, "column": 9, "nodeType": "197", "messageId": "198", "endLine": 115, "endColumn": 20}, {"ruleId": "195", "severity": 1, "message": "210", "line": 133, "column": 9, "nodeType": "197", "messageId": "198", "endLine": 133, "endColumn": 23}, {"ruleId": "195", "severity": 1, "message": "211", "line": 141, "column": 9, "nodeType": "197", "messageId": "198", "endLine": 141, "endColumn": 23}, {"ruleId": "195", "severity": 1, "message": "212", "line": 241, "column": 9, "nodeType": "197", "messageId": "198", "endLine": 241, "endColumn": 15}, {"ruleId": "195", "severity": 1, "message": "213", "line": 218, "column": 9, "nodeType": "197", "messageId": "198", "endLine": 218, "endColumn": 21}, {"ruleId": "195", "severity": 1, "message": "214", "line": 8, "column": 10, "nodeType": "197", "messageId": "198", "endLine": 8, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "200", "line": 221, "column": 21, "nodeType": "201", "endLine": 221, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 222, "column": 21, "nodeType": "201", "endLine": 222, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 223, "column": 21, "nodeType": "201", "endLine": 223, "endColumn": 94}, {"ruleId": "199", "severity": 1, "message": "200", "line": 224, "column": 21, "nodeType": "201", "endLine": 224, "endColumn": 94}, {"ruleId": null, "fatal": true, "severity": 2, "message": "215", "line": 804, "column": 3, "nodeType": null}, "no-unused-vars", "'isPatientPortal' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'showCheckout' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchMessages', 'fetchUnreadCount', and 'fetchUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["216"], "'selectedRoom' is assigned a value but never used.", "'showForm' is assigned a value but never used.", "'addBillItem' is assigned a value but never used.", "'removeBillItem' is assigned a value but never used.", "'updateBillItem' is assigned a value but never used.", "'totals' is assigned a value but never used.", "'printInvoice' is assigned a value but never used.", "'patients' is assigned a value but never used.", "Parsing error: Unexpected token, expected \"}\" (804:3)", {"desc": "217", "fix": "218"}, "Update the dependencies array to be: [user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", {"range": "219", "text": "220"}, [916, 933], "[user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]"]
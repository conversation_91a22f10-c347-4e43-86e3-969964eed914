[{"C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx": "4", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx": "5", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx": "6", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx": "7", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx": "8", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx": "9", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx": "10", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx": "11", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx": "12", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx": "13", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx": "14", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx": "15", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx": "16", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx": "17", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx": "18", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx": "19", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx": "20", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx": "21", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx": "22", "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx": "23"}, {"size": 535, "mtime": 1747922952389, "results": "24", "hashOfConfig": "25"}, {"size": 19098, "mtime": 1749544998945, "results": "26", "hashOfConfig": "25"}, {"size": 362, "mtime": 1747922953071, "results": "27", "hashOfConfig": "25"}, {"size": 30386, "mtime": 1749631648315, "results": "28", "hashOfConfig": "25"}, {"size": 17390, "mtime": 1748251195090, "results": "29", "hashOfConfig": "25"}, {"size": 13771, "mtime": 1748251298811, "results": "30", "hashOfConfig": "25"}, {"size": 30818, "mtime": 1748265227619, "results": "31", "hashOfConfig": "25"}, {"size": 42181, "mtime": 1748725421880, "results": "32", "hashOfConfig": "25"}, {"size": 30709, "mtime": 1748272681020, "results": "33", "hashOfConfig": "25"}, {"size": 23474, "mtime": 1748712959569, "results": "34", "hashOfConfig": "25"}, {"size": 21102, "mtime": 1748338142433, "results": "35", "hashOfConfig": "25"}, {"size": 31971, "mtime": 1748631188267, "results": "36", "hashOfConfig": "25"}, {"size": 30067, "mtime": 1748630180954, "results": "37", "hashOfConfig": "25"}, {"size": 39141, "mtime": 1748336567706, "results": "38", "hashOfConfig": "25"}, {"size": 24095, "mtime": 1748427457298, "results": "39", "hashOfConfig": "25"}, {"size": 5626, "mtime": 1748766707686, "results": "40", "hashOfConfig": "25"}, {"size": 2147, "mtime": 1748638448579, "results": "41", "hashOfConfig": "25"}, {"size": 6790, "mtime": 1748766718790, "results": "42", "hashOfConfig": "25"}, {"size": 141106, "mtime": 1749631173170, "results": "43", "hashOfConfig": "25"}, {"size": 20352, "mtime": 1749026176261, "results": "44", "hashOfConfig": "25"}, {"size": 32531, "mtime": 1749545822057, "results": "45", "hashOfConfig": "25"}, {"size": 11421, "mtime": 1749545947834, "results": "46", "hashOfConfig": "25"}, {"size": 14787, "mtime": 1749546013154, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1soy7zt", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Home.jsx", ["117", "118", "119", "120", "121", "122", "123", "124"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Help.jsx", ["125", "126", "127", "128", "129", "130", "131", "132"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\About.jsx", ["133", "134", "135", "136", "137", "138", "139", "140"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Pharmacy.jsx", ["141", "142", "143", "144", "145", "146", "147", "148", "149"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Patients.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Exams.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Messages.jsx", ["150"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Doctor.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\HospitalTransfer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\MedicalRecords.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Room.jsx", ["151"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\Appointment.jsx", ["152"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Login.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\BillingSection.jsx", ["153", "154", "155", "156"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\Billing.jsx", ["157"], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\components\\PrintInvoice.jsx", [], [], "C:\\Users\\<USER>\\Documents\\Healthcare\\frontend\\src\\pages\\BillingPage.jsx", ["158"], [], {"ruleId": "159", "severity": 1, "message": "160", "line": 404, "column": 21, "nodeType": "161", "endLine": 404, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 405, "column": 21, "nodeType": "161", "endLine": 405, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 406, "column": 21, "nodeType": "161", "endLine": 406, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 407, "column": 21, "nodeType": "161", "endLine": 407, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 414, "column": 21, "nodeType": "161", "endLine": 414, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 415, "column": 21, "nodeType": "161", "endLine": 415, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 416, "column": 21, "nodeType": "161", "endLine": 416, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 417, "column": 21, "nodeType": "161", "endLine": 417, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 266, "column": 21, "nodeType": "161", "endLine": 266, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 267, "column": 21, "nodeType": "161", "endLine": 267, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 268, "column": 21, "nodeType": "161", "endLine": 268, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 269, "column": 21, "nodeType": "161", "endLine": 269, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 276, "column": 21, "nodeType": "161", "endLine": 276, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 277, "column": 21, "nodeType": "161", "endLine": 277, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 278, "column": 21, "nodeType": "161", "endLine": 278, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 279, "column": 21, "nodeType": "161", "endLine": 279, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 212, "column": 21, "nodeType": "161", "endLine": 212, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 213, "column": 21, "nodeType": "161", "endLine": 213, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 214, "column": 21, "nodeType": "161", "endLine": 214, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 215, "column": 21, "nodeType": "161", "endLine": 215, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 222, "column": 21, "nodeType": "161", "endLine": 222, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 223, "column": 21, "nodeType": "161", "endLine": 223, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 224, "column": 21, "nodeType": "161", "endLine": 224, "endColumn": 94}, {"ruleId": "159", "severity": 1, "message": "160", "line": 225, "column": 21, "nodeType": "161", "endLine": 225, "endColumn": 94}, {"ruleId": "162", "severity": 1, "message": "163", "line": 7, "column": 10, "nodeType": "164", "messageId": "165", "endLine": 7, "endColumn": 22}, {"ruleId": "159", "severity": 1, "message": "160", "line": 435, "column": 21, "nodeType": "161", "endLine": 435, "endColumn": 80}, {"ruleId": "159", "severity": 1, "message": "160", "line": 436, "column": 21, "nodeType": "161", "endLine": 436, "endColumn": 80}, {"ruleId": "159", "severity": 1, "message": "160", "line": 437, "column": 21, "nodeType": "161", "endLine": 437, "endColumn": 80}, {"ruleId": "159", "severity": 1, "message": "160", "line": 438, "column": 21, "nodeType": "161", "endLine": 438, "endColumn": 80}, {"ruleId": "159", "severity": 1, "message": "160", "line": 439, "column": 21, "nodeType": "161", "endLine": 439, "endColumn": 80}, {"ruleId": "159", "severity": 1, "message": "160", "line": 473, "column": 15, "nodeType": "161", "endLine": 473, "endColumn": 74}, {"ruleId": "159", "severity": 1, "message": "160", "line": 474, "column": 15, "nodeType": "161", "endLine": 474, "endColumn": 74}, {"ruleId": "159", "severity": 1, "message": "160", "line": 475, "column": 15, "nodeType": "161", "endLine": 475, "endColumn": 74}, {"ruleId": "166", "severity": 1, "message": "167", "line": 32, "column": 6, "nodeType": "168", "endLine": 32, "endColumn": 23, "suggestions": "169"}, {"ruleId": "162", "severity": 1, "message": "170", "line": 18, "column": 10, "nodeType": "164", "messageId": "165", "endLine": 18, "endColumn": 22}, {"ruleId": "162", "severity": 1, "message": "171", "line": 9, "column": 10, "nodeType": "164", "messageId": "165", "endLine": 9, "endColumn": 18}, {"ruleId": "162", "severity": 1, "message": "172", "line": 115, "column": 9, "nodeType": "164", "messageId": "165", "endLine": 115, "endColumn": 20}, {"ruleId": "162", "severity": 1, "message": "173", "line": 133, "column": 9, "nodeType": "164", "messageId": "165", "endLine": 133, "endColumn": 23}, {"ruleId": "162", "severity": 1, "message": "174", "line": 141, "column": 9, "nodeType": "164", "messageId": "165", "endLine": 141, "endColumn": 23}, {"ruleId": "162", "severity": 1, "message": "175", "line": 241, "column": 9, "nodeType": "164", "messageId": "165", "endLine": 241, "endColumn": 15}, {"ruleId": "162", "severity": 1, "message": "176", "line": 214, "column": 9, "nodeType": "164", "messageId": "165", "endLine": 214, "endColumn": 21}, {"ruleId": "162", "severity": 1, "message": "177", "line": 7, "column": 10, "nodeType": "164", "messageId": "165", "endLine": 7, "endColumn": 18}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'showCheckout' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchMessages', 'fetchUnreadCount', and 'fetchUsers'. Either include them or remove the dependency array.", "ArrayExpression", ["178"], "'selectedRoom' is assigned a value but never used.", "'showForm' is assigned a value but never used.", "'addBillItem' is assigned a value but never used.", "'removeBillItem' is assigned a value but never used.", "'updateBillItem' is assigned a value but never used.", "'totals' is assigned a value but never used.", "'printInvoice' is assigned a value but never used.", "'patients' is assigned a value but never used.", {"desc": "179", "fix": "180"}, "Update the dependencies array to be: [user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]", {"range": "181", "text": "182"}, [916, 933], "[user, activeTab, fetchMessages, fetchUsers, fetchUnreadCount]"]
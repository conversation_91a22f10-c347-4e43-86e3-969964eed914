import { Link } from 'react-router-dom';
import Billing from '../components/Billing';

function Home() {
  return(
    <div className="bg-gradient-to-br from-gray-50 to-blue-50 font-sans">
      {/* Hero Section */}
      <section
        className="relative bg-cover bg-center text-white py-24 px-6 min-h-[600px] flex items-center"
        style={{
          backgroundImage: "url('/image/Screenshot 2025-04-21 200615.png')"
        }}
      >
        <div className="absolute inset-0 bg-black/40"></div>
        <div className="relative z-10 text-center max-w-5xl mx-auto">
          <div className="mb-6">
            <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4">
              🏥 Trusted Healthcare Platform
            </span>
          </div>
          <h1 className="text-4xl md:text-7xl font-extrabold mb-6 leading-tight">
            Quality healthcare for
            <span className="text-yellow-400 block">community</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
            Experience seamless healthcare management with our comprehensive digital platform designed for modern medical care.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900 font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
              Explore Our Services
            </button>
            <button className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 border border-white/30">
              Learn More
            </button>
          </div>
        </div>
      </section>

      {/* Healthcare Categories */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-4">
              Our Services
            </span>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Healthcare Categories</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Access comprehensive healthcare services through our integrated digital platform
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            <Link to="/medical-records" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/website (1).png" alt="Medical Records" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Medical Records</h3>
                <p className="text-sm text-gray-600">Secure digital health records</p>
              </div>
            </Link>

            <Link to="/pharmacy" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/syringe (1).png" alt="Pharmacy" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Pharmacy</h3>
                <p className="text-sm text-gray-600">Online prescription services</p>
              </div>
            </Link>

            <Link to="/doctor" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-red-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/doctor.png" alt="Doctor" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Doctor</h3>
                <p className="text-sm text-gray-600">Connect with specialists</p>
              </div>
            </Link>

            <Link to="/patients" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/user.png" alt="Patients" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Patients</h3>
                <p className="text-sm text-gray-600">Patient management system</p>
              </div>
            </Link>

            <Link to="/hospital-transfer" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/website.png" alt="Hospital Transfer" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Hospital Transfer</h3>
                <p className="text-sm text-gray-600">Seamless facility transfers</p>
              </div>
            </Link>

            <Link to="/exams" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/exam.png" alt="Exams" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Exams</h3>
                <p className="text-sm text-gray-600">Medical examinations</p>
              </div>
            </Link>

            <Link to="/messages" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-pink-50 to-pink-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-pink-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/communication.png" alt="Messages" className="w-8 h-8 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Messages</h3>
                <p className="text-sm text-gray-600">Secure communication</p>
              </div>
            </Link>

            <Link to="/room" className="group cursor-pointer">
              <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-indigo-200/50">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">Room Management</h3>
                <p className="text-sm text-gray-600">Hospital room & bed tracking</p>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Top Services Section */}
      <section className="py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-full text-sm font-semibold mb-4">
              ⭐ Featured This Week
            </span>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Top Services this Week</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover our most popular healthcare services trusted by thousands of patients
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="group cursor-pointer">
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100">
                <div className="h-56 relative overflow-hidden">
                  <img
                    src="/image/jc-gellidon-UIp163xCV6w-unsplash.jpg"
                    alt="Emergency Surgery"
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">URGENT</span>
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <span className="text-sm font-medium opacity-90">Emergency</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">Emergency Surgery</h3>
                  <p className="text-gray-600 mb-4">24/7 urgent care and emergency surgical procedures</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Available Now</span>
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-500 transition-colors">
                      <svg className="w-4 h-4 text-red-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="group cursor-pointer">
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100">
                <div className="h-56 relative overflow-hidden">
                  <img
                    src="/image/bennett-tobias-YMpvL5eAtg0-unsplash.jpg"
                    alt="Child Care"
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold">PEDIATRIC</span>
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <span className="text-sm font-medium opacity-90">Children</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">Child Care</h3>
                  <p className="text-gray-600 mb-4">Specialized pediatric care from 12-1 PM daily</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">12:00 - 13:00</span>
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-500 transition-colors">
                      <svg className="w-4 h-4 text-blue-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="group cursor-pointer">
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-2 border border-gray-100">
                <div className="h-56 relative overflow-hidden">
                  <img
                    src="/image/towfiqu-barbhuiya-FsVEqeiOtPo-unsplash.jpg"
                    alt="Medical Supplies"
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">FREE DELIVERY</span>
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <span className="text-sm font-medium opacity-90">Medical</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">Medical Supplies</h3>
                  <p className="text-gray-600 mb-4">Essential medical supplies with free home delivery</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Free Delivery</span>
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-500 transition-colors">
                      <svg className="w-4 h-4 text-green-500 group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-semibold mb-4">
              🚀 Platform Features
            </span>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">Features</h1>
            <p className="text-xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Healthcare platform has multitude of features and modules to ensure all relevant activities and processes in hospitals are digitized.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="group">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-blue-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/patient-program.png" alt="Medical Records" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Medical Records</h3>
                <p className="text-gray-600 leading-relaxed">
                  Automated medical reports generated by data input of doctors per patient all in one place in a particular hospital.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-green-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/surgeon.png" alt="Doctor" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Doctor</h3>
                <p className="text-gray-600 leading-relaxed">
                  Each doctor has a profile with all necessary data with a dashboard of all activities and appointments.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-purple-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/immune-system.png" alt="Immunisation Services" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Immunisation Services</h3>
                <p className="text-gray-600 leading-relaxed">
                  Immunization protects individuals and communities from infectious diseases, reducing illness, saving lives, and promoting public health worldwide.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-orange-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/pharmacy.png" alt="Online Pharmacy" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Online Pharmacy</h3>
                <p className="text-gray-600 leading-relaxed">
                  Online pharmacies offer convenient, accessible medication services, improving healthcare delivery, saving time, and enhancing patient compliance globally.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-teal-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/appointment-request.png" alt="Appointment Scheduling" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Appointment Scheduling</h3>
                <p className="text-gray-600 leading-relaxed">
                  Appointment scheduling streamlines healthcare access, reduces wait times, improves efficiency, enhances patient satisfaction, and supports organized medical practices.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-pink-50 to-pink-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-pink-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/medical-team.png" alt="Collaboration" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Collaboration</h3>
                <p className="text-gray-600 leading-relaxed">
                  Our platform allows better collaboration and coordination of staff and patient hence increasing productivity and efficiency.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-indigo-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/efficiency.png" alt="Productivity Tracking" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Productivity Tracking</h3>
                <p className="text-gray-600 leading-relaxed">
                  Tracks efficiency and output, but risks burnout and care quality if not balanced with context and compassion.
                </p>
              </div>
            </div>

            <div className="group">
              <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-emerald-200/50 text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <img src="/icons/paperless.png" alt="Paperless Program" className="w-10 h-10 object-contain filter brightness-0 invert" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Paperless Program</h3>
                <p className="text-gray-600 leading-relaxed">
                  Our platform allows hospitals to move from traditional paper ways to digitized paperless program.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Billing Management Section */}
      <section className="py-20 px-6 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto">
          <Billing />
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-gray-900 to-blue-900 text-white py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8-1a1 1 0 00-1 1v4a1 1 0 002 0V9h2a1 1 0 100-2h-3z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold">HEALTHCARE Portal</h3>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Your trusted healthcare management platform providing comprehensive digital solutions for modern medical care and patient management.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer">
                  <span className="text-sm">📧</span>
                </div>
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer">
                  <span className="text-sm">📱</span>
                </div>
                <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors cursor-pointer">
                  <span className="text-sm">🌐</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Services</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Doctors</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Terms of Service</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Emergency: 112</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-gray-400">
              &copy; 2025 HealthCarePro. All rights reserved. | Terms & Privacy
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default Home;

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\pages\\\\BillingPage.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport PrintInvoice from '../components/PrintInvoice';\nimport Billing from '../components/Billing';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BillingPage = () => {\n  _s();\n  const [bills, setBills] = useState([]);\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedBill, setSelectedBill] = useState(null);\n  const [showPrintModal, setShowPrintModal] = useState(false);\n  const [showBillingForm, setShowBillingForm] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [statusFilter, setStatusFilter] = useState('All');\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch bills and patients on component mount\n  useEffect(() => {\n    fetchBills();\n    fetchPatients();\n  }, []);\n\n  // Fetch all bills\n  const fetchBills = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/billing`);\n      const data = await response.json();\n      if (data.success) {\n        setBills(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching bills:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch all patients\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n    }\n  };\n\n  // Handle print\n  const handlePrint = bill => {\n    setSelectedBill(bill);\n    setShowPrintModal(true);\n  };\n\n  // Filter bills based on search and status\n  const filteredBills = bills.filter(bill => {\n    const matchesSearch = bill.patientName.toLowerCase().includes(searchQuery.toLowerCase()) || bill.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) || bill.patientNationalId && bill.patientNationalId.includes(searchQuery);\n    const matchesStatus = statusFilter === 'All' || bill.paymentStatus === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  // Calculate statistics\n  const totalBills = bills.length;\n  const paidBills = bills.filter(bill => bill.paymentStatus === 'Paid').length;\n  const pendingBills = bills.filter(bill => bill.paymentStatus === 'Pending').length;\n  const totalRevenue = bills.reduce((sum, bill) => sum + parseFloat(bill.totalAmountToBePaid || bill.total), 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"flex items-center gap-2 text-sm text-gray-600 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"hover:text-blue-600\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"/\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600 font-medium\",\n                children: \"Hospital Billing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-bold text-gray-900 mb-2\",\n              children: \"Hospital Billing Management\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Comprehensive patient billing and financial management system\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowBillingForm(true),\n            className: \"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M12 4v16m8-8H4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), \"Generate New Bill\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-6 border border-blue-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Bills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-blue-600\",\n                children: totalBills\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-6 border border-green-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Paid Bills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-green-600\",\n                children: paidBills\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-green-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-6 border border-yellow-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Pending Bills\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-yellow-600\",\n                children: pendingBills\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-yellow-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-6 border border-purple-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Revenue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-purple-600\",\n                children: [totalRevenue.toFixed(0), \" RWF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-purple-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row gap-4 items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Search Bills\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search by patient name, bill number, or ID...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"absolute left-3 top-2.5 w-5 h-5 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Filter by Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: statusFilter,\n              onChange: e => setStatusFilter(e.target.value),\n              className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"All\",\n                children: \"All Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Paid\",\n                children: \"Paid\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Pending\",\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Partial\",\n                children: \"Partial\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Cancelled\",\n                children: \"Cancelled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: [\"Medical Bills (\", filteredBills.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-8 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Loading bills...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-blue-50 border-b border-blue-100\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Bill No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Patient Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Services\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Financial Summary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Payment Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"divide-y divide-gray-100\",\n              children: filteredBills.map(bill => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"hover:bg-blue-25 transition-colors\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-900\",\n                    children: bill.invoiceNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\"#\", bill.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: bill.patientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\"ID: \", bill.patientNationalId || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this), bill.patientPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: bill.patientPhone\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 47\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-900\",\n                    children: [bill.items.length, \" service(s)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [bill.items.slice(0, 2).map(item => item.description).join(', '), bill.items.length > 2 && '...']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-green-700\",\n                    children: [(bill.totalAmountToBePaid || bill.total).toFixed(2), \" RWF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\"Paid: \", (bill.amountPaid || 0).toFixed(2), \" RWF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-red-600\",\n                    children: [\"Due: \", ((bill.totalAmountToBePaid || bill.total) - (bill.amountPaid || 0)).toFixed(2), \" RWF\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-3 py-1 text-xs font-bold rounded-full border ${bill.paymentStatus === 'Paid' ? 'bg-green-50 text-green-700 border-green-200' : bill.paymentStatus === 'Pending' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' : bill.paymentStatus === 'Partial' ? 'bg-orange-50 text-orange-700 border-orange-200' : 'bg-red-50 text-red-700 border-red-200'}`,\n                    children: bill.paymentStatus\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 text-sm text-gray-600\",\n                  children: new Date(bill.createdAt).toLocaleDateString('en-GB')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handlePrint(bill),\n                    className: \"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-4 h-4\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this), \"Print\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this)]\n              }, bill.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), showPrintModal && selectedBill && /*#__PURE__*/_jsxDEV(PrintInvoice, {\n        bill: selectedBill,\n        onClose: () => {\n          setShowPrintModal(false);\n          setSelectedBill(null);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(BillingPage, \"GfGhRVW44MY1pi+duwRl7GxLARQ=\");\n_c = BillingPage;\nexport default BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");", "map": {"version": 3, "names": ["useState", "useEffect", "Link", "PrintInvoice", "Billing", "jsxDEV", "_jsxDEV", "BillingPage", "_s", "bills", "setBills", "patients", "setPatients", "loading", "setLoading", "selected<PERSON><PERSON>", "setSelectedBill", "showPrintModal", "setShowPrintModal", "showBillingForm", "setShowBillingForm", "searchQuery", "setSearch<PERSON>uery", "statusFilter", "setStatus<PERSON>ilter", "API_BASE_URL", "fetchBills", "fetchPatients", "response", "fetch", "data", "json", "success", "error", "console", "handlePrint", "bill", "filteredBills", "filter", "matchesSearch", "patientName", "toLowerCase", "includes", "invoiceNumber", "patientNationalId", "matchesStatus", "paymentStatus", "totalBills", "length", "paidBills", "pendingBills", "totalRevenue", "reduce", "sum", "parseFloat", "totalAmountToBePaid", "total", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "strokeWidth", "viewBox", "strokeLinecap", "strokeLinejoin", "d", "toFixed", "type", "placeholder", "value", "onChange", "e", "target", "map", "id", "patientPhone", "items", "slice", "item", "description", "join", "amountPaid", "Date", "createdAt", "toLocaleDateString", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/pages/BillingPage.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport PrintInvoice from '../components/PrintInvoice';\nimport Billing from '../components/Billing';\n\nconst BillingPage = () => {\n  const [bills, setBills] = useState([]);\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedBill, setSelectedBill] = useState(null);\n  const [showPrintModal, setShowPrintModal] = useState(false);\n  const [showBillingForm, setShowBillingForm] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [statusFilter, setStatusFilter] = useState('All');\n\n  const API_BASE_URL = 'http://localhost:5000/api';\n\n  // Fetch bills and patients on component mount\n  useEffect(() => {\n    fetchBills();\n    fetchPatients();\n  }, []);\n\n  // Fetch all bills\n  const fetchBills = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`${API_BASE_URL}/billing`);\n      const data = await response.json();\n      if (data.success) {\n        setBills(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching bills:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch all patients\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch(`${API_BASE_URL}/patients`);\n      const data = await response.json();\n      if (data.success) {\n        setPatients(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n    }\n  };\n\n  // Handle print\n  const handlePrint = (bill) => {\n    setSelectedBill(bill);\n    setShowPrintModal(true);\n  };\n\n  // Filter bills based on search and status\n  const filteredBills = bills.filter(bill => {\n    const matchesSearch = bill.patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         bill.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         (bill.patientNationalId && bill.patientNationalId.includes(searchQuery));\n    const matchesStatus = statusFilter === 'All' || bill.paymentStatus === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  // Calculate statistics\n  const totalBills = bills.length;\n  const paidBills = bills.filter(bill => bill.paymentStatus === 'Paid').length;\n  const pendingBills = bills.filter(bill => bill.paymentStatus === 'Pending').length;\n  const totalRevenue = bills.reduce((sum, bill) => sum + parseFloat(bill.totalAmountToBePaid || bill.total), 0);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <nav className=\"flex items-center gap-2 text-sm text-gray-600 mb-2\">\n                <Link to=\"/\" className=\"hover:text-blue-600\">Home</Link>\n                <span>/</span>\n                <span className=\"text-blue-600 font-medium\">Hospital Billing</span>\n              </nav>\n              <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Hospital Billing Management</h1>\n              <p className=\"text-gray-600\">Comprehensive patient billing and financial management system</p>\n            </div>\n            <button\n              onClick={() => setShowBillingForm(true)}\n              className=\"bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4v16m8-8H4\" />\n              </svg>\n              Generate New Bill\n            </button>\n          </div>\n        </div>\n\n        {/* Statistics Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-blue-100\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Bills</p>\n                <p className=\"text-3xl font-bold text-blue-600\">{totalBills}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-green-100\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Paid Bills</p>\n                <p className=\"text-3xl font-bold text-green-600\">{paidBills}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-yellow-100\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Pending Bills</p>\n                <p className=\"text-3xl font-bold text-yellow-600\">{pendingBills}</p>\n              </div>\n              <div className=\"w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-purple-100\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                <p className=\"text-3xl font-bold text-purple-600\">{totalRevenue.toFixed(0)} RWF</p>\n              </div>\n              <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters and Search */}\n        <div className=\"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\">\n          <div className=\"flex flex-col md:flex-row gap-4 items-center justify-between\">\n            <div className=\"flex-1 max-w-md\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Search Bills</label>\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search by patient name, bill number, or ID...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <svg className=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 21l-4.35-4.35M11 19a8 8 0 100-16 8 8 0 000 16z\" />\n                </svg>\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Filter by Status</label>\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"All\">All Status</option>\n                <option value=\"Paid\">Paid</option>\n                <option value=\"Pending\">Pending</option>\n                <option value=\"Partial\">Partial</option>\n                <option value=\"Cancelled\">Cancelled</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Bills Table */}\n        <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Medical Bills ({filteredBills.length})</h3>\n          </div>\n          \n          {loading ? (\n            <div className=\"p-8 text-center\">\n              <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n              <p className=\"mt-2 text-gray-600\">Loading bills...</p>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-blue-50 border-b border-blue-100\">\n                  <tr>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Bill No.</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Patient Details</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Services</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Financial Summary</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Payment Status</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Date</th>\n                    <th className=\"px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody className=\"divide-y divide-gray-100\">\n                  {filteredBills.map((bill) => (\n                    <tr key={bill.id} className=\"hover:bg-blue-25 transition-colors\">\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm font-bold text-blue-900\">{bill.invoiceNumber}</div>\n                        <div className=\"text-xs text-gray-500\">#{bill.id}</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{bill.patientName}</div>\n                        <div className=\"text-xs text-gray-500\">ID: {bill.patientNationalId || 'N/A'}</div>\n                        {bill.patientPhone && <div className=\"text-xs text-gray-500\">{bill.patientPhone}</div>}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-900\">{bill.items.length} service(s)</div>\n                        <div className=\"text-xs text-gray-500\">\n                          {bill.items.slice(0, 2).map(item => item.description).join(', ')}\n                          {bill.items.length > 2 && '...'}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm font-bold text-green-700\">{(bill.totalAmountToBePaid || bill.total).toFixed(2)} RWF</div>\n                        <div className=\"text-xs text-gray-500\">Paid: {(bill.amountPaid || 0).toFixed(2)} RWF</div>\n                        <div className=\"text-xs text-red-600\">Due: {((bill.totalAmountToBePaid || bill.total) - (bill.amountPaid || 0)).toFixed(2)} RWF</div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <span className={`inline-flex px-3 py-1 text-xs font-bold rounded-full border ${\n                          bill.paymentStatus === 'Paid' ? 'bg-green-50 text-green-700 border-green-200' :\n                          bill.paymentStatus === 'Pending' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :\n                          bill.paymentStatus === 'Partial' ? 'bg-orange-50 text-orange-700 border-orange-200' :\n                          'bg-red-50 text-red-700 border-red-200'\n                        }`}>\n                          {bill.paymentStatus}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 text-sm text-gray-600\">\n                        {new Date(bill.createdAt).toLocaleDateString('en-GB')}\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <button\n                          onClick={() => handlePrint(bill)}\n                          className=\"bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2\"\n                        >\n                          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\n                          </svg>\n                          Print\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n\n        {/* Print Invoice Modal */}\n        {showPrintModal && selectedBill && (\n          <PrintInvoice \n            bill={selectedBill} \n            onClose={() => {\n              setShowPrintModal(false);\n              setSelectedBill(null);\n            }} \n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default BillingPage;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,OAAO,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMyB,YAAY,GAAG,2BAA2B;;EAEhD;EACAxB,SAAS,CAAC,MAAM;IACdyB,UAAU,CAAC,CAAC;IACZC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,UAAU,CAAC;MACvD,MAAMK,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBtB,QAAQ,CAACoB,IAAI,CAACA,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMa,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,WAAW,CAAC;MACxD,MAAMK,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBpB,WAAW,CAACkB,IAAI,CAACA,IAAI,CAAC;MACxB;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAME,WAAW,GAAIC,IAAI,IAAK;IAC5BpB,eAAe,CAACoB,IAAI,CAAC;IACrBlB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMmB,aAAa,GAAG5B,KAAK,CAAC6B,MAAM,CAACF,IAAI,IAAI;IACzC,MAAMG,aAAa,GAAGH,IAAI,CAACI,WAAW,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,WAAW,CAACoB,WAAW,CAAC,CAAC,CAAC,IACnEL,IAAI,CAACO,aAAa,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrB,WAAW,CAACoB,WAAW,CAAC,CAAC,CAAC,IACnEL,IAAI,CAACQ,iBAAiB,IAAIR,IAAI,CAACQ,iBAAiB,CAACF,QAAQ,CAACrB,WAAW,CAAE;IAC7F,MAAMwB,aAAa,GAAGtB,YAAY,KAAK,KAAK,IAAIa,IAAI,CAACU,aAAa,KAAKvB,YAAY;IACnF,OAAOgB,aAAa,IAAIM,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAME,UAAU,GAAGtC,KAAK,CAACuC,MAAM;EAC/B,MAAMC,SAAS,GAAGxC,KAAK,CAAC6B,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACU,aAAa,KAAK,MAAM,CAAC,CAACE,MAAM;EAC5E,MAAME,YAAY,GAAGzC,KAAK,CAAC6B,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACU,aAAa,KAAK,SAAS,CAAC,CAACE,MAAM;EAClF,MAAMG,YAAY,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,CAACC,GAAG,EAAEjB,IAAI,KAAKiB,GAAG,GAAGC,UAAU,CAAClB,IAAI,CAACmB,mBAAmB,IAAInB,IAAI,CAACoB,KAAK,CAAC,EAAE,CAAC,CAAC;EAE7G,oBACElD,OAAA;IAAKmD,SAAS,EAAC,wEAAwE;IAAAC,QAAA,eACrFpD,OAAA;MAAKmD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCpD,OAAA;QAAKmD,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBpD,OAAA;UAAKmD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpD,OAAA;YAAAoD,QAAA,gBACEpD,OAAA;cAAKmD,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBACjEpD,OAAA,CAACJ,IAAI;gBAACyD,EAAE,EAAC,GAAG;gBAACF,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDzD,OAAA;gBAAAoD,QAAA,EAAM;cAAC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdzD,OAAA;gBAAMmD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACNzD,OAAA;cAAImD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtFzD,OAAA;cAAGmD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eACNzD,OAAA;YACE0D,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAAC,IAAI,CAAE;YACxCqC,SAAS,EAAC,0OAA0O;YAAAC,QAAA,gBAEpPpD,OAAA;cAAKmD,SAAS,EAAC,SAAS;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,OAAO,EAAC,WAAW;cAAAV,QAAA,eAC5FpD,OAAA;gBAAM+D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAgB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,qBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKmD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDpD,OAAA;UAAKmD,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxEpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAGmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChEzD,OAAA;gBAAGmD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEX;cAAU;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNzD,OAAA;cAAKmD,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAChFpD,OAAA;gBAAKmD,SAAS,EAAC,uBAAuB;gBAACQ,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC1GpD,OAAA;kBAAM+D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAsH;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3K;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKmD,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eACzEpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAGmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/DzD,OAAA;gBAAGmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAET;cAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNzD,OAAA;cAAKmD,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eACjFpD,OAAA;gBAAKmD,SAAS,EAAC,wBAAwB;gBAACQ,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC3GpD,OAAA;kBAAM+D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA+C;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKmD,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAGmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClEzD,OAAA;gBAAGmD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAER;cAAY;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNzD,OAAA;cAAKmD,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFpD,OAAA;gBAAKmD,SAAS,EAAC,yBAAyB;gBAACQ,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC5GpD,OAAA;kBAAM+D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA6C;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAKmD,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAGmD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClEzD,OAAA;gBAAGmD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAEP,YAAY,CAACqB,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACNzD,OAAA;cAAKmD,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFpD,OAAA;gBAAKmD,SAAS,EAAC,yBAAyB;gBAACQ,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAC5GpD,OAAA;kBAAM+D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA2I;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKmD,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7EpD,OAAA;UAAKmD,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EpD,OAAA;YAAKmD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpD,OAAA;cAAOmD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpFzD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBpD,OAAA;gBACEmE,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,+CAA+C;gBAC3DC,KAAK,EAAEtD,WAAY;gBACnBuD,QAAQ,EAAGC,CAAC,IAAKvD,cAAc,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAChDlB,SAAS,EAAC;cAAoH;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC,eACFzD,OAAA;gBAAKmD,SAAS,EAAC,+CAA+C;gBAACQ,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,OAAO,EAAC,WAAW;gBAAAV,QAAA,eAClIpD,OAAA;kBAAM+D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAoD;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzD,OAAA;YAAAoD,QAAA,gBACEpD,OAAA;cAAOmD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxFzD,OAAA;cACEqE,KAAK,EAAEpD,YAAa;cACpBqD,QAAQ,EAAGC,CAAC,IAAKrD,eAAe,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACjDlB,SAAS,EAAC,uGAAuG;cAAAC,QAAA,gBAEjHpD,OAAA;gBAAQqE,KAAK,EAAC,KAAK;gBAAAjB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCzD,OAAA;gBAAQqE,KAAK,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCzD,OAAA;gBAAQqE,KAAK,EAAC,SAAS;gBAAAjB,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCzD,OAAA;gBAAQqE,KAAK,EAAC,SAAS;gBAAAjB,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCzD,OAAA;gBAAQqE,KAAK,EAAC,WAAW;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKmD,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpFpD,OAAA;UAAKmD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDpD,OAAA;YAAImD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,iBAAe,EAACrB,aAAa,CAACW,MAAM,EAAC,GAAC;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5F,CAAC,EAELlD,OAAO,gBACNP,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpD,OAAA;YAAKmD,SAAS,EAAC;UAA2E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGzD,OAAA;YAAGmD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,gBAENzD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BpD,OAAA;YAAOmD,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACvBpD,OAAA;cAAOmD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eACpDpD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/FzD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtGzD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/FzD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxGzD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrGzD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3FzD,OAAA;kBAAImD,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRzD,OAAA;cAAOmD,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACxCrB,aAAa,CAAC0C,GAAG,CAAE3C,IAAI,iBACtB9B,OAAA;gBAAkBmD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBAC9DpD,OAAA;kBAAImD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBpD,OAAA;oBAAKmD,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEtB,IAAI,CAACO;kBAAa;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3EzD,OAAA;oBAAKmD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,GAAC,EAACtB,IAAI,CAAC4C,EAAE;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACLzD,OAAA;kBAAImD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBpD,OAAA;oBAAKmD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEtB,IAAI,CAACI;kBAAW;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3EzD,OAAA;oBAAKmD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,MAAI,EAACtB,IAAI,CAACQ,iBAAiB,IAAI,KAAK;kBAAA;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACjF3B,IAAI,CAAC6C,YAAY,iBAAI3E,OAAA;oBAAKmD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEtB,IAAI,CAAC6C;kBAAY;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACLzD,OAAA;kBAAImD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBpD,OAAA;oBAAKmD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAEtB,IAAI,CAAC8C,KAAK,CAAClC,MAAM,EAAC,aAAW;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3EzD,OAAA;oBAAKmD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCtB,IAAI,CAAC8C,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,GAAG,CAACK,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAC/DlD,IAAI,CAAC8C,KAAK,CAAClC,MAAM,GAAG,CAAC,IAAI,KAAK;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLzD,OAAA;kBAAImD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACvBpD,OAAA;oBAAKmD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,GAAE,CAACtB,IAAI,CAACmB,mBAAmB,IAAInB,IAAI,CAACoB,KAAK,EAAEgB,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjHzD,OAAA;oBAAKmD,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,QAAM,EAAC,CAACtB,IAAI,CAACmD,UAAU,IAAI,CAAC,EAAEf,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1FzD,OAAA;oBAAKmD,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,GAAC,OAAK,EAAC,CAAC,CAACtB,IAAI,CAACmB,mBAAmB,IAAInB,IAAI,CAACoB,KAAK,KAAKpB,IAAI,CAACmD,UAAU,IAAI,CAAC,CAAC,EAAEf,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnI,CAAC,eACLzD,OAAA;kBAAImD,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvBpD,OAAA;oBAAMmD,SAAS,EAAE,+DACfrB,IAAI,CAACU,aAAa,KAAK,MAAM,GAAG,6CAA6C,GAC7EV,IAAI,CAACU,aAAa,KAAK,SAAS,GAAG,gDAAgD,GACnFV,IAAI,CAACU,aAAa,KAAK,SAAS,GAAG,gDAAgD,GACnF,uCAAuC,EACtC;oBAAAY,QAAA,EACAtB,IAAI,CAACU;kBAAa;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLzD,OAAA;kBAAImD,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C,IAAI8B,IAAI,CAACpD,IAAI,CAACqD,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACLzD,OAAA;kBAAImD,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACvBpD,OAAA;oBACE0D,OAAO,EAAEA,CAAA,KAAM7B,WAAW,CAACC,IAAI,CAAE;oBACjCqB,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1IpD,OAAA;sBAAKmD,SAAS,EAAC,SAAS;sBAACQ,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,OAAO,EAAC,WAAW;sBAAAV,QAAA,eAC5FpD,OAAA;wBAAM+D,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAA8K;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnO,CAAC,SAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GA7CE3B,IAAI,CAAC4C,EAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8CZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL9C,cAAc,IAAIF,YAAY,iBAC7BT,OAAA,CAACH,YAAY;QACXiC,IAAI,EAAErB,YAAa;QACnB4E,OAAO,EAAEA,CAAA,KAAM;UACbzE,iBAAiB,CAAC,KAAK,CAAC;UACxBF,eAAe,CAAC,IAAI,CAAC;QACvB;MAAE;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CA3RID,WAAW;AAAAqF,EAAA,GAAXrF,WAAW;AA6RjB,eAAeA,WAAW;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
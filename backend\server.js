const express = require('express');
const cors = require('cors');
const mysql = require('mysql');
const smsService = require('./services/smsService');
const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Database connection
const db = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: '',
  database: 'healthcare'
});

db.connect((err) => {
  if (err) {
    console.error('Error connecting to MySQL database:', err);
    return;
  }
  console.log('Connected to MySQL database: healthcare');
});

// Create patients table with migration support
const createPatientsTable = () => {
  // Create table only if it doesn't exist (preserve existing data)
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS patients (
      nationalId VARCHAR(20) PRIMARY KEY,
      firstName VARCHAR(100) NOT NULL,
      lastName VARCHAR(100) NOT NULL,
      email VARCHAR(255) UNIQUE NOT NULL,
      phone VARCHAR(20) NOT NULL,
      dateOfBirth DATE NOT NULL,
      gender ENUM('Male', 'Female', 'Other') DEFAULT NULL,
      address TEXT,
      emergencyContact VARCHAR(255),
      bloodType ENUM('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-') DEFAULT NULL,
      allergies TEXT,
      medicalHistory TEXT,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `;

  db.query(createTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating patients table:', err);
      return;
    }
    console.log('Patients table ready (created or already exists)');

    // Insert sample patients if table is empty
    insertSamplePatients();

    // Create other tables after patients table is ready
    createExamsTable();
    createRoomsTable();
    createRoomAssignmentsTable();
    createDoctorsTable();
    createAppointmentsTable();
    createPrescriptionsTable();
    createHospitalTransfersTable();
    createUsersTable();
    createMessagesTable();
    createRolePermissionsTable();
  });
};

// Insert sample patients
const insertSamplePatients = () => {
  const checkPatientsQuery = 'SELECT COUNT(*) as count FROM patients';

  db.query(checkPatientsQuery, (err, result) => {
    if (err) {
      console.error('Error checking patients data:', err);
      return;
    }

    if (result[0].count === 0) {
      const samplePatients = [
        ['**********123456', 'John', 'Doe', '<EMAIL>', '(555) 123-4567', '1985-06-15', 'Male', '123 Main St, City, State 12345', 'Jane Doe - (555) 987-6543', 'O+', 'Penicillin', 'Hypertension, Diabetes Type 2'],
        ['2345678901234567', 'Sarah', 'Johnson', '<EMAIL>', '(555) 234-5678', '1990-03-22', 'Female', '456 Oak Ave, City, State 12345', 'Mike Johnson - (555) 876-5432', 'A-', 'None', 'Asthma'],
        ['3456789012345678', 'Michael', 'Brown', '<EMAIL>', '(555) 345-6789', '1978-11-08', 'Male', '789 Pine St, City, State 12345', 'Lisa Brown - (555) 765-4321', 'B+', 'Shellfish', 'Arthritis'],
        ['4567890123456789', 'Emily', 'Davis', '<EMAIL>', '(*************', '1995-02-14', 'Female', '321 Elm St, City, State 12345', 'Robert Davis - (*************', 'AB-', 'Latex', 'None'],
        ['567890**********', 'David', 'Wilson', '<EMAIL>', '(*************', '1982-09-30', 'Male', '654 Maple Ave, City, State 12345', 'Jennifer Wilson - (*************', 'O-', 'Peanuts', 'High Blood Pressure']
      ];

      const insertQuery = `
        INSERT INTO patients
        (nationalId, firstName, lastName, email, phone, dateOfBirth, gender, address, emergencyContact, bloodType, allergies, medicalHistory)
        VALUES ?
      `;

      db.query(insertQuery, [samplePatients], (err, result) => {
        if (err) {
          console.error('Error inserting sample patients:', err);
          return;
        }
        console.log('Sample patients data inserted');
      });
    }
  });
};

// Create exams table
const createExamsTable = () => {
  const createExamsTableQuery = `
    CREATE TABLE IF NOT EXISTS exams (
      id INT AUTO_INCREMENT PRIMARY KEY,
      nationalId VARCHAR(20) NOT NULL,
      examType VARCHAR(100) NOT NULL,
      examDate DATE NOT NULL,
      results TEXT NOT NULL,
      notes TEXT,
      status ENUM('pending', 'completed', 'in-progress', 'cancelled') DEFAULT 'pending',
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (nationalId) REFERENCES patients(nationalId) ON DELETE CASCADE ON UPDATE CASCADE
    )
  `;

  db.query(createExamsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating exams table:', err);
      return;
    }
    console.log('Exams table created successfully');
  });
};

// Create rooms table
const createRoomsTable = () => {
  const createRoomsTableQuery = `
    CREATE TABLE IF NOT EXISTS rooms (
      id INT AUTO_INCREMENT PRIMARY KEY,
      roomNumber VARCHAR(20) NOT NULL UNIQUE,
      roomType ENUM('general', 'private', 'icu', 'emergency', 'surgery', 'maternity', 'pediatric') NOT NULL,
      department VARCHAR(100) NOT NULL,
      capacity INT NOT NULL DEFAULT 1,
      currentOccupancy INT NOT NULL DEFAULT 0,
      status ENUM('available', 'occupied', 'maintenance', 'cleaning', 'reserved') DEFAULT 'available',
      floor INT NOT NULL,
      amenities TEXT,
      dailyRate DECIMAL(10,2),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `;

  db.query(createRoomsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating rooms table:', err);
      return;
    }
    console.log('Rooms table created successfully');

    // Insert sample rooms if table is empty
    insertSampleRooms();
  });
};

// Create room assignments table
const createRoomAssignmentsTable = () => {
  const createAssignmentsTableQuery = `
    CREATE TABLE IF NOT EXISTS room_assignments (
      id INT AUTO_INCREMENT PRIMARY KEY,
      roomId INT NOT NULL,
      nationalId VARCHAR(20) NOT NULL,
      admissionDate DATETIME NOT NULL,
      expectedDischargeDate DATETIME,
      actualDischargeDate DATETIME,
      status ENUM('active', 'discharged', 'transferred') DEFAULT 'active',
      notes TEXT,
      assignedBy VARCHAR(100),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (roomId) REFERENCES rooms(id) ON DELETE CASCADE ON UPDATE CASCADE,
      FOREIGN KEY (nationalId) REFERENCES patients(nationalId) ON DELETE CASCADE ON UPDATE CASCADE
    )
  `;

  db.query(createAssignmentsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating room assignments table:', err);
      return;
    }
    console.log('Room assignments table created successfully');
  });
};

// Create doctors table
const createDoctorsTable = () => {
  const createDoctorsTableQuery = `
    CREATE TABLE IF NOT EXISTS doctors (
      id INT AUTO_INCREMENT PRIMARY KEY,
      doctorId VARCHAR(20) NOT NULL UNIQUE,
      firstName VARCHAR(100) NOT NULL,
      lastName VARCHAR(100) NOT NULL,
      email VARCHAR(255) UNIQUE NOT NULL,
      phone VARCHAR(20) NOT NULL,
      specialization VARCHAR(100) NOT NULL,
      department VARCHAR(100) NOT NULL,
      qualification VARCHAR(255) NOT NULL,
      experience INT NOT NULL,
      consultationFee DECIMAL(10,2) NOT NULL,
      availableFrom TIME NOT NULL,
      availableTo TIME NOT NULL,
      workingDays VARCHAR(50) NOT NULL,
      status ENUM('active', 'inactive', 'on_leave') DEFAULT 'active',
      profileImage VARCHAR(255),
      address TEXT,
      dateOfBirth DATE,
      gender ENUM('Male', 'Female', 'Other'),
      licenseNumber VARCHAR(50),
      bio TEXT,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `;

  db.query(createDoctorsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating doctors table:', err);
      return;
    }
    console.log('Doctors table created successfully');

    // Insert sample doctors if table is empty
    insertSampleDoctors();
  });
};

// Insert sample doctors
const insertSampleDoctors = () => {
  const checkDoctorsQuery = 'SELECT COUNT(*) as count FROM doctors';

  db.query(checkDoctorsQuery, (err, result) => {
    if (err) {
      console.error('Error checking doctors data:', err);
      return;
    }

    if (result[0].count === 0) {
      const sampleDoctors = [
        ['DOC001', 'Dr. Sarah', 'Johnson', '<EMAIL>', '+250788123456', 'Cardiology', 'Cardiology', 'MD, FACC', 15, 75000, '08:00:00', '17:00:00', 'Monday-Friday', 'active', null, '123 Medical Center, Kigali', '1978-03-15', 'Female', 'LIC001', 'Experienced cardiologist specializing in heart disease prevention and treatment.'],
        ['DOC002', 'Dr. Michael', 'Chen', '<EMAIL>', '+250788234567', 'Neurology', 'Neurology', 'MD, PhD', 12, 80000, '09:00:00', '18:00:00', 'Monday-Saturday', 'active', null, '456 Brain Center, Kigali', '1980-07-22', 'Male', 'LIC002', 'Neurologist with expertise in brain disorders and neurological conditions.'],
        ['DOC003', 'Dr. Emily', 'Williams', '<EMAIL>', '+250788345678', 'Pediatrics', 'Pediatrics', 'MD, FAAP', 10, 60000, '07:30:00', '16:30:00', 'Monday-Friday', 'active', null, '789 Children Hospital, Kigali', '1985-11-08', 'Female', 'LIC003', 'Pediatrician dedicated to providing comprehensive care for children.'],
        ['DOC004', 'Dr. James', 'Brown', '<EMAIL>', '+250788456789', 'Orthopedics', 'Orthopedics', 'MD, FAAOS', 18, 85000, '08:30:00', '17:30:00', 'Monday-Friday', 'active', null, '321 Bone Center, Kigali', '1975-01-30', 'Male', 'LIC004', 'Orthopedic surgeon specializing in bone and joint treatments.'],
        ['DOC005', 'Dr. Lisa', 'Davis', '<EMAIL>', '+250788567890', 'Dermatology', 'Dermatology', 'MD, FAAD', 8, 65000, '09:00:00', '16:00:00', 'Tuesday-Saturday', 'active', null, '654 Skin Clinic, Kigali', '1987-09-12', 'Female', 'LIC005', 'Dermatologist expert in skin conditions and cosmetic procedures.'],
        ['DOC006', 'Dr. Robert', 'Wilson', '<EMAIL>', '+250788678901', 'Emergency Medicine', 'Emergency', 'MD, FACEP', 14, 70000, '00:00:00', '23:59:59', 'Monday-Sunday', 'active', null, '987 Emergency Wing, Kigali', '1979-05-18', 'Male', 'LIC006', 'Emergency medicine physician available 24/7 for critical care.'],
        ['DOC007', 'Dr. Maria', 'Garcia', '<EMAIL>', '+250788789012', 'Obstetrics & Gynecology', 'Maternity', 'MD, FACOG', 11, 72000, '08:00:00', '16:00:00', 'Monday-Friday', 'active', null, '147 Maternity Ward, Kigali', '1982-12-03', 'Female', 'LIC007', 'OB/GYN specialist in womens health and maternity care.'],
        ['DOC008', 'Dr. David', 'Lee', '<EMAIL>', '+250788890123', 'Psychiatry', 'Mental Health', 'MD, MRCPsych', 9, 68000, '10:00:00', '18:00:00', 'Monday-Friday', 'active', null, '258 Mental Health Center, Kigali', '1984-04-25', 'Male', 'LIC008', 'Psychiatrist specializing in mental health and behavioral disorders.']
      ];

      const insertQuery = `
        INSERT INTO doctors
        (doctorId, firstName, lastName, email, phone, specialization, department, qualification, experience, consultationFee, availableFrom, availableTo, workingDays, status, profileImage, address, dateOfBirth, gender, licenseNumber, bio)
        VALUES ?
      `;

      db.query(insertQuery, [sampleDoctors], (err, result) => {
        if (err) {
          console.error('Error inserting sample doctors:', err);
          return;
        }
        console.log('Sample doctors data inserted');
      });
    }
  });
};

// Create appointments table
const createAppointmentsTable = () => {
  const createAppointmentsTableQuery = `
    CREATE TABLE IF NOT EXISTS appointments (
      id INT AUTO_INCREMENT PRIMARY KEY,
      appointmentId VARCHAR(20) NOT NULL UNIQUE,
      nationalId VARCHAR(20) NOT NULL,
      doctorId VARCHAR(20) NOT NULL,
      appointmentDate DATE NOT NULL,
      appointmentTime TIME NOT NULL,
      duration INT DEFAULT 30,
      appointmentType ENUM('consultation', 'follow_up', 'emergency', 'routine_checkup', 'surgery', 'diagnostic') DEFAULT 'consultation',
      status ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show') DEFAULT 'scheduled',
      reason TEXT,
      notes TEXT,
      symptoms TEXT,
      priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
      createdBy VARCHAR(100),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (nationalId) REFERENCES patients(nationalId) ON DELETE CASCADE,
      FOREIGN KEY (doctorId) REFERENCES doctors(doctorId) ON DELETE CASCADE,
      INDEX idx_appointment_date (appointmentDate),
      INDEX idx_national_id (nationalId),
      INDEX idx_doctor_id (doctorId),
      INDEX idx_status (status)
    )
  `;

  db.query(createAppointmentsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating appointments table:', err);
      return;
    }
    console.log('Appointments table created successfully');

    // Insert sample appointments if table is empty
    insertSampleAppointments();
  });
};

// Insert sample appointments
const insertSampleAppointments = () => {
  const checkAppointmentsQuery = 'SELECT COUNT(*) as count FROM appointments';

  db.query(checkAppointmentsQuery, (err, result) => {
    if (err) {
      console.error('Error checking appointments data:', err);
      return;
    }

    if (result[0].count === 0) {
      // First, get existing patients and doctors to create valid appointments
      const getPatientsQuery = 'SELECT nationalId FROM patients LIMIT 5';
      const getDoctorsQuery = 'SELECT doctorId FROM doctors LIMIT 5';

      db.query(getPatientsQuery, (err, patients) => {
        if (err) {
          console.error('Error fetching patients for sample appointments:', err);
          return;
        }

        if (patients.length === 0) {
          console.log('No patients found, skipping sample appointments');
          return;
        }

        db.query(getDoctorsQuery, (err, doctors) => {
          if (err) {
            console.error('Error fetching doctors for sample appointments:', err);
            return;
          }

          if (doctors.length === 0) {
            console.log('No doctors found, skipping sample appointments');
            return;
          }

          // Generate appointment IDs
          const generateAppointmentId = (index) => `APT${String(index + 1).padStart(4, '0')}`;

          // Create sample appointments using existing patient and doctor IDs
          const sampleAppointments = [];
          const appointmentTemplates = [
            ['consultation', 'scheduled', 'Regular checkup', 'Routine health examination', 'General wellness check', 'medium'],
            ['follow_up', 'confirmed', 'Follow-up consultation', 'Previous treatment review', 'Headache follow-up', 'medium'],
            ['routine_checkup', 'scheduled', 'Wellness visit', 'Annual health checkup', 'Preventive care', 'low'],
            ['consultation', 'confirmed', 'Medical consultation', 'Health evaluation', 'General health concerns', 'high'],
            ['consultation', 'scheduled', 'Health assessment', 'Medical examination', 'Health screening', 'medium']
          ];

          // Create appointments using available patients and doctors
          for (let i = 0; i < Math.min(5, patients.length, doctors.length); i++) {
            const appointmentDate = new Date();
            appointmentDate.setDate(appointmentDate.getDate() + i + 1); // Future dates
            const formattedDate = appointmentDate.toISOString().split('T')[0];

            const template = appointmentTemplates[i];
            sampleAppointments.push([
              generateAppointmentId(i),
              patients[i].nationalId,
              doctors[i % doctors.length].doctorId,
              formattedDate,
              `${9 + i}:00:00`, // Different times: 9:00, 10:00, 11:00, etc.
              30,
              template[0], // appointmentType
              template[1], // status
              template[2], // reason
              template[3], // notes
              template[4], // symptoms
              template[5], // priority
              'System'
            ]);
          }

          if (sampleAppointments.length > 0) {
            const insertQuery = `
              INSERT INTO appointments
              (appointmentId, nationalId, doctorId, appointmentDate, appointmentTime, duration, appointmentType, status, reason, notes, symptoms, priority, createdBy)
              VALUES ?
            `;

            db.query(insertQuery, [sampleAppointments], (err, result) => {
              if (err) {
                console.error('Error inserting sample appointments:', err);
                return;
              }
              console.log(`${sampleAppointments.length} sample appointments inserted successfully`);
            });
          }
        });
      });
    }
  });
};

// Create prescriptions table
const createPrescriptionsTable = () => {
  const createPrescriptionsTableQuery = `
    CREATE TABLE IF NOT EXISTS prescriptions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      prescriptionId VARCHAR(20) NOT NULL UNIQUE,
      nationalId VARCHAR(20) NOT NULL,
      doctorId VARCHAR(20) NOT NULL,
      prescriptionDate DATE NOT NULL,
      diagnosis TEXT NOT NULL,
      medications JSON NOT NULL,
      notes TEXT,
      status ENUM('active', 'completed', 'cancelled', 'expired') DEFAULT 'active',
      validUntil DATE,
      createdBy VARCHAR(100),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (nationalId) REFERENCES patients(nationalId) ON DELETE CASCADE,
      FOREIGN KEY (doctorId) REFERENCES doctors(doctorId) ON DELETE CASCADE,
      INDEX idx_prescription_date (prescriptionDate),
      INDEX idx_national_id (nationalId),
      INDEX idx_doctor_id (doctorId),
      INDEX idx_status (status)
    )
  `;

  db.query(createPrescriptionsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating prescriptions table:', err);
      return;
    }
    console.log('Prescriptions table created successfully');

    // Insert sample prescriptions if table is empty
    insertSamplePrescriptions();
  });
};

// Insert sample prescriptions
const insertSamplePrescriptions = () => {
  const checkPrescriptionsQuery = 'SELECT COUNT(*) as count FROM prescriptions';

  db.query(checkPrescriptionsQuery, (err, result) => {
    if (err) {
      console.error('Error checking prescriptions data:', err);
      return;
    }

    if (result[0].count === 0) {
      // Get existing patients and doctors to create valid prescriptions
      const getPatientsQuery = 'SELECT nationalId FROM patients LIMIT 3';
      const getDoctorsQuery = 'SELECT doctorId FROM doctors LIMIT 3';

      db.query(getPatientsQuery, (err, patients) => {
        if (err) {
          console.error('Error fetching patients for sample prescriptions:', err);
          return;
        }

        if (patients.length === 0) {
          console.log('No patients found, skipping sample prescriptions');
          return;
        }

        db.query(getDoctorsQuery, (err, doctors) => {
          if (err) {
            console.error('Error fetching doctors for sample prescriptions:', err);
            return;
          }

          if (doctors.length === 0) {
            console.log('No doctors found, skipping sample prescriptions');
            return;
          }

          // Generate prescription IDs
          const generatePrescriptionId = (index) => `RX${String(index + 1).padStart(4, '0')}`;

          // Create sample prescriptions using existing patient and doctor IDs
          const samplePrescriptions = [];
          const prescriptionTemplates = [
            {
              diagnosis: 'Hypertension',
              medications: [
                { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily', duration: '30 days', instructions: 'Take with food' },
                { name: 'Hydrochlorothiazide', dosage: '25mg', frequency: 'Once daily', duration: '30 days', instructions: 'Take in the morning' }
              ],
              notes: 'Monitor blood pressure regularly. Follow up in 4 weeks.'
            },
            {
              diagnosis: 'Type 2 Diabetes',
              medications: [
                { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily', duration: '90 days', instructions: 'Take with meals' },
                { name: 'Glipizide', dosage: '5mg', frequency: 'Once daily', duration: '90 days', instructions: 'Take before breakfast' }
              ],
              notes: 'Monitor blood glucose levels. Diet and exercise counseling provided.'
            },
            {
              diagnosis: 'Bacterial Infection',
              medications: [
                { name: 'Amoxicillin', dosage: '500mg', frequency: 'Three times daily', duration: '10 days', instructions: 'Complete full course' }
              ],
              notes: 'Take with food to reduce stomach upset. Return if symptoms persist.'
            }
          ];

          // Create prescriptions using available patients and doctors
          for (let i = 0; i < Math.min(3, patients.length, doctors.length); i++) {
            const prescriptionDate = new Date();
            prescriptionDate.setDate(prescriptionDate.getDate() - i); // Recent dates
            const formattedDate = prescriptionDate.toISOString().split('T')[0];

            const validUntil = new Date();
            validUntil.setDate(validUntil.getDate() + 90); // Valid for 90 days
            const formattedValidUntil = validUntil.toISOString().split('T')[0];

            const template = prescriptionTemplates[i];
            samplePrescriptions.push([
              generatePrescriptionId(i),
              patients[i].nationalId,
              doctors[i % doctors.length].doctorId,
              formattedDate,
              template.diagnosis,
              JSON.stringify(template.medications),
              template.notes,
              'active',
              formattedValidUntil,
              'System'
            ]);
          }

          if (samplePrescriptions.length > 0) {
            const insertQuery = `
              INSERT INTO prescriptions
              (prescriptionId, nationalId, doctorId, prescriptionDate, diagnosis, medications, notes, status, validUntil, createdBy)
              VALUES ?
            `;

            db.query(insertQuery, [samplePrescriptions], (err, result) => {
              if (err) {
                console.error('Error inserting sample prescriptions:', err);
                return;
              }
              console.log(`${samplePrescriptions.length} sample prescriptions inserted successfully`);
            });
          }
        });
      });
    }
  });
};

// Create hospital transfers table
const createHospitalTransfersTable = () => {
  const createHospitalTransfersTableQuery = `
    CREATE TABLE IF NOT EXISTS hospital_transfers (
      id INT AUTO_INCREMENT PRIMARY KEY,
      transferId VARCHAR(20) NOT NULL UNIQUE,
      nationalId VARCHAR(20) NOT NULL,
      currentHospital VARCHAR(255) NOT NULL,
      targetHospital VARCHAR(255) NOT NULL,
      transferReason TEXT NOT NULL,
      medicalCondition TEXT NOT NULL,
      urgencyLevel ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
      requestedBy VARCHAR(100) NOT NULL,
      requestedByRole ENUM('doctor', 'nurse', 'admin', 'family') DEFAULT 'doctor',
      contactNumber VARCHAR(20) NOT NULL,
      preferredDate DATE,
      preferredTime TIME,
      transportMethod ENUM('ambulance', 'helicopter', 'private_vehicle', 'public_transport') DEFAULT 'ambulance',
      specialRequirements TEXT,
      patientConsent BOOLEAN DEFAULT FALSE,
      familyNotified BOOLEAN DEFAULT FALSE,
      status ENUM('pending', 'approved', 'rejected', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
      adminNotes TEXT,
      approvedBy VARCHAR(100),
      approvedAt TIMESTAMP NULL,
      rejectedBy VARCHAR(100),
      rejectedAt TIMESTAMP NULL,
      rejectionReason TEXT,
      completedAt TIMESTAMP NULL,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (nationalId) REFERENCES patients(nationalId) ON DELETE CASCADE,
      INDEX idx_transfer_status (status),
      INDEX idx_national_id (nationalId),
      INDEX idx_urgency_level (urgencyLevel),
      INDEX idx_created_at (createdAt)
    )
  `;

  db.query(createHospitalTransfersTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating hospital transfers table:', err);
      return;
    }
    console.log('Hospital transfers table created successfully');

    // Insert sample transfer requests if table is empty
    insertSampleTransferRequests();
  });
};

// Insert sample transfer requests
const insertSampleTransferRequests = () => {
  const checkTransfersQuery = 'SELECT COUNT(*) as count FROM hospital_transfers';

  db.query(checkTransfersQuery, (err, result) => {
    if (err) {
      console.error('Error checking transfer requests data:', err);
      return;
    }

    if (result[0].count === 0) {
      // Get existing patients to create valid transfer requests
      const getPatientsQuery = 'SELECT nationalId FROM patients LIMIT 3';

      db.query(getPatientsQuery, (err, patients) => {
        if (err) {
          console.error('Error fetching patients for sample transfers:', err);
          return;
        }

        if (patients.length === 0) {
          console.log('No patients found, skipping sample transfer requests');
          return;
        }

        // Generate transfer IDs
        const generateTransferId = (index) => `TRF${String(index + 1).padStart(4, '0')}`;

        // Create sample transfer requests using existing patient IDs
        const sampleTransfers = [];
        const transferTemplates = [
          {
            currentHospital: 'City General Hospital',
            targetHospital: 'Metropolitan Medical Center',
            transferReason: 'Specialized cardiac surgery required',
            medicalCondition: 'Acute myocardial infarction requiring immediate intervention',
            urgencyLevel: 'critical',
            requestedBy: 'Dr. Sarah Johnson',
            requestedByRole: 'doctor',
            contactNumber: '(555) 123-4567',
            transportMethod: 'ambulance',
            specialRequirements: 'Cardiac monitoring equipment, oxygen support',
            patientConsent: true,
            familyNotified: true,
            status: 'pending'
          },
          {
            currentHospital: 'Regional Medical Center',
            targetHospital: 'Children\'s Hospital',
            transferReason: 'Pediatric specialist consultation needed',
            medicalCondition: 'Complex pediatric neurological condition',
            urgencyLevel: 'high',
            requestedBy: 'Dr. Michael Brown',
            requestedByRole: 'doctor',
            contactNumber: '(555) 234-5678',
            transportMethod: 'ambulance',
            specialRequirements: 'Pediatric life support equipment',
            patientConsent: true,
            familyNotified: true,
            status: 'approved'
          },
          {
            currentHospital: 'Community Health Center',
            targetHospital: 'University Medical Center',
            transferReason: 'Advanced diagnostic imaging required',
            medicalCondition: 'Suspected brain tumor requiring MRI and specialist evaluation',
            urgencyLevel: 'medium',
            requestedBy: 'Dr. Emily Davis',
            requestedByRole: 'doctor',
            contactNumber: '(555) 345-6789',
            transportMethod: 'private_vehicle',
            specialRequirements: 'Patient mobility assistance',
            patientConsent: true,
            familyNotified: false,
            status: 'pending'
          }
        ];

        // Create transfer requests using available patients
        for (let i = 0; i < Math.min(3, patients.length); i++) {
          const preferredDate = new Date();
          preferredDate.setDate(preferredDate.getDate() + i + 1); // Future dates
          const formattedDate = preferredDate.toISOString().split('T')[0];

          const template = transferTemplates[i];
          sampleTransfers.push([
            generateTransferId(i),
            patients[i].nationalId,
            template.currentHospital,
            template.targetHospital,
            template.transferReason,
            template.medicalCondition,
            template.urgencyLevel,
            template.requestedBy,
            template.requestedByRole,
            template.contactNumber,
            formattedDate,
            '14:00:00', // 2 PM
            template.transportMethod,
            template.specialRequirements,
            template.patientConsent,
            template.familyNotified,
            template.status
          ]);
        }

        if (sampleTransfers.length > 0) {
          const insertQuery = `
            INSERT INTO hospital_transfers
            (transferId, nationalId, currentHospital, targetHospital, transferReason, medicalCondition, urgencyLevel, requestedBy, requestedByRole, contactNumber, preferredDate, preferredTime, transportMethod, specialRequirements, patientConsent, familyNotified, status)
            VALUES ?
          `;

          db.query(insertQuery, [sampleTransfers], (err, result) => {
            if (err) {
              console.error('Error inserting sample transfer requests:', err);
              return;
            }
            console.log(`${sampleTransfers.length} sample transfer requests inserted successfully`);
          });
        }
      });
    }
  });
};

// Create users table
const createUsersTable = () => {
  const createUsersTableQuery = `
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      email VARCHAR(255) NOT NULL UNIQUE,
      password VARCHAR(255) NOT NULL,
      firstName VARCHAR(100) NOT NULL,
      lastName VARCHAR(100) NOT NULL,
      role ENUM('admin', 'doctor', 'nurse', 'staff') NOT NULL,
      userType ENUM('admin', 'staff') NOT NULL,
      department VARCHAR(100),
      specialization VARCHAR(100),
      phone VARCHAR(20),
      isActive BOOLEAN DEFAULT TRUE,
      lastLogin TIMESTAMP NULL,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_email (email),
      INDEX idx_role (role),
      INDEX idx_user_type (userType)
    )
  `;

  db.query(createUsersTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating users table:', err);
      return;
    }
    console.log('Users table created successfully');

    // Insert sample users if table is empty
    insertSampleUsers();
  });
};

// Insert sample users
const insertSampleUsers = () => {
  const checkUsersQuery = 'SELECT COUNT(*) as count FROM users';

  db.query(checkUsersQuery, (err, result) => {
    if (err) {
      console.error('Error checking users data:', err);
      return;
    }

    if (result[0].count === 0) {
      const sampleUsers = [
        [
          '<EMAIL>',
          'admin123', // In production, this should be hashed
          'Admin',
          'User',
          'admin',
          'admin',
          'Administration',
          null,
          '(555) 000-0001'
        ],
        [
          '<EMAIL>',
          'doctor123',
          'Dr. John',
          'Smith',
          'doctor',
          'staff',
          'Cardiology',
          'Cardiologist',
          '(555) 000-0002'
        ],
        [
          '<EMAIL>',
          'nurse123',
          'Jane',
          'Doe',
          'nurse',
          'staff',
          'Emergency',
          'Emergency Nurse',
          '(555) 000-0003'
        ],
        [
          '<EMAIL>',
          'doctor123',
          'Dr. Sarah',
          'Johnson',
          'doctor',
          'staff',
          'Pediatrics',
          'Pediatrician',
          '(*************'
        ],
        [
          '<EMAIL>',
          'nurse123',
          'Michael',
          'Brown',
          'nurse',
          'staff',
          'Surgery',
          'Surgical Nurse',
          '(*************'
        ]
      ];

      const insertQuery = `
        INSERT INTO users
        (email, password, firstName, lastName, role, userType, department, specialization, phone)
        VALUES ?
      `;

      db.query(insertQuery, [sampleUsers], (err, result) => {
        if (err) {
          console.error('Error inserting sample users:', err);
          return;
        }
        console.log(`${sampleUsers.length} sample users inserted successfully`);
      });
    }
  });
};

// Create messages table
const createMessagesTable = () => {
  const createMessagesTableQuery = `
    CREATE TABLE IF NOT EXISTS messages (
      id INT AUTO_INCREMENT PRIMARY KEY,
      messageId VARCHAR(20) NOT NULL UNIQUE,
      senderId INT NOT NULL,
      receiverId INT NOT NULL,
      subject VARCHAR(255) NOT NULL,
      content TEXT NOT NULL,
      messageType ENUM('general', 'urgent', 'patient_related', 'system') DEFAULT 'general',
      priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
      isRead BOOLEAN DEFAULT FALSE,
      isStarred BOOLEAN DEFAULT FALSE,
      isArchived BOOLEAN DEFAULT FALSE,
      patientId VARCHAR(20) NULL,
      attachments JSON NULL,
      replyToId INT NULL,
      sentAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      readAt TIMESTAMP NULL,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (receiverId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (replyToId) REFERENCES messages(id) ON DELETE SET NULL,
      FOREIGN KEY (patientId) REFERENCES patients(nationalId) ON DELETE SET NULL,
      INDEX idx_sender (senderId),
      INDEX idx_receiver (receiverId),
      INDEX idx_message_type (messageType),
      INDEX idx_priority (priority),
      INDEX idx_is_read (isRead),
      INDEX idx_sent_at (sentAt)
    )
  `;

  db.query(createMessagesTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating messages table:', err);
      return;
    }
    console.log('Messages table created successfully');

    // Insert sample messages if table is empty
    insertSampleMessages();
  });
};

// Insert sample messages
const insertSampleMessages = () => {
  const checkMessagesQuery = 'SELECT COUNT(*) as count FROM messages';

  db.query(checkMessagesQuery, (err, result) => {
    if (err) {
      console.error('Error checking messages data:', err);
      return;
    }

    if (result[0].count === 0) {
      // Get existing users to create valid messages
      const getUsersQuery = 'SELECT id, firstName, lastName, role FROM users LIMIT 5';

      db.query(getUsersQuery, (err, users) => {
        if (err) {
          console.error('Error fetching users for sample messages:', err);
          return;
        }

        if (users.length < 2) {
          console.log('Not enough users found, skipping sample messages');
          return;
        }

        // Generate message IDs
        const generateMessageId = (index) => `MSG${String(index + 1).padStart(4, '0')}`;

        // Create sample messages between users
        const sampleMessages = [];
        const messageTemplates = [
          {
            subject: 'Patient Update - Room 205',
            content: 'Patient in room 205 is showing improvement. Vital signs are stable and patient is responding well to treatment. Please review the latest lab results when available.',
            messageType: 'patient_related',
            priority: 'normal',
            isRead: false
          },
          {
            subject: 'Urgent: Emergency Department Staffing',
            content: 'We need additional nursing staff in the Emergency Department immediately. Current patient volume is exceeding capacity. Please coordinate with available staff.',
            messageType: 'urgent',
            priority: 'urgent',
            isRead: true
          },
          {
            subject: 'Weekly Team Meeting Reminder',
            content: 'Reminder: Weekly team meeting scheduled for tomorrow at 2 PM in Conference Room A. Agenda includes patient care protocols and new equipment training.',
            messageType: 'general',
            priority: 'normal',
            isRead: false
          },
          {
            subject: 'Medication Inventory Alert',
            content: 'Low inventory alert for several critical medications. Please review attached list and coordinate with pharmacy for immediate restocking.',
            messageType: 'system',
            priority: 'high',
            isRead: false
          },
          {
            subject: 'Patient Discharge Instructions',
            content: 'Patient John Doe (ID: 123456789) is ready for discharge. Please prepare discharge instructions and coordinate with family for pickup at 3 PM.',
            messageType: 'patient_related',
            priority: 'normal',
            isRead: true
          }
        ];

        // Create messages between different users
        for (let i = 0; i < Math.min(5, messageTemplates.length); i++) {
          const template = messageTemplates[i];
          const senderIndex = i % users.length;
          const receiverIndex = (i + 1) % users.length;

          sampleMessages.push([
            generateMessageId(i),
            users[senderIndex].id,
            users[receiverIndex].id,
            template.subject,
            template.content,
            template.messageType,
            template.priority,
            template.isRead,
            false, // isStarred
            false, // isArchived
            null,  // patientId
            null,  // attachments
            null   // replyToId
          ]);
        }

        if (sampleMessages.length > 0) {
          const insertQuery = `
            INSERT INTO messages
            (messageId, senderId, receiverId, subject, content, messageType, priority, isRead, isStarred, isArchived, patientId, attachments, replyToId)
            VALUES ?
          `;

          db.query(insertQuery, [sampleMessages], (err, result) => {
            if (err) {
              console.error('Error inserting sample messages:', err);
              return;
            }
            console.log(`${sampleMessages.length} sample messages inserted successfully`);
          });
        }
      });
    }
  });
};

// Create role permissions table
const createRolePermissionsTable = () => {
  const createRolePermissionsTableQuery = `
    CREATE TABLE IF NOT EXISTS role_permissions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      role ENUM('admin', 'doctor', 'nurse', 'staff') NOT NULL,
      resource VARCHAR(50) NOT NULL,
      action ENUM('create', 'read', 'update', 'delete', 'manage') NOT NULL,
      allowed BOOLEAN DEFAULT TRUE,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_permission (role, resource, action),
      INDEX idx_role (role),
      INDEX idx_resource (resource),
      INDEX idx_action (action)
    )
  `;

  db.query(createRolePermissionsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating role permissions table:', err);
      return;
    }
    console.log('Role permissions table created successfully');

    // Insert default permissions
    insertDefaultPermissions();
  });
};

// Insert default role permissions
const insertDefaultPermissions = () => {
  const checkPermissionsQuery = 'SELECT COUNT(*) as count FROM role_permissions';

  db.query(checkPermissionsQuery, (err, result) => {
    if (err) {
      console.error('Error checking permissions data:', err);
      return;
    }

    if (result[0].count === 0) {
      const defaultPermissions = [
        // Admin permissions - full access to everything
        ['admin', 'patients', 'manage'],
        ['admin', 'doctors', 'manage'],
        ['admin', 'rooms', 'manage'],
        ['admin', 'exams', 'manage'],
        ['admin', 'appointments', 'manage'],
        ['admin', 'transfers', 'manage'],
        ['admin', 'messages', 'manage'],
        ['admin', 'users', 'manage'],
        ['admin', 'dashboard', 'read'],

        // Doctor permissions
        ['doctor', 'patients', 'read'],
        ['doctor', 'patients', 'update'],
        ['doctor', 'exams', 'manage'],
        ['doctor', 'appointments', 'read'],
        ['doctor', 'appointments', 'update'],
        ['doctor', 'rooms', 'read'],
        ['doctor', 'transfers', 'read'],
        ['doctor', 'transfers', 'create'],
        ['doctor', 'messages', 'manage'],

        // Nurse permissions - restricted to messages and patients only
        ['nurse', 'patients', 'read'],
        ['nurse', 'patients', 'update'],
        ['nurse', 'messages', 'manage'],

        // Staff permissions
        ['staff', 'patients', 'read'],
        ['staff', 'appointments', 'manage'],
        ['staff', 'messages', 'manage']
      ];

      if (defaultPermissions.length > 0) {
        const insertQuery = `
          INSERT INTO role_permissions (role, resource, action)
          VALUES ?
        `;

        db.query(insertQuery, [defaultPermissions], (err, result) => {
          if (err) {
            console.error('Error inserting default permissions:', err);
            return;
          }
          console.log(`${defaultPermissions.length} default permissions inserted successfully`);
        });
      }
    }
  });
};

// Create utilities table
const createUtilitiesTable = () => {
  const createUtilitiesTableQuery = `
    CREATE TABLE IF NOT EXISTS utilities (
      id INT AUTO_INCREMENT PRIMARY KEY,
      supplyId VARCHAR(20) UNIQUE NOT NULL,
      supplyName VARCHAR(100) NOT NULL,
      supplyType ENUM('medication', 'oxygen_tanks', 'surgical_supplies', 'ppe', 'diagnostic_supplies', 'emergency_supplies', 'iv_fluids', 'blood_products', 'medical_devices', 'other') NOT NULL,
      location VARCHAR(100) NOT NULL,
      department VARCHAR(100),
      status ENUM('in_stock', 'low_stock', 'out_of_stock', 'expired', 'recalled') DEFAULT 'in_stock',
      priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
      description TEXT,
      currentStock INT DEFAULT 0,
      minimumStock INT DEFAULT 10,
      maximumStock INT DEFAULT 100,
      unitOfMeasure VARCHAR(20) DEFAULT 'units',
      expiryDate DATE,
      batchNumber VARCHAR(50),
      supplier VARCHAR(100),
      lastRestocked DATE,
      costPerUnit DECIMAL(10,2),
      totalValue DECIMAL(10,2),
      responsiblePerson VARCHAR(100),
      contactNumber VARCHAR(20),
      notes TEXT,
      createdBy VARCHAR(100),
      updatedBy VARCHAR(100),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_supply_type (supplyType),
      INDEX idx_status (status),
      INDEX idx_location (location),
      INDEX idx_expiry_date (expiryDate),
      INDEX idx_current_stock (currentStock)
    )
  `;

  db.query(createUtilitiesTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating utilities table:', err);
      return;
    }
    console.log('Utilities table created successfully');

    // Create stock transactions table after utilities table is ready
    createStockTransactionsTable();

    // Insert sample utilities if table is empty
    insertSampleUtilities();
  });
};

// Create stock transactions table
const createStockTransactionsTable = () => {
  const createStockTransactionsTableQuery = `
    CREATE TABLE IF NOT EXISTS stock_transactions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      transactionId VARCHAR(20) UNIQUE NOT NULL,
      supplyId VARCHAR(20) NOT NULL,
      transactionType ENUM('stock_in', 'stock_out', 'adjustment', 'expired', 'damaged') NOT NULL,
      quantity INT NOT NULL,
      unitOfMeasure VARCHAR(20) NOT NULL,
      previousStock INT DEFAULT 0,
      newStock INT DEFAULT 0,
      unitCost DECIMAL(10,2),
      totalCost DECIMAL(10,2),
      supplier VARCHAR(100),
      batchNumber VARCHAR(50),
      expiryDate DATE,
      invoiceNumber VARCHAR(50),
      deliveryDate DATE,
      receivedBy VARCHAR(100),
      notes TEXT,
      reason VARCHAR(200),
      createdBy VARCHAR(100),
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_supply_id (supplyId),
      INDEX idx_transaction_type (transactionType),
      INDEX idx_created_at (createdAt)
    )
  `;

  db.query(createStockTransactionsTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating stock transactions table:', err);
      return;
    }
    console.log('Stock transactions table created successfully');
  });
};

// Insert sample utilities
const insertSampleUtilities = () => {
  const checkUtilitiesQuery = 'SELECT COUNT(*) as count FROM utilities';

  db.query(checkUtilitiesQuery, (err, result) => {
    if (err) {
      console.error('Error checking utilities data:', err);
      return;
    }

    if (result[0].count === 0) {
      // Generate supply IDs
      const generateSupplyId = (type, index) => `${type.toUpperCase().substring(0, 3)}${String(index + 1).padStart(3, '0')}`;

      const sampleUtilities = [
        // Medications
        [generateSupplyId('medication', 0), 'Paracetamol 500mg', 'medication', 'Pharmacy - Main Storage', 'Pharmacy', 'in_stock', 'medium', 'Pain relief and fever reducer tablets', 500, 50, 1000, 'tablets', '2025-06-15', 'BATCH001', 'PharmaCorp Ltd.', '2024-01-15', 0.25, 125.00, 'Dr. Sarah Johnson', '+**********', 'Store in cool, dry place', 'Admin', 'Admin'],
        [generateSupplyId('medication', 1), 'Insulin Vials', 'medication', 'Pharmacy - Refrigerated', 'Endocrinology', 'low_stock', 'critical', 'Insulin for diabetes management', 15, 20, 100, 'vials', '2024-12-31', 'INS2024', 'DiabetesCare Inc.', '2024-02-01', 45.00, 675.00, 'Dr. Sarah Johnson', '+**********', 'Requires refrigeration 2-8°C', 'Admin', 'Admin'],
        [generateSupplyId('medication', 2), 'Antibiotics - Amoxicillin', 'medication', 'Pharmacy - Main Storage', 'Pharmacy', 'in_stock', 'high', 'Broad-spectrum antibiotic capsules', 200, 30, 500, 'capsules', '2025-03-20', 'AMX2024', 'AntiBio Pharma', '2024-01-20', 1.50, 300.00, 'Dr. Sarah Johnson', '+**********', 'Complete course as prescribed', 'Admin', 'Admin'],

        // Oxygen Tanks
        [generateSupplyId('oxygen', 0), 'Oxygen Tank - Large', 'oxygen_tanks', 'ICU - Storage Room', 'ICU', 'in_stock', 'critical', 'Large oxygen tanks for critical care', 25, 10, 50, 'tanks', '2026-01-15', 'OXY2024L', 'OxygenPro Medical', '2024-01-10', 85.00, 2125.00, 'Nurse Manager ICU', '+**********', 'Check pressure regularly', 'Admin', 'Admin'],
        [generateSupplyId('oxygen', 1), 'Portable Oxygen Tanks', 'oxygen_tanks', 'Emergency Department', 'Emergency', 'low_stock', 'high', 'Portable oxygen for emergency transport', 8, 15, 40, 'tanks', '2025-11-30', 'POXY2024', 'EmergencyOxy Ltd.', '2024-02-05', 35.00, 280.00, 'ER Supervisor', '+**********', 'Ready for ambulance use', 'Admin', 'Admin'],

        // Surgical Supplies
        [generateSupplyId('surgical', 0), 'Surgical Gloves - Sterile', 'surgical_supplies', 'OR - Supply Room', 'Surgery', 'in_stock', 'high', 'Sterile latex surgical gloves', 2000, 200, 5000, 'pairs', '2025-08-15', 'GLOVE2024', 'SterileSupply Co.', '2024-01-25', 0.75, 1500.00, 'OR Supervisor', '+**********', 'Maintain sterile conditions', 'Admin', 'Admin'],
        [generateSupplyId('surgical', 1), 'Surgical Masks', 'surgical_supplies', 'OR - Supply Room', 'Surgery', 'in_stock', 'medium', 'Disposable surgical face masks', 1500, 300, 3000, 'pieces', '2025-07-10', 'MASK2024', 'MedMask Inc.', '2024-02-10', 0.15, 225.00, 'OR Supervisor', '+**********', 'Single use only', 'Admin', 'Admin'],

        // PPE
        [generateSupplyId('ppe', 0), 'N95 Respirators', 'ppe', 'Central Supply', 'All Departments', 'low_stock', 'critical', 'N95 filtering facepiece respirators', 45, 100, 500, 'pieces', '2025-09-30', 'N95-2024', 'SafetyFirst Medical', '2024-01-30', 2.50, 112.50, 'Supply Manager', '+**********', 'Fit test required', 'Admin', 'Admin'],
        [generateSupplyId('ppe', 1), 'Isolation Gowns', 'ppe', 'Infectious Disease Ward', 'Infectious Disease', 'in_stock', 'high', 'Disposable isolation gowns', 300, 50, 800, 'pieces', '2025-05-25', 'GOWN2024', 'ProtectWear Ltd.', '2024-02-15', 3.25, 975.00, 'ID Ward Supervisor', '+**********', 'Dispose after single use', 'Admin', 'Admin'],

        // IV Fluids
        [generateSupplyId('iv', 0), 'Normal Saline 0.9%', 'iv_fluids', 'IV Fluid Storage', 'All Departments', 'in_stock', 'critical', '1000ml normal saline bags', 150, 50, 300, 'bags', '2025-12-15', 'SAL2024', 'FluidTherapy Inc.', '2024-01-12', 4.50, 675.00, 'IV Therapy Coordinator', '+**********', 'Check for leaks before use', 'Admin', 'Admin'],
        [generateSupplyId('iv', 1), 'Dextrose 5% in Water', 'iv_fluids', 'IV Fluid Storage', 'All Departments', 'in_stock', 'high', '500ml dextrose solution bags', 100, 30, 200, 'bags', '2025-10-20', 'DEX2024', 'FluidTherapy Inc.', '2024-02-20', 5.25, 525.00, 'IV Therapy Coordinator', '+**********', 'Monitor glucose levels', 'Admin', 'Admin'],

        // Blood Products
        [generateSupplyId('blood', 0), 'Blood Bags - Type O Negative', 'blood_products', 'Blood Bank - Refrigerated', 'Blood Bank', 'low_stock', 'critical', 'Universal donor blood bags', 12, 20, 50, 'units', '2024-03-15', 'ONEG2024', 'Regional Blood Center', '2024-02-25', 125.00, 1500.00, 'Blood Bank Manager', '+**********', 'Critical shortage - universal donor', 'Admin', 'Admin'],
        [generateSupplyId('blood', 1), 'Platelet Concentrates', 'blood_products', 'Blood Bank - Refrigerated', 'Blood Bank', 'in_stock', 'critical', 'Platelet concentrates for transfusion', 8, 5, 25, 'units', '2024-02-10', 'PLT2024', 'Regional Blood Center', '2024-02-28', 200.00, 1600.00, 'Blood Bank Manager', '+**********', 'Short shelf life - use quickly', 'Admin', 'Admin'],

        // Medical Devices
        [generateSupplyId('device', 0), 'Disposable Syringes 10ml', 'medical_devices', 'Central Supply', 'All Departments', 'in_stock', 'medium', 'Sterile disposable syringes', 800, 100, 2000, 'pieces', '2026-04-30', 'SYR2024', 'MedDevice Corp.', '2024-01-18', 0.35, 280.00, 'Supply Manager', '+**********', 'Single use - dispose safely', 'Admin', 'Admin'],
        [generateSupplyId('device', 1), 'Blood Pressure Cuffs', 'medical_devices', 'Equipment Storage', 'Cardiology', 'low_stock', 'medium', 'Disposable BP cuffs various sizes', 25, 30, 100, 'pieces', '2026-08-15', 'BP2024', 'CardioEquip Ltd.', '2024-02-12', 8.50, 212.50, 'Cardiology Supervisor', '+**********', 'Various sizes available', 'Admin', 'Admin']
      ];

      if (sampleUtilities.length > 0) {
        const insertQuery = `
          INSERT INTO utilities
          (supplyId, supplyName, supplyType, location, department, status, priority, description, currentStock, minimumStock, maximumStock, unitOfMeasure, expiryDate, batchNumber, supplier, lastRestocked, costPerUnit, totalValue, responsiblePerson, contactNumber, notes, createdBy, updatedBy)
          VALUES ?
        `;

        db.query(insertQuery, [sampleUtilities], (err, result) => {
          if (err) {
            console.error('Error inserting sample utilities:', err);
            return;
          }
          console.log(`${sampleUtilities.length} sample utilities inserted successfully`);
        });
      }
    }
  });
};

// Insert sample rooms
const insertSampleRooms = () => {
  const checkRoomsQuery = 'SELECT COUNT(*) as count FROM rooms';

  db.query(checkRoomsQuery, (err, result) => {
    if (err) {
      console.error('Error checking rooms data:', err);
      return;
    }

    if (result[0].count === 0) {
      const sampleRooms = [
        ['101', 'general', 'General Medicine', 2, 0, 'available', 1, 'TV, WiFi, Private Bathroom', 50000],
        ['102', 'general', 'General Medicine', 2, 1, 'occupied', 1, 'TV, WiFi, Private Bathroom', 50000],
        ['201', 'private', 'Cardiology', 1, 0, 'available', 2, 'TV, WiFi, Private Bathroom, Refrigerator', 100000],
        ['202', 'private', 'Cardiology', 1, 1, 'occupied', 2, 'TV, WiFi, Private Bathroom, Refrigerator', 100000],
        ['301', 'icu', 'Intensive Care', 1, 0, 'available', 3, 'Life Support, Monitoring Equipment', 200000],
        ['302', 'icu', 'Intensive Care', 1, 1, 'occupied', 3, 'Life Support, Monitoring Equipment', 200000],
        ['401', 'emergency', 'Emergency', 1, 0, 'available', 4, 'Emergency Equipment', 75000],
        ['501', 'surgery', 'Surgery', 1, 0, 'maintenance', 5, 'Surgical Equipment, Recovery Bed', 150000],
        ['601', 'maternity', 'Maternity', 1, 0, 'available', 6, 'Baby Crib, Nursing Chair', 80000],
        ['701', 'pediatric', 'Pediatrics', 2, 0, 'available', 7, 'Child-friendly, Play Area', 60000]
      ];

      const insertQuery = `
        INSERT INTO rooms
        (roomNumber, roomType, department, capacity, currentOccupancy, status, floor, amenities, dailyRate)
        VALUES ?
      `;

      db.query(insertQuery, [sampleRooms], (err, result) => {
        if (err) {
          console.error('Error inserting sample rooms:', err);
          return;
        }
        console.log('Sample rooms data inserted');
      });
    }
  });
};

// Create billing table if it doesn't exist
const createBillingTable = () => {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS billing (
      id INT AUTO_INCREMENT PRIMARY KEY,
      invoiceNumber VARCHAR(20) UNIQUE NOT NULL,
      patientNationalId VARCHAR(20),
      patientName VARCHAR(200) NOT NULL,
      patientEmail VARCHAR(255),
      patientPhone VARCHAR(20),
      patientAddress TEXT,
      consultationFees DECIMAL(10,2) DEFAULT 0,
      examFees DECIMAL(10,2) DEFAULT 0,
      items JSON NOT NULL,
      subtotal DECIMAL(10,2) NOT NULL,
      insurancePercentage DECIMAL(5,2) DEFAULT 0,
      insuranceAmount DECIMAL(10,2) DEFAULT 0,
      totalAmountToBePaid DECIMAL(10,2) NOT NULL,
      amountPaid DECIMAL(10,2) DEFAULT 0,
      paymentMethod ENUM('Cash', 'Card', 'Insurance', 'Mobile Money', 'Flutterwave') DEFAULT 'Cash',
      paymentStatus ENUM('Paid', 'Pending', 'Partial', 'Cancelled') DEFAULT 'Pending',
      transactionId VARCHAR(100),
      flutterwaveRef VARCHAR(100),
      paymentType VARCHAR(50),
      notes TEXT,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (patientNationalId) REFERENCES patients(nationalId) ON DELETE SET NULL
    )
  `;

  db.query(createTableQuery, (err, result) => {
    if (err) {
      console.error('Error creating billing table:', err);
      return;
    }
    console.log('Billing table ready');

    // Add Flutterwave payment columns if they don't exist
    const alterQueries = [
      "ALTER TABLE billing MODIFY COLUMN paymentMethod ENUM('Cash', 'Card', 'Insurance', 'Mobile Money', 'Flutterwave') DEFAULT 'Cash'",
      "ALTER TABLE billing ADD COLUMN IF NOT EXISTS transactionId VARCHAR(100)",
      "ALTER TABLE billing ADD COLUMN IF NOT EXISTS flutterwaveRef VARCHAR(100)",
      "ALTER TABLE billing ADD COLUMN IF NOT EXISTS paymentType VARCHAR(50)"
    ];

    alterQueries.forEach((query, index) => {
      db.query(query, (err) => {
        if (err && !err.message.includes('Duplicate column name')) {
          console.error(`Error in alter query ${index + 1}:`, err.message);
        }
      });
    });
  });
};

// Initialize database
createPatientsTable();
createBillingTable();
createUtilitiesTable();

// Patient Management API Routes

// GET all patients
app.get('/api/patients', (req, res) => {
  const query = 'SELECT * FROM patients ORDER BY createdAt DESC';

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching patients:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patients',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET single patient by National ID
app.get('/api/patients/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;
  const query = 'SELECT * FROM patients WHERE nationalId = ?';

  db.query(query, [nationalId], (err, results) => {
    if (err) {
      console.error('Error fetching patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    res.json({
      success: true,
      data: results[0]
    });
  });
});

// POST create new patient
app.post('/api/patients', (req, res) => {
  const {
    nationalId,
    firstName,
    lastName,
    email,
    phone,
    dateOfBirth,
    gender,
    address,
    emergencyContact,
    bloodType,
    allergies,
    medicalHistory
  } = req.body;

  // Basic validation
  if (!nationalId || !firstName || !lastName || !email || !phone || !dateOfBirth) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: nationalId, firstName, lastName, email, phone, dateOfBirth'
    });
  }

  // Validate national ID format (basic validation - adjust according to your country's format)
  if (!/^\d{10,20}$/.test(nationalId)) {
    return res.status(400).json({
      success: false,
      message: 'National ID must be 10-20 digits'
    });
  }

  // Check if national ID already exists
  const checkNationalIdQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkNationalIdQuery, [nationalId], (err, results) => {
    if (err) {
      console.error('Error checking national ID:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking national ID',
        error: err.message
      });
    }

    if (results.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Patient with this national ID already exists'
      });
    }

    // Check if email already exists
    const checkEmailQuery = 'SELECT nationalId FROM patients WHERE email = ?';

    db.query(checkEmailQuery, [email], (err, emailResults) => {
      if (err) {
        console.error('Error checking email:', err);
        return res.status(500).json({
          success: false,
          message: 'Error checking email',
          error: err.message
        });
      }

      if (emailResults.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Patient with this email already exists'
        });
      }

      // Insert new patient
      const insertQuery = `
        INSERT INTO patients
        (nationalId, firstName, lastName, email, phone, dateOfBirth, gender, address, emergencyContact, bloodType, allergies, medicalHistory)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        nationalId,
        firstName,
        lastName,
        email,
        phone,
        dateOfBirth,
        gender || null,
        address || null,
        emergencyContact || null,
        bloodType || null,
        allergies || null,
        medicalHistory || null
      ];

    db.query(insertQuery, values, (err, result) => {
      if (err) {
        console.error('Error creating patient:', err);
        return res.status(500).json({
          success: false,
          message: 'Error creating patient',
          error: err.message
        });
      }

        // Get the created patient
        const getPatientQuery = 'SELECT * FROM patients WHERE nationalId = ?';
        db.query(getPatientQuery, [nationalId], (err, patientResults) => {
          if (err) {
            console.error('Error fetching created patient:', err);
            return res.status(500).json({
              success: false,
              message: 'Patient created but error fetching data',
              error: err.message
            });
          }

          res.status(201).json({
            success: true,
            message: 'Patient created successfully',
            data: patientResults[0]
          });
        });
      });
    });
  });
});

// PUT update patient by National ID
app.put('/api/patients/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;
  const {
    firstName,
    lastName,
    email,
    phone,
    dateOfBirth,
    gender,
    address,
    emergencyContact,
    bloodType,
    allergies,
    medicalHistory
  } = req.body;

  // Check if patient exists
  const checkPatientQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkPatientQuery, [nationalId], (err, results) => {
    if (err) {
      console.error('Error checking patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking patient',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Check if email already exists (excluding current patient)
    const checkEmailQuery = 'SELECT nationalId FROM patients WHERE email = ? AND nationalId != ?';

    db.query(checkEmailQuery, [email, nationalId], (err, emailResults) => {
      if (err) {
        console.error('Error checking email:', err);
        return res.status(500).json({
          success: false,
          message: 'Error checking email',
          error: err.message
        });
      }

      if (emailResults.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Patient with this email already exists'
        });
      }

      // Update patient
      const updateQuery = `
        UPDATE patients SET
        firstName = ?, lastName = ?, email = ?, phone = ?, dateOfBirth = ?,
        gender = ?, address = ?, emergencyContact = ?, bloodType = ?,
        allergies = ?, medicalHistory = ?
        WHERE nationalId = ?
      `;

      const values = [
        firstName,
        lastName,
        email,
        phone,
        dateOfBirth,
        gender || null,
        address || null,
        emergencyContact || null,
        bloodType || null,
        allergies || null,
        medicalHistory || null,
        nationalId
      ];

      db.query(updateQuery, values, (err, result) => {
        if (err) {
          console.error('Error updating patient:', err);
          return res.status(500).json({
            success: false,
            message: 'Error updating patient',
            error: err.message
          });
        }

        // Get the updated patient
        const getPatientQuery = 'SELECT * FROM patients WHERE nationalId = ?';
        db.query(getPatientQuery, [nationalId], (err, patientResults) => {
          if (err) {
            console.error('Error fetching updated patient:', err);
            return res.status(500).json({
              success: false,
              message: 'Patient updated but error fetching data',
              error: err.message
            });
          }

          res.json({
            success: true,
            message: 'Patient updated successfully',
            data: patientResults[0]
          });
        });
      });
    });
  });
});

// DELETE patient by National ID
app.delete('/api/patients/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;

  // First get the patient data before deleting
  const getPatientQuery = 'SELECT * FROM patients WHERE nationalId = ?';

  db.query(getPatientQuery, [nationalId], (err, results) => {
    if (err) {
      console.error('Error fetching patient for deletion:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    const patientToDelete = results[0];

    // Delete the patient
    const deleteQuery = 'DELETE FROM patients WHERE nationalId = ?';

    db.query(deleteQuery, [nationalId], (err, result) => {
      if (err) {
        console.error('Error deleting patient:', err);
        return res.status(500).json({
          success: false,
          message: 'Error deleting patient',
          error: err.message
        });
      }

      res.json({
        success: true,
        message: 'Patient deleted successfully',
        data: patientToDelete
      });
    });
  });
});

// Search patients
app.get('/api/patients/search/:query', (req, res) => {
  const searchQuery = req.params.query;
  const searchPattern = `%${searchQuery}%`;

  const query = `
    SELECT * FROM patients
    WHERE nationalId LIKE ?
    OR firstName LIKE ?
    OR lastName LIKE ?
    OR email LIKE ?
    OR phone LIKE ?
    ORDER BY createdAt DESC
  `;

  db.query(query, [searchPattern, searchPattern, searchPattern, searchPattern, searchPattern], (err, results) => {
    if (err) {
      console.error('Error searching patients:', err);
      return res.status(500).json({
        success: false,
        message: 'Error searching patients',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// Exam Management API Routes

// GET all exams
app.get('/api/exams', (req, res) => {
  const query = `
    SELECT e.*, p.firstName, p.lastName, p.email
    FROM exams e
    JOIN patients p ON e.nationalId = p.nationalId
    ORDER BY e.createdAt DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching exams:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching exams',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET exams for a specific patient
app.get('/api/exams/patient/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;
  const query = `
    SELECT e.*, p.firstName, p.lastName, p.email
    FROM exams e
    JOIN patients p ON e.nationalId = p.nationalId
    WHERE e.nationalId = ?
    ORDER BY e.createdAt DESC
  `;

  db.query(query, [nationalId], (err, results) => {
    if (err) {
      console.error('Error fetching patient exams:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient exams',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET single exam by ID
app.get('/api/exams/:id', (req, res) => {
  const examId = req.params.id;
  const query = `
    SELECT e.*, p.firstName, p.lastName, p.email
    FROM exams e
    JOIN patients p ON e.nationalId = p.nationalId
    WHERE e.id = ?
  `;

  db.query(query, [examId], (err, results) => {
    if (err) {
      console.error('Error fetching exam:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching exam',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    res.json({
      success: true,
      data: results[0]
    });
  });
});

// POST create new exam
app.post('/api/exams', (req, res) => {
  const {
    nationalId,
    examType,
    examDate,
    results,
    notes,
    status
  } = req.body;

  // Basic validation
  if (!nationalId || !examType || !examDate || !results) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: nationalId, examType, examDate, results'
    });
  }

  // Check if patient exists
  const checkPatientQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkPatientQuery, [nationalId], (err, patientResults) => {
    if (err) {
      console.error('Error checking patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking patient',
        error: err.message
      });
    }

    if (patientResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Insert new exam
    const insertQuery = `
      INSERT INTO exams (nationalId, examType, examDate, results, notes, status)
      VALUES (?, ?, ?, ?, ?, ?)
    `;

    const values = [
      nationalId,
      examType,
      examDate,
      results,
      notes || null,
      status || 'pending'
    ];

    db.query(insertQuery, values, (err, result) => {
      if (err) {
        console.error('Error creating exam:', err);
        return res.status(500).json({
          success: false,
          message: 'Error creating exam',
          error: err.message
        });
      }

      // Get the created exam with patient info
      const getExamQuery = `
        SELECT e.*, p.firstName, p.lastName, p.email
        FROM exams e
        JOIN patients p ON e.nationalId = p.nationalId
        WHERE e.id = ?
      `;

      db.query(getExamQuery, [result.insertId], (err, examResults) => {
        if (err) {
          console.error('Error fetching created exam:', err);
          return res.status(500).json({
            success: false,
            message: 'Exam created but error fetching data',
            error: err.message
          });
        }

        res.status(201).json({
          success: true,
          message: 'Exam created successfully',
          data: examResults[0]
        });
      });
    });
  });
});

// PUT update exam by ID
app.put('/api/exams/:id', (req, res) => {
  const examId = req.params.id;
  const {
    examType,
    examDate,
    results,
    notes,
    status
  } = req.body;

  // Check if exam exists
  const checkExamQuery = 'SELECT id FROM exams WHERE id = ?';

  db.query(checkExamQuery, [examId], (err, examResults) => {
    if (err) {
      console.error('Error checking exam:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking exam',
        error: err.message
      });
    }

    if (examResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    // Update exam
    const updateQuery = `
      UPDATE exams SET
      examType = ?, examDate = ?, results = ?, notes = ?, status = ?
      WHERE id = ?
    `;

    const values = [
      examType,
      examDate,
      results,
      notes || null,
      status || 'pending',
      examId
    ];

    db.query(updateQuery, values, (err, result) => {
      if (err) {
        console.error('Error updating exam:', err);
        return res.status(500).json({
          success: false,
          message: 'Error updating exam',
          error: err.message
        });
      }

      // Get the updated exam with patient info
      const getExamQuery = `
        SELECT e.*, p.firstName, p.lastName, p.email
        FROM exams e
        JOIN patients p ON e.nationalId = p.nationalId
        WHERE e.id = ?
      `;

      db.query(getExamQuery, [examId], (err, updatedResults) => {
        if (err) {
          console.error('Error fetching updated exam:', err);
          return res.status(500).json({
            success: false,
            message: 'Exam updated but error fetching data',
            error: err.message
          });
        }

        res.json({
          success: true,
          message: 'Exam updated successfully',
          data: updatedResults[0]
        });
      });
    });
  });
});

// DELETE exam by ID
app.delete('/api/exams/:id', (req, res) => {
  const examId = req.params.id;

  // First get the exam data before deleting
  const getExamQuery = `
    SELECT e.*, p.firstName, p.lastName, p.email
    FROM exams e
    JOIN patients p ON e.nationalId = p.nationalId
    WHERE e.id = ?
  `;

  db.query(getExamQuery, [examId], (err, results) => {
    if (err) {
      console.error('Error fetching exam for deletion:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching exam',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Exam not found'
      });
    }

    const examToDelete = results[0];

    // Delete the exam
    const deleteQuery = 'DELETE FROM exams WHERE id = ?';

    db.query(deleteQuery, [examId], (err, result) => {
      if (err) {
        console.error('Error deleting exam:', err);
        return res.status(500).json({
          success: false,
          message: 'Error deleting exam',
          error: err.message
        });
      }

      res.json({
        success: true,
        message: 'Exam deleted successfully',
        data: examToDelete
      });
    });
  });
});

// Room Management API Routes

// GET all rooms
app.get('/api/rooms', (req, res) => {
  const query = 'SELECT * FROM rooms ORDER BY floor ASC, roomNumber ASC';

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching rooms:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching rooms',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET rooms by status
app.get('/api/rooms/status/:status', (req, res) => {
  const status = req.params.status;
  const query = 'SELECT * FROM rooms WHERE status = ? ORDER BY floor ASC, roomNumber ASC';

  db.query(query, [status], (err, results) => {
    if (err) {
      console.error('Error fetching rooms by status:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching rooms by status',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET rooms by type
app.get('/api/rooms/type/:type', (req, res) => {
  const roomType = req.params.type;
  const query = 'SELECT * FROM rooms WHERE roomType = ? ORDER BY floor ASC, roomNumber ASC';

  db.query(query, [roomType], (err, results) => {
    if (err) {
      console.error('Error fetching rooms by type:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching rooms by type',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET single room by ID
app.get('/api/rooms/:id', (req, res) => {
  const roomId = req.params.id;
  const query = 'SELECT * FROM rooms WHERE id = ?';

  db.query(query, [roomId], (err, results) => {
    if (err) {
      console.error('Error fetching room:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching room',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }

    res.json({
      success: true,
      data: results[0]
    });
  });
});

// POST assign patient to room
app.post('/api/rooms/assign', (req, res) => {
  const {
    roomId,
    nationalId,
    admissionDate,
    expectedDischargeDate,
    notes,
    assignedBy
  } = req.body;

  // Basic validation
  if (!roomId || !nationalId || !admissionDate) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: roomId, nationalId, admissionDate'
    });
  }

  // Check if patient exists
  const checkPatientQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkPatientQuery, [nationalId], (err, patientResults) => {
    if (err) {
      console.error('Error checking patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking patient',
        error: err.message
      });
    }

    if (patientResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Check if room exists and is available
    const checkRoomQuery = 'SELECT * FROM rooms WHERE id = ?';

    db.query(checkRoomQuery, [roomId], (err, roomResults) => {
      if (err) {
        console.error('Error checking room:', err);
        return res.status(500).json({
          success: false,
          message: 'Error checking room',
          error: err.message
        });
      }

      if (roomResults.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Room not found'
        });
      }

      const room = roomResults[0];

      if (room.currentOccupancy >= room.capacity) {
        return res.status(400).json({
          success: false,
          message: 'Room is at full capacity'
        });
      }

      // Check if patient already has an active assignment
      const checkActiveAssignmentQuery = 'SELECT id FROM room_assignments WHERE nationalId = ? AND status = "active"';

      db.query(checkActiveAssignmentQuery, [nationalId], (err, activeResults) => {
        if (err) {
          console.error('Error checking active assignments:', err);
          return res.status(500).json({
            success: false,
            message: 'Error checking active assignments',
            error: err.message
          });
        }

        if (activeResults.length > 0) {
          return res.status(400).json({
            success: false,
            message: 'Patient already has an active room assignment'
          });
        }

        // Create room assignment
        const insertAssignmentQuery = `
          INSERT INTO room_assignments (roomId, nationalId, admissionDate, expectedDischargeDate, notes, assignedBy)
          VALUES (?, ?, ?, ?, ?, ?)
        `;

        const assignmentValues = [
          roomId,
          nationalId,
          admissionDate,
          expectedDischargeDate || null,
          notes || null,
          assignedBy || null
        ];

        db.query(insertAssignmentQuery, assignmentValues, (err, assignmentResult) => {
          if (err) {
            console.error('Error creating room assignment:', err);
            return res.status(500).json({
              success: false,
              message: 'Error creating room assignment',
              error: err.message
            });
          }

          // Update room occupancy and status
          const newOccupancy = room.currentOccupancy + 1;
          const newStatus = newOccupancy >= room.capacity ? 'occupied' : room.status;

          const updateRoomQuery = 'UPDATE rooms SET currentOccupancy = ?, status = ? WHERE id = ?';

          db.query(updateRoomQuery, [newOccupancy, newStatus, roomId], (err, updateResult) => {
            if (err) {
              console.error('Error updating room occupancy:', err);
              return res.status(500).json({
                success: false,
                message: 'Assignment created but error updating room',
                error: err.message
              });
            }

            // Get the created assignment with patient and room info
            const getAssignmentQuery = `
              SELECT ra.*, p.firstName, p.lastName, p.email, r.roomNumber, r.roomType, r.department
              FROM room_assignments ra
              JOIN patients p ON ra.nationalId = p.nationalId
              JOIN rooms r ON ra.roomId = r.id
              WHERE ra.id = ?
            `;

            db.query(getAssignmentQuery, [assignmentResult.insertId], (err, assignmentResults) => {
              if (err) {
                console.error('Error fetching created assignment:', err);
                return res.status(500).json({
                  success: false,
                  message: 'Assignment created but error fetching data',
                  error: err.message
                });
              }

              res.status(201).json({
                success: true,
                message: 'Patient assigned to room successfully',
                data: assignmentResults[0]
              });
            });
          });
        });
      });
    });
  });
});

// GET all room assignments
app.get('/api/room-assignments', (req, res) => {
  const query = `
    SELECT ra.*, p.firstName, p.lastName, p.email, r.roomNumber, r.roomType, r.department, r.floor
    FROM room_assignments ra
    JOIN patients p ON ra.nationalId = p.nationalId
    JOIN rooms r ON ra.roomId = r.id
    ORDER BY ra.createdAt DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching room assignments:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching room assignments',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// POST discharge patient
app.post('/api/rooms/discharge', (req, res) => {
  const { assignmentId, actualDischargeDate, notes } = req.body;

  if (!assignmentId || !actualDischargeDate) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: assignmentId, actualDischargeDate'
    });
  }

  // Get assignment details
  const getAssignmentQuery = `
    SELECT ra.*, r.currentOccupancy, r.capacity
    FROM room_assignments ra
    JOIN rooms r ON ra.roomId = r.id
    WHERE ra.id = ? AND ra.status = 'active'
  `;

  db.query(getAssignmentQuery, [assignmentId], (err, results) => {
    if (err) {
      console.error('Error fetching assignment:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching assignment',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Active assignment not found'
      });
    }

    const assignment = results[0];

    // Update assignment status
    const updateAssignmentQuery = `
      UPDATE room_assignments
      SET status = 'discharged', actualDischargeDate = ?, notes = CONCAT(COALESCE(notes, ''), ?, ?)
      WHERE id = ?
    `;

    const dischargeNotes = notes ? `\n\nDischarge Notes: ${notes}` : '';
    const updateValues = [actualDischargeDate, '\n\nDischarged on: ', actualDischargeDate, dischargeNotes, assignmentId];

    db.query(updateAssignmentQuery, updateValues, (err, updateResult) => {
      if (err) {
        console.error('Error updating assignment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error updating assignment',
          error: err.message
        });
      }

      // Update room occupancy
      const newOccupancy = Math.max(0, assignment.currentOccupancy - 1);
      const newStatus = newOccupancy === 0 ? 'available' : 'occupied';

      const updateRoomQuery = 'UPDATE rooms SET currentOccupancy = ?, status = ? WHERE id = ?';

      db.query(updateRoomQuery, [newOccupancy, newStatus, assignment.roomId], (err, roomUpdateResult) => {
        if (err) {
          console.error('Error updating room occupancy:', err);
          return res.status(500).json({
            success: false,
            message: 'Patient discharged but error updating room',
            error: err.message
          });
        }

        res.json({
          success: true,
          message: 'Patient discharged successfully',
          data: {
            assignmentId: assignmentId,
            dischargeDate: actualDischargeDate,
            roomOccupancy: newOccupancy
          }
        });
      });
    });
  });
});

// POST transfer patient
app.post('/api/rooms/transfer', (req, res) => {
  const { assignmentId, newRoomId, transferDate, notes } = req.body;

  if (!assignmentId || !newRoomId || !transferDate) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: assignmentId, newRoomId, transferDate'
    });
  }

  // Get current assignment and check new room availability
  const getAssignmentQuery = `
    SELECT ra.*, r1.currentOccupancy as oldRoomOccupancy, r2.currentOccupancy as newRoomOccupancy, r2.capacity as newRoomCapacity
    FROM room_assignments ra
    JOIN rooms r1 ON ra.roomId = r1.id
    JOIN rooms r2 ON r2.id = ?
    WHERE ra.id = ? AND ra.status = 'active'
  `;

  db.query(getAssignmentQuery, [newRoomId, assignmentId], (err, results) => {
    if (err) {
      console.error('Error fetching assignment and room data:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching assignment and room data',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Active assignment or new room not found'
      });
    }

    const assignment = results[0];

    if (assignment.newRoomOccupancy >= assignment.newRoomCapacity) {
      return res.status(400).json({
        success: false,
        message: 'New room is at full capacity'
      });
    }

    // Update old assignment as transferred
    const updateOldAssignmentQuery = `
      UPDATE room_assignments
      SET status = 'transferred', notes = CONCAT(COALESCE(notes, ''), ?, ?)
      WHERE id = ?
    `;

    const transferNotes = `\n\nTransferred on: ${transferDate}` + (notes ? `\nTransfer Notes: ${notes}` : '');

    db.query(updateOldAssignmentQuery, ['\n\nTransfer Info: ', transferNotes, assignmentId], (err, updateResult) => {
      if (err) {
        console.error('Error updating old assignment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error updating old assignment',
          error: err.message
        });
      }

      // Create new assignment
      const createNewAssignmentQuery = `
        INSERT INTO room_assignments (roomId, nationalId, admissionDate, expectedDischargeDate, notes, assignedBy)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      const newAssignmentValues = [
        newRoomId,
        assignment.nationalId,
        transferDate,
        assignment.expectedDischargeDate,
        `Transferred from Room ${assignment.roomId}` + (notes ? `\nTransfer Notes: ${notes}` : ''),
        'System Transfer'
      ];

      db.query(createNewAssignmentQuery, newAssignmentValues, (err, newAssignmentResult) => {
        if (err) {
          console.error('Error creating new assignment:', err);
          return res.status(500).json({
            success: false,
            message: 'Error creating new assignment',
            error: err.message
          });
        }

        // Update old room occupancy
        const oldRoomNewOccupancy = Math.max(0, assignment.oldRoomOccupancy - 1);
        const oldRoomNewStatus = oldRoomNewOccupancy === 0 ? 'available' : 'occupied';

        // Update new room occupancy
        const newRoomNewOccupancy = assignment.newRoomOccupancy + 1;
        const newRoomNewStatus = newRoomNewOccupancy >= assignment.newRoomCapacity ? 'occupied' : 'available';

        // Update both rooms
        const updateOldRoomQuery = 'UPDATE rooms SET currentOccupancy = ?, status = ? WHERE id = ?';
        const updateNewRoomQuery = 'UPDATE rooms SET currentOccupancy = ?, status = ? WHERE id = ?';

        db.query(updateOldRoomQuery, [oldRoomNewOccupancy, oldRoomNewStatus, assignment.roomId], (err, oldRoomResult) => {
          if (err) {
            console.error('Error updating old room:', err);
            return res.status(500).json({
              success: false,
              message: 'Transfer completed but error updating old room',
              error: err.message
            });
          }

          db.query(updateNewRoomQuery, [newRoomNewOccupancy, newRoomNewStatus, newRoomId], (err, newRoomResult) => {
            if (err) {
              console.error('Error updating new room:', err);
              return res.status(500).json({
                success: false,
                message: 'Transfer completed but error updating new room',
                error: err.message
              });
            }

            res.json({
              success: true,
              message: 'Patient transferred successfully',
              data: {
                oldAssignmentId: assignmentId,
                newAssignmentId: newAssignmentResult.insertId,
                transferDate: transferDate,
                oldRoomId: assignment.roomId,
                newRoomId: newRoomId
              }
            });
          });
        });
      });
    });
  });
});

// Doctor Management API Routes

// GET all doctors
app.get('/api/doctors', (req, res) => {
  const query = 'SELECT * FROM doctors ORDER BY firstName ASC, lastName ASC';

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching doctors:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching doctors',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET doctors by specialization
app.get('/api/doctors/specialization/:specialization', (req, res) => {
  const specialization = req.params.specialization;
  const query = 'SELECT * FROM doctors WHERE specialization = ? ORDER BY firstName ASC, lastName ASC';

  db.query(query, [specialization], (err, results) => {
    if (err) {
      console.error('Error fetching doctors by specialization:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching doctors by specialization',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET doctors by department
app.get('/api/doctors/department/:department', (req, res) => {
  const department = req.params.department;
  const query = 'SELECT * FROM doctors WHERE department = ? ORDER BY firstName ASC, lastName ASC';

  db.query(query, [department], (err, results) => {
    if (err) {
      console.error('Error fetching doctors by department:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching doctors by department',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET doctors by status
app.get('/api/doctors/status/:status', (req, res) => {
  const status = req.params.status;
  const query = 'SELECT * FROM doctors WHERE status = ? ORDER BY firstName ASC, lastName ASC';

  db.query(query, [status], (err, results) => {
    if (err) {
      console.error('Error fetching doctors by status:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching doctors by status',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET single doctor by ID
app.get('/api/doctors/:id', (req, res) => {
  const doctorId = req.params.id;
  const query = 'SELECT * FROM doctors WHERE id = ? OR doctorId = ?';

  db.query(query, [doctorId, doctorId], (err, results) => {
    if (err) {
      console.error('Error fetching doctor:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching doctor',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Doctor not found'
      });
    }

    res.json({
      success: true,
      data: results[0]
    });
  });
});

// Search doctors
app.get('/api/doctors/search/:query', (req, res) => {
  const searchQuery = req.params.query;
  const searchPattern = `%${searchQuery}%`;

  const query = `
    SELECT * FROM doctors
    WHERE doctorId LIKE ?
    OR firstName LIKE ?
    OR lastName LIKE ?
    OR specialization LIKE ?
    OR department LIKE ?
    OR email LIKE ?
    ORDER BY firstName ASC, lastName ASC
  `;

  db.query(query, [searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern], (err, results) => {
    if (err) {
      console.error('Error searching doctors:', err);
      return res.status(500).json({
        success: false,
        message: 'Error searching doctors',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// Appointment Management API Routes

// GET all appointments
app.get('/api/appointments', (req, res) => {
  const query = `
    SELECT a.*,
           p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail,
           d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
    FROM appointments a
    JOIN patients p ON a.nationalId = p.nationalId
    JOIN doctors d ON a.doctorId = d.doctorId
    ORDER BY a.appointmentDate DESC, a.appointmentTime DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching appointments:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching appointments',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// POST create new appointment
app.post('/api/appointments', (req, res) => {
  const {
    nationalId,
    doctorId,
    appointmentDate,
    appointmentTime,
    duration,
    appointmentType,
    reason,
    symptoms,
    priority,
    notes,
    createdBy
  } = req.body;

  // Basic validation
  if (!nationalId || !doctorId || !appointmentDate || !appointmentTime || !reason) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: nationalId, doctorId, appointmentDate, appointmentTime, reason'
    });
  }

  // Generate appointment ID
  const generateAppointmentId = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `APT${timestamp}`;
  };

  const appointmentId = generateAppointmentId();

  // Check if patient exists
  const checkPatientQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkPatientQuery, [nationalId], (err, patientResults) => {
    if (err) {
      console.error('Error checking patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking patient',
        error: err.message
      });
    }

    if (patientResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Check if doctor exists
    const checkDoctorQuery = 'SELECT doctorId FROM doctors WHERE doctorId = ?';

    db.query(checkDoctorQuery, [doctorId], (err, doctorResults) => {
      if (err) {
        console.error('Error checking doctor:', err);
        return res.status(500).json({
          success: false,
          message: 'Error checking doctor',
          error: err.message
        });
      }

      if (doctorResults.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }

      // Create appointment
      const insertQuery = `
        INSERT INTO appointments
        (appointmentId, nationalId, doctorId, appointmentDate, appointmentTime, duration, appointmentType, reason, symptoms, priority, notes, createdBy)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        appointmentId,
        nationalId,
        doctorId,
        appointmentDate,
        appointmentTime,
        duration || 30,
        appointmentType || 'consultation',
        reason,
        symptoms || null,
        priority || 'medium',
        notes || null,
        createdBy || 'System'
      ];

      db.query(insertQuery, values, (err, result) => {
        if (err) {
          console.error('Error creating appointment:', err);
          return res.status(500).json({
            success: false,
            message: 'Error creating appointment',
            error: err.message
          });
        }

        // Get the created appointment with patient and doctor info
        const getAppointmentQuery = `
          SELECT a.*,
                 p.firstName as patientFirstName, p.lastName as patientLastName,
                 d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
          FROM appointments a
          JOIN patients p ON a.nationalId = p.nationalId
          JOIN doctors d ON a.doctorId = d.doctorId
          WHERE a.id = ?
        `;

        db.query(getAppointmentQuery, [result.insertId], (err, appointmentResults) => {
          if (err) {
            console.error('Error fetching created appointment:', err);
            return res.status(500).json({
              success: false,
              message: 'Appointment created but error fetching data',
              error: err.message
            });
          }

          res.status(201).json({
            success: true,
            message: 'Appointment created successfully',
            data: appointmentResults[0]
          });
        });
      });
    });
  });
});

// GET appointments by patient
app.get('/api/appointments/patient/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;

  const query = `
    SELECT a.*,
           d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
    FROM appointments a
    JOIN doctors d ON a.doctorId = d.doctorId
    WHERE a.nationalId = ?
    ORDER BY a.appointmentDate DESC, a.appointmentTime DESC
  `;

  db.query(query, [nationalId], (err, results) => {
    if (err) {
      console.error('Error fetching patient appointments:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient appointments',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET appointments by doctor
app.get('/api/appointments/doctor/:doctorId', (req, res) => {
  const doctorId = req.params.doctorId;

  const query = `
    SELECT a.*,
           p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail
    FROM appointments a
    JOIN patients p ON a.nationalId = p.nationalId
    WHERE a.doctorId = ?
    ORDER BY a.appointmentDate DESC, a.appointmentTime DESC
  `;

  db.query(query, [doctorId], (err, results) => {
    if (err) {
      console.error('Error fetching doctor appointments:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching doctor appointments',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// PUT approve appointment (for admin)
app.put('/api/appointments/:appointmentId/approve', (req, res) => {
  const appointmentId = req.params.appointmentId;
  const { adminNotes, processedBy } = req.body;

  if (!processedBy) {
    return res.status(400).json({
      success: false,
      message: 'processedBy is required'
    });
  }

  // Check if appointment exists
  const checkAppointmentQuery = 'SELECT * FROM appointments WHERE appointmentId = ?';

  db.query(checkAppointmentQuery, [appointmentId], (err, appointmentResults) => {
    if (err) {
      console.error('Error checking appointment:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking appointment',
        error: err.message
      });
    }

    if (appointmentResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found'
      });
    }

    const appointment = appointmentResults[0];

    // Update appointment status to confirmed
    const updateQuery = `
      UPDATE appointments
      SET status = 'confirmed', notes = CONCAT(COALESCE(notes, ''), ?, ?)
      WHERE appointmentId = ?
    `;

    const approvalNotes = `\n\nApproved by: ${processedBy} on ${new Date().toLocaleString()}`;
    const fullNotes = adminNotes ? `\nAdmin Notes: ${adminNotes}` : '';

    db.query(updateQuery, [fullNotes, approvalNotes, appointmentId], (err, result) => {
      if (err) {
        console.error('Error approving appointment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error approving appointment',
          error: err.message
        });
      }

      // Get the updated appointment with patient and doctor info
      const getAppointmentQuery = `
        SELECT a.*,
               p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail,
               d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
        FROM appointments a
        JOIN patients p ON a.nationalId = p.nationalId
        JOIN doctors d ON a.doctorId = d.doctorId
        WHERE a.appointmentId = ?
      `;

      db.query(getAppointmentQuery, [appointmentId], (err, updatedResults) => {
        if (err) {
          console.error('Error fetching updated appointment:', err);
          return res.status(500).json({
            success: false,
            message: 'Appointment approved but error fetching data',
            error: err.message
          });
        }

        res.json({
          success: true,
          message: 'Appointment approved successfully',
          data: updatedResults[0]
        });
      });
    });
  });
});

// PUT reject appointment (for admin)
app.put('/api/appointments/:appointmentId/reject', (req, res) => {
  const appointmentId = req.params.appointmentId;
  const { adminNotes, processedBy } = req.body;

  if (!processedBy) {
    return res.status(400).json({
      success: false,
      message: 'processedBy is required'
    });
  }

  if (!adminNotes || !adminNotes.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Admin notes are required for rejection'
    });
  }

  // Check if appointment exists
  const checkAppointmentQuery = 'SELECT * FROM appointments WHERE appointmentId = ?';

  db.query(checkAppointmentQuery, [appointmentId], (err, appointmentResults) => {
    if (err) {
      console.error('Error checking appointment:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking appointment',
        error: err.message
      });
    }

    if (appointmentResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found'
      });
    }

    const appointment = appointmentResults[0];

    // Update appointment status to cancelled
    const updateQuery = `
      UPDATE appointments
      SET status = 'cancelled', notes = CONCAT(COALESCE(notes, ''), ?, ?)
      WHERE appointmentId = ?
    `;

    const rejectionNotes = `\n\nRejected by: ${processedBy} on ${new Date().toLocaleString()}`;
    const fullNotes = `\nRejection Reason: ${adminNotes}`;

    db.query(updateQuery, [fullNotes, rejectionNotes, appointmentId], (err, result) => {
      if (err) {
        console.error('Error rejecting appointment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error rejecting appointment',
          error: err.message
        });
      }

      // Get the updated appointment with patient and doctor info
      const getAppointmentQuery = `
        SELECT a.*,
               p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail,
               d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
        FROM appointments a
        JOIN patients p ON a.nationalId = p.nationalId
        JOIN doctors d ON a.doctorId = d.doctorId
        WHERE a.appointmentId = ?
      `;

      db.query(getAppointmentQuery, [appointmentId], (err, updatedResults) => {
        if (err) {
          console.error('Error fetching updated appointment:', err);
          return res.status(500).json({
            success: false,
            message: 'Appointment rejected but error fetching data',
            error: err.message
          });
        }

        res.json({
          success: true,
          message: 'Appointment rejected successfully',
          data: updatedResults[0]
        });
      });
    });
  });
});

// Prescription Management API Routes

// GET all prescriptions
app.get('/api/prescriptions', (req, res) => {
  const query = `
    SELECT p.*,
           pt.firstName as patientFirstName, pt.lastName as patientLastName, pt.email as patientEmail,
           d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
    FROM prescriptions p
    JOIN patients pt ON p.nationalId = pt.nationalId
    JOIN doctors d ON p.doctorId = d.doctorId
    ORDER BY p.prescriptionDate DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching prescriptions:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching prescriptions',
        error: err.message
      });
    }

    // Parse medications JSON for each prescription
    const prescriptionsWithParsedMedications = results.map(prescription => ({
      ...prescription,
      medications: JSON.parse(prescription.medications)
    }));

    res.json({
      success: true,
      data: prescriptionsWithParsedMedications,
      count: prescriptionsWithParsedMedications.length
    });
  });
});

// GET prescriptions by patient
app.get('/api/prescriptions/patient/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;

  const query = `
    SELECT p.*,
           d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
    FROM prescriptions p
    JOIN doctors d ON p.doctorId = d.doctorId
    WHERE p.nationalId = ?
    ORDER BY p.prescriptionDate DESC
  `;

  db.query(query, [nationalId], (err, results) => {
    if (err) {
      console.error('Error fetching patient prescriptions:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient prescriptions',
        error: err.message
      });
    }

    // Parse medications JSON for each prescription
    const prescriptionsWithParsedMedications = results.map(prescription => ({
      ...prescription,
      medications: JSON.parse(prescription.medications)
    }));

    res.json({
      success: true,
      data: prescriptionsWithParsedMedications,
      count: prescriptionsWithParsedMedications.length
    });
  });
});

// POST create new prescription
app.post('/api/prescriptions', (req, res) => {
  const {
    nationalId,
    doctorId,
    prescriptionDate,
    diagnosis,
    medications,
    notes,
    validUntil,
    createdBy
  } = req.body;

  // Basic validation
  if (!nationalId || !doctorId || !prescriptionDate || !diagnosis || !medications) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: nationalId, doctorId, prescriptionDate, diagnosis, medications'
    });
  }

  // Generate prescription ID
  const generatePrescriptionId = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `RX${timestamp}`;
  };

  const prescriptionId = generatePrescriptionId();

  // Check if patient exists
  const checkPatientQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkPatientQuery, [nationalId], (err, patientResults) => {
    if (err) {
      console.error('Error checking patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking patient',
        error: err.message
      });
    }

    if (patientResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Check if doctor exists
    const checkDoctorQuery = 'SELECT doctorId FROM doctors WHERE doctorId = ?';

    db.query(checkDoctorQuery, [doctorId], (err, doctorResults) => {
      if (err) {
        console.error('Error checking doctor:', err);
        return res.status(500).json({
          success: false,
          message: 'Error checking doctor',
          error: err.message
        });
      }

      if (doctorResults.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Doctor not found'
        });
      }

      // Create prescription
      const insertQuery = `
        INSERT INTO prescriptions
        (prescriptionId, nationalId, doctorId, prescriptionDate, diagnosis, medications, notes, validUntil, createdBy)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        prescriptionId,
        nationalId,
        doctorId,
        prescriptionDate,
        diagnosis,
        JSON.stringify(medications),
        notes || null,
        validUntil || null,
        createdBy || 'System'
      ];

      db.query(insertQuery, values, (err, result) => {
        if (err) {
          console.error('Error creating prescription:', err);
          return res.status(500).json({
            success: false,
            message: 'Error creating prescription',
            error: err.message
          });
        }

        // Get the created prescription with patient and doctor info
        const getPrescriptionQuery = `
          SELECT p.*,
                 pt.firstName as patientFirstName, pt.lastName as patientLastName, pt.phone as patientPhone,
                 d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
          FROM prescriptions p
          JOIN patients pt ON p.nationalId = pt.nationalId
          JOIN doctors d ON p.doctorId = d.doctorId
          WHERE p.id = ?
        `;

        db.query(getPrescriptionQuery, [result.insertId], async (err, prescriptionResults) => {
          if (err) {
            console.error('Error fetching created prescription:', err);
            return res.status(500).json({
              success: false,
              message: 'Prescription created but error fetching data',
              error: err.message
            });
          }

          const prescriptionWithParsedMedications = {
            ...prescriptionResults[0],
            medications: JSON.parse(prescriptionResults[0].medications)
          };

          // Send SMS notification to patient
          try {
            const patientName = `${prescriptionWithParsedMedications.patientFirstName} ${prescriptionWithParsedMedications.patientLastName}`;
            const patientPhone = prescriptionWithParsedMedications.patientPhone;

            if (patientPhone) {
              const smsResult = await smsService.sendPrescriptionNotification(
                patientPhone,
                patientName,
                prescriptionWithParsedMedications
              );

              console.log('SMS notification result:', smsResult);

              // Add SMS status to response
              prescriptionWithParsedMedications.smsNotification = {
                sent: smsResult.success,
                message: smsResult.message,
                to: smsResult.to || patientPhone
              };
            } else {
              console.log('No phone number available for SMS notification');
              prescriptionWithParsedMedications.smsNotification = {
                sent: false,
                message: 'No phone number available',
                to: null
              };
            }
          } catch (smsError) {
            console.error('Error sending SMS notification:', smsError);
            prescriptionWithParsedMedications.smsNotification = {
              sent: false,
              message: 'SMS service error',
              error: smsError.message
            };
          }

          res.status(201).json({
            success: true,
            message: 'Prescription created successfully',
            data: prescriptionWithParsedMedications
          });
        });
      });
    });
  });
});

// Medical Records API - Get comprehensive patient data
app.get('/api/medical-records/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;

  // First get patient information
  const getPatientQuery = 'SELECT * FROM patients WHERE nationalId = ?';

  db.query(getPatientQuery, [nationalId], (err, patientResults) => {
    if (err) {
      console.error('Error fetching patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient data',
        error: err.message
      });
    }

    if (patientResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    const patient = patientResults[0];

    // Get patient's exams
    const getExamsQuery = `
      SELECT e.*, p.firstName, p.lastName, p.email
      FROM exams e
      JOIN patients p ON e.nationalId = p.nationalId
      WHERE e.nationalId = ?
      ORDER BY e.examDate DESC
    `;

    db.query(getExamsQuery, [nationalId], (err, examResults) => {
      if (err) {
        console.error('Error fetching patient exams:', err);
        return res.status(500).json({
          success: false,
          message: 'Error fetching patient exams',
          error: err.message
        });
      }

      // Get patient's prescriptions
      const getPrescriptionsQuery = `
        SELECT p.*,
               d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
        FROM prescriptions p
        JOIN doctors d ON p.doctorId = d.doctorId
        WHERE p.nationalId = ?
        ORDER BY p.prescriptionDate DESC
      `;

      db.query(getPrescriptionsQuery, [nationalId], (err, prescriptionResults) => {
        if (err) {
          console.error('Error fetching patient prescriptions:', err);
          return res.status(500).json({
            success: false,
            message: 'Error fetching patient prescriptions',
            error: err.message
          });
        }

        // Parse medications JSON for each prescription
        const prescriptionsWithParsedMedications = prescriptionResults.map(prescription => ({
          ...prescription,
          medications: JSON.parse(prescription.medications)
        }));

        // Get patient's appointments
        const getAppointmentsQuery = `
          SELECT a.*,
                 d.firstName as doctorFirstName, d.lastName as doctorLastName, d.specialization
          FROM appointments a
          JOIN doctors d ON a.doctorId = d.doctorId
          WHERE a.nationalId = ?
          ORDER BY a.appointmentDate DESC, a.appointmentTime DESC
        `;

        db.query(getAppointmentsQuery, [nationalId], (err, appointmentResults) => {
          if (err) {
            console.error('Error fetching patient appointments:', err);
            return res.status(500).json({
              success: false,
              message: 'Error fetching patient appointments',
              error: err.message
            });
          }

          // Get patient's room assignments
          const getRoomAssignmentsQuery = `
            SELECT ra.*, r.roomNumber, r.roomType, r.department, r.floor
            FROM room_assignments ra
            JOIN rooms r ON ra.roomId = r.id
            WHERE ra.nationalId = ?
            ORDER BY ra.createdAt DESC
          `;

          db.query(getRoomAssignmentsQuery, [nationalId], (err, roomResults) => {
            if (err) {
              console.error('Error fetching patient room assignments:', err);
              return res.status(500).json({
                success: false,
                message: 'Error fetching patient room assignments',
                error: err.message
              });
            }

            // Compile comprehensive medical record
            const medicalRecord = {
              patient: patient,
              exams: examResults,
              prescriptions: prescriptionsWithParsedMedications,
              appointments: appointmentResults,
              roomAssignments: roomResults,
              summary: {
                totalExams: examResults.length,
                totalPrescriptions: prescriptionsWithParsedMedications.length,
                totalAppointments: appointmentResults.length,
                totalRoomAssignments: roomResults.length,
                lastExam: examResults.length > 0 ? examResults[0].examDate : null,
                lastPrescription: prescriptionsWithParsedMedications.length > 0 ? prescriptionsWithParsedMedications[0].prescriptionDate : null,
                lastAppointment: appointmentResults.length > 0 ? appointmentResults[0].appointmentDate : null
              }
            };

            res.json({
              success: true,
              data: medicalRecord
            });
          });
        });
      });
    });
  });
});

// Hospital Transfer Management API Routes

// GET all transfer requests
app.get('/api/hospital-transfers', (req, res) => {
  const query = `
    SELECT ht.*,
           p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail, p.phone as patientPhone
    FROM hospital_transfers ht
    JOIN patients p ON ht.nationalId = p.nationalId
    ORDER BY ht.createdAt DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching transfer requests:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching transfer requests',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET transfer requests by status (for admin dashboard)
app.get('/api/hospital-transfers/status/:status', (req, res) => {
  const status = req.params.status;

  const query = `
    SELECT ht.*,
           p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail, p.phone as patientPhone
    FROM hospital_transfers ht
    JOIN patients p ON ht.nationalId = p.nationalId
    WHERE ht.status = ?
    ORDER BY ht.urgencyLevel DESC, ht.createdAt DESC
  `;

  db.query(query, [status], (err, results) => {
    if (err) {
      console.error('Error fetching transfer requests by status:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching transfer requests by status',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET transfer requests by patient
app.get('/api/hospital-transfers/patient/:nationalId', (req, res) => {
  const nationalId = req.params.nationalId;

  const query = `
    SELECT ht.*
    FROM hospital_transfers ht
    WHERE ht.nationalId = ?
    ORDER BY ht.createdAt DESC
  `;

  db.query(query, [nationalId], (err, results) => {
    if (err) {
      console.error('Error fetching patient transfer requests:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching patient transfer requests',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// POST create new transfer request
app.post('/api/hospital-transfers', (req, res) => {
  const {
    nationalId,
    currentHospital,
    targetHospital,
    transferReason,
    medicalCondition,
    urgencyLevel,
    requestedBy,
    requestedByRole,
    contactNumber,
    preferredDate,
    preferredTime,
    transportMethod,
    specialRequirements,
    patientConsent,
    familyNotified
  } = req.body;

  // Basic validation
  if (!nationalId || !currentHospital || !targetHospital || !transferReason || !medicalCondition || !requestedBy || !contactNumber) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: nationalId, currentHospital, targetHospital, transferReason, medicalCondition, requestedBy, contactNumber'
    });
  }

  // Generate transfer ID
  const generateTransferId = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `TRF${timestamp}`;
  };

  const transferId = generateTransferId();

  // Check if patient exists
  const checkPatientQuery = 'SELECT nationalId FROM patients WHERE nationalId = ?';

  db.query(checkPatientQuery, [nationalId], (err, patientResults) => {
    if (err) {
      console.error('Error checking patient:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking patient',
        error: err.message
      });
    }

    if (patientResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Create transfer request
    const insertQuery = `
      INSERT INTO hospital_transfers
      (transferId, nationalId, currentHospital, targetHospital, transferReason, medicalCondition, urgencyLevel, requestedBy, requestedByRole, contactNumber, preferredDate, preferredTime, transportMethod, specialRequirements, patientConsent, familyNotified)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      transferId,
      nationalId,
      currentHospital,
      targetHospital,
      transferReason,
      medicalCondition,
      urgencyLevel || 'medium',
      requestedBy,
      requestedByRole || 'doctor',
      contactNumber,
      preferredDate || null,
      preferredTime || null,
      transportMethod || 'ambulance',
      specialRequirements || null,
      patientConsent || false,
      familyNotified || false
    ];

    db.query(insertQuery, values, (err, result) => {
      if (err) {
        console.error('Error creating transfer request:', err);
        return res.status(500).json({
          success: false,
          message: 'Error creating transfer request',
          error: err.message
        });
      }

      // Get the created transfer request with patient info
      const getTransferQuery = `
        SELECT ht.*,
               p.firstName as patientFirstName, p.lastName as patientLastName, p.email as patientEmail
        FROM hospital_transfers ht
        JOIN patients p ON ht.nationalId = p.nationalId
        WHERE ht.id = ?
      `;

      db.query(getTransferQuery, [result.insertId], (err, transferResults) => {
        if (err) {
          console.error('Error fetching created transfer request:', err);
          return res.status(500).json({
            success: false,
            message: 'Transfer request created but error fetching data',
            error: err.message
          });
        }

        res.status(201).json({
          success: true,
          message: 'Transfer request created successfully',
          data: transferResults[0]
        });
      });
    });
  });
});

// PUT approve transfer request (for admin)
app.put('/api/hospital-transfers/:transferId/approve', (req, res) => {
  const transferId = req.params.transferId;
  const { approvedBy, adminNotes } = req.body;

  if (!approvedBy) {
    return res.status(400).json({
      success: false,
      message: 'approvedBy is required'
    });
  }

  const updateQuery = `
    UPDATE hospital_transfers
    SET status = 'approved', approvedBy = ?, approvedAt = NOW(), adminNotes = ?
    WHERE transferId = ?
  `;

  db.query(updateQuery, [approvedBy, adminNotes || null, transferId], (err, result) => {
    if (err) {
      console.error('Error approving transfer request:', err);
      return res.status(500).json({
        success: false,
        message: 'Error approving transfer request',
        error: err.message
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Transfer request not found'
      });
    }

    // Get the updated transfer request
    const getTransferQuery = `
      SELECT ht.*,
             p.firstName as patientFirstName, p.lastName as patientLastName
      FROM hospital_transfers ht
      JOIN patients p ON ht.nationalId = p.nationalId
      WHERE ht.transferId = ?
    `;

    db.query(getTransferQuery, [transferId], (err, transferResults) => {
      if (err) {
        console.error('Error fetching updated transfer request:', err);
        return res.status(500).json({
          success: false,
          message: 'Transfer approved but error fetching data',
          error: err.message
        });
      }

      res.json({
        success: true,
        message: 'Transfer request approved successfully',
        data: transferResults[0]
      });
    });
  });
});

// PUT reject transfer request (for admin)
app.put('/api/hospital-transfers/:transferId/reject', (req, res) => {
  const transferId = req.params.transferId;
  const { rejectedBy, rejectionReason } = req.body;

  if (!rejectedBy || !rejectionReason) {
    return res.status(400).json({
      success: false,
      message: 'rejectedBy and rejectionReason are required'
    });
  }

  const updateQuery = `
    UPDATE hospital_transfers
    SET status = 'rejected', rejectedBy = ?, rejectedAt = NOW(), rejectionReason = ?
    WHERE transferId = ?
  `;

  db.query(updateQuery, [rejectedBy, rejectionReason, transferId], (err, result) => {
    if (err) {
      console.error('Error rejecting transfer request:', err);
      return res.status(500).json({
        success: false,
        message: 'Error rejecting transfer request',
        error: err.message
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Transfer request not found'
      });
    }

    // Get the updated transfer request
    const getTransferQuery = `
      SELECT ht.*,
             p.firstName as patientFirstName, p.lastName as patientLastName
      FROM hospital_transfers ht
      JOIN patients p ON ht.nationalId = p.nationalId
      WHERE ht.transferId = ?
    `;

    db.query(getTransferQuery, [transferId], (err, transferResults) => {
      if (err) {
        console.error('Error fetching updated transfer request:', err);
        return res.status(500).json({
          success: false,
          message: 'Transfer rejected but error fetching data',
          error: err.message
        });
      }

      res.json({
        success: true,
        message: 'Transfer request rejected successfully',
        data: transferResults[0]
      });
    });
  });
});

// Utility Management API Routes

// GET all utilities
app.get('/api/utilities', (req, res) => {
  const query = 'SELECT * FROM utilities ORDER BY supplyType ASC, supplyName ASC';

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching utilities:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching utilities',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET utilities by type
app.get('/api/utilities/type/:type', (req, res) => {
  const supplyType = req.params.type;
  const query = 'SELECT * FROM utilities WHERE supplyType = ? ORDER BY supplyName ASC';

  db.query(query, [supplyType], (err, results) => {
    if (err) {
      console.error('Error fetching utilities by type:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching utilities by type',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET utilities by status
app.get('/api/utilities/status/:status', (req, res) => {
  const status = req.params.status;
  const query = 'SELECT * FROM utilities WHERE status = ? ORDER BY priority DESC, supplyName ASC';

  db.query(query, [status], (err, results) => {
    if (err) {
      console.error('Error fetching utilities by status:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching utilities by status',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET single utility by ID
app.get('/api/utilities/:id', (req, res) => {
  const supplyId = req.params.id;
  const query = 'SELECT * FROM utilities WHERE supplyId = ?';

  db.query(query, [supplyId], (err, results) => {
    if (err) {
      console.error('Error fetching utility:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching utility',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utility not found'
      });
    }

    res.json({
      success: true,
      data: results[0]
    });
  });
});

// POST create new utility
app.post('/api/utilities', (req, res) => {
  const {
    utilityName,
    utilityType,
    location,
    department,
    status,
    priority,
    description,
    lastMaintenanceDate,
    nextMaintenanceDate,
    maintenanceInterval,
    responsiblePerson,
    contactNumber,
    vendor,
    warrantyExpiry,
    installationDate,
    cost,
    energyRating,
    notes
  } = req.body;

  // Basic validation
  if (!utilityName || !utilityType || !location) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: utilityName, utilityType, location'
    });
  }

  // Generate utility ID
  const generateUtilityId = () => {
    const typePrefix = utilityType.toUpperCase().substring(0, 3);
    const timestamp = Date.now().toString().slice(-3);
    return `${typePrefix}${timestamp}`;
  };

  const utilityId = generateUtilityId();

  const insertQuery = `
    INSERT INTO utilities
    (utilityId, utilityName, utilityType, location, department, status, priority, description, lastMaintenanceDate, nextMaintenanceDate, maintenanceInterval, responsiblePerson, contactNumber, vendor, warrantyExpiry, installationDate, cost, energyRating, notes, createdBy, updatedBy)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const values = [
    utilityId,
    utilityName,
    utilityType,
    location,
    department || null,
    status || 'operational',
    priority || 'medium',
    description || null,
    lastMaintenanceDate || null,
    nextMaintenanceDate || null,
    maintenanceInterval || 30,
    responsiblePerson || null,
    contactNumber || null,
    vendor || null,
    warrantyExpiry || null,
    installationDate || null,
    cost || null,
    energyRating || null,
    notes || null,
    'Admin User',
    'Admin User'
  ];

  db.query(insertQuery, values, (err, result) => {
    if (err) {
      console.error('Error creating utility:', err);
      return res.status(500).json({
        success: false,
        message: 'Error creating utility',
        error: err.message
      });
    }

    // Get the created utility
    const getUtilityQuery = 'SELECT * FROM utilities WHERE utilityId = ?';
    db.query(getUtilityQuery, [utilityId], (err, utilityResults) => {
      if (err) {
        console.error('Error fetching created utility:', err);
        return res.status(500).json({
          success: false,
          message: 'Utility created but error fetching data',
          error: err.message
        });
      }

      res.status(201).json({
        success: true,
        message: 'Utility created successfully',
        data: utilityResults[0]
      });
    });
  });
});

// PUT update utility status
app.put('/api/utilities/:id/status', (req, res) => {
  const supplyId = req.params.id;
  const { status, notes, updatedBy } = req.body;

  if (!status) {
    return res.status(400).json({
      success: false,
      message: 'Status is required'
    });
  }

  // Check if utility exists
  const checkUtilityQuery = 'SELECT * FROM utilities WHERE supplyId = ?';

  db.query(checkUtilityQuery, [supplyId], (err, results) => {
    if (err) {
      console.error('Error checking utility:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking utility',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utility not found'
      });
    }

    const statusNotes = notes ? `\nStatus Update: ${status} - ${notes}\nUpdated on: ${new Date().toLocaleString()}` : `\nStatus Update: ${status}\nUpdated on: ${new Date().toLocaleString()}`;

    const updateQuery = `
      UPDATE utilities
      SET status = ?,
          notes = CONCAT(COALESCE(notes, ''), ?),
          updatedBy = ?
      WHERE supplyId = ?
    `;

    db.query(updateQuery, [status, statusNotes, updatedBy || 'Admin User', supplyId], (err, result) => {
      if (err) {
        console.error('Error updating utility status:', err);
        return res.status(500).json({
          success: false,
          message: 'Error updating utility status',
          error: err.message
        });
      }

      // Get the updated utility
      const getUtilityQuery = 'SELECT * FROM utilities WHERE supplyId = ?';
      db.query(getUtilityQuery, [supplyId], (err, updatedResults) => {
        if (err) {
          console.error('Error fetching updated utility:', err);
          return res.status(500).json({
            success: false,
            message: 'Utility updated but error fetching data',
            error: err.message
          });
        }

        res.json({
          success: true,
          message: 'Utility status updated successfully',
          data: updatedResults[0]
        });
      });
    });
  });
});

// Stock Management API Routes

// POST add stock in (receive new stock)
app.post('/api/stock/add', (req, res) => {
  const {
    supplyId,
    quantity,
    unitCost,
    supplier,
    batchNumber,
    expiryDate,
    invoiceNumber,
    deliveryDate,
    receivedBy,
    notes
  } = req.body;

  // Basic validation
  if (!supplyId || !quantity || quantity <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Supply ID and positive quantity are required'
    });
  }

  // Check if supply exists
  const checkSupplyQuery = 'SELECT * FROM utilities WHERE supplyId = ?';

  db.query(checkSupplyQuery, [supplyId], (err, supplyResults) => {
    if (err) {
      console.error('Error checking supply:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking supply',
        error: err.message
      });
    }

    if (supplyResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Supply not found'
      });
    }

    const supply = supplyResults[0];
    const previousStock = supply.currentStock;
    const newStock = previousStock + parseInt(quantity);
    const totalCost = unitCost ? (unitCost * quantity) : null;

    // Generate transaction ID
    const generateTransactionId = () => {
      const timestamp = Date.now().toString().slice(-6);
      return `STK${timestamp}`;
    };

    const transactionId = generateTransactionId();

    // Start transaction
    db.beginTransaction((err) => {
      if (err) {
        console.error('Error starting transaction:', err);
        return res.status(500).json({
          success: false,
          message: 'Error starting transaction',
          error: err.message
        });
      }

      // Insert stock transaction record
      const insertTransactionQuery = `
        INSERT INTO stock_transactions
        (transactionId, supplyId, transactionType, quantity, unitOfMeasure, previousStock, newStock, unitCost, totalCost, supplier, batchNumber, expiryDate, invoiceNumber, deliveryDate, receivedBy, notes, createdBy)
        VALUES (?, ?, 'stock_in', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const transactionValues = [
        transactionId,
        supplyId,
        quantity,
        supply.unitOfMeasure,
        previousStock,
        newStock,
        unitCost || null,
        totalCost,
        supplier || null,
        batchNumber || null,
        expiryDate || null,
        invoiceNumber || null,
        deliveryDate || null,
        receivedBy || 'Admin User',
        notes || null,
        'Admin User'
      ];

      db.query(insertTransactionQuery, transactionValues, (err, transactionResult) => {
        if (err) {
          return db.rollback(() => {
            console.error('Error inserting stock transaction:', err);
            res.status(500).json({
              success: false,
              message: 'Error recording stock transaction',
              error: err.message
            });
          });
        }

        // Update supply stock and status
        let newStatus = supply.status;
        if (newStock >= supply.minimumStock && supply.status === 'out_of_stock') {
          newStatus = 'in_stock';
        } else if (newStock >= supply.minimumStock && supply.status === 'low_stock') {
          newStatus = 'in_stock';
        }

        // Update total value if unit cost provided
        const newTotalValue = unitCost ? (newStock * unitCost) : supply.totalValue;

        const updateSupplyQuery = `
          UPDATE utilities
          SET currentStock = ?,
              status = ?,
              totalValue = ?,
              lastRestocked = CURRENT_DATE,
              updatedBy = ?
          WHERE supplyId = ?
        `;

        db.query(updateSupplyQuery, [newStock, newStatus, newTotalValue, 'Admin User', supplyId], (err, updateResult) => {
          if (err) {
            return db.rollback(() => {
              console.error('Error updating supply stock:', err);
              res.status(500).json({
                success: false,
                message: 'Error updating supply stock',
                error: err.message
              });
            });
          }

          // Commit transaction
          db.commit((err) => {
            if (err) {
              return db.rollback(() => {
                console.error('Error committing transaction:', err);
                res.status(500).json({
                  success: false,
                  message: 'Error committing transaction',
                  error: err.message
                });
              });
            }

            // Get updated supply info
            const getUpdatedSupplyQuery = 'SELECT * FROM utilities WHERE supplyId = ?';
            db.query(getUpdatedSupplyQuery, [supplyId], (err, updatedSupplyResults) => {
              if (err) {
                console.error('Error fetching updated supply:', err);
                return res.status(500).json({
                  success: false,
                  message: 'Stock added but error fetching updated data',
                  error: err.message
                });
              }

              res.status(201).json({
                success: true,
                message: `Successfully added ${quantity} ${supply.unitOfMeasure} to ${supply.supplyName}`,
                data: {
                  transaction: {
                    transactionId,
                    supplyId,
                    quantity,
                    previousStock,
                    newStock,
                    totalCost
                  },
                  supply: updatedSupplyResults[0]
                }
              });
            });
          });
        });
      });
    });
  });
});

// POST remove stock (stock out)
app.post('/api/stock/remove', (req, res) => {
  const {
    supplyId,
    quantity,
    reason,
    usedBy,
    department,
    patientId,
    notes
  } = req.body;

  // Basic validation
  if (!supplyId || !quantity || quantity <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Supply ID and positive quantity are required'
    });
  }

  // Check if supply exists
  const checkSupplyQuery = 'SELECT * FROM utilities WHERE supplyId = ?';

  db.query(checkSupplyQuery, [supplyId], (err, supplyResults) => {
    if (err) {
      console.error('Error checking supply:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking supply',
        error: err.message
      });
    }

    if (supplyResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Supply not found'
      });
    }

    const supply = supplyResults[0];
    const previousStock = supply.currentStock;
    const requestedQuantity = parseInt(quantity);

    // Check if enough stock available
    if (previousStock < requestedQuantity) {
      return res.status(400).json({
        success: false,
        message: `Insufficient stock. Available: ${previousStock} ${supply.unitOfMeasure}, Requested: ${requestedQuantity} ${supply.unitOfMeasure}`
      });
    }

    const newStock = previousStock - requestedQuantity;

    // Generate transaction ID
    const generateTransactionId = () => {
      const timestamp = Date.now().toString().slice(-6);
      return `OUT${timestamp}`;
    };

    const transactionId = generateTransactionId();

    // Start transaction
    db.beginTransaction((err) => {
      if (err) {
        console.error('Error starting transaction:', err);
        return res.status(500).json({
          success: false,
          message: 'Error starting transaction',
          error: err.message
        });
      }

      // Insert stock transaction record
      const insertTransactionQuery = `
        INSERT INTO stock_transactions
        (transactionId, supplyId, transactionType, quantity, unitOfMeasure, previousStock, newStock, reason, createdBy, notes)
        VALUES (?, ?, 'stock_out', ?, ?, ?, ?, ?, ?, ?)
      `;

      const transactionValues = [
        transactionId,
        supplyId,
        requestedQuantity,
        supply.unitOfMeasure,
        previousStock,
        newStock,
        reason || 'Stock usage',
        usedBy || 'Admin User',
        notes || null
      ];

      db.query(insertTransactionQuery, transactionValues, (err, transactionResult) => {
        if (err) {
          return db.rollback(() => {
            console.error('Error inserting stock transaction:', err);
            res.status(500).json({
              success: false,
              message: 'Error recording stock transaction',
              error: err.message
            });
          });
        }

        // Update supply stock and status
        let newStatus = supply.status;
        if (newStock === 0) {
          newStatus = 'out_of_stock';
        } else if (newStock <= supply.minimumStock && supply.status === 'in_stock') {
          newStatus = 'low_stock';
        }

        const updateSupplyQuery = `
          UPDATE utilities
          SET currentStock = ?,
              status = ?,
              updatedBy = ?
          WHERE supplyId = ?
        `;

        db.query(updateSupplyQuery, [newStock, newStatus, 'Admin User', supplyId], (err, updateResult) => {
          if (err) {
            return db.rollback(() => {
              console.error('Error updating supply stock:', err);
              res.status(500).json({
                success: false,
                message: 'Error updating supply stock',
                error: err.message
              });
            });
          }

          // Commit transaction
          db.commit((err) => {
            if (err) {
              return db.rollback(() => {
                console.error('Error committing transaction:', err);
                res.status(500).json({
                  success: false,
                  message: 'Error committing transaction',
                  error: err.message
                });
              });
            }

            // Get updated supply info
            const getUpdatedSupplyQuery = 'SELECT * FROM utilities WHERE supplyId = ?';
            db.query(getUpdatedSupplyQuery, [supplyId], (err, updatedSupplyResults) => {
              if (err) {
                console.error('Error fetching updated supply:', err);
                return res.status(500).json({
                  success: false,
                  message: 'Stock removed but error fetching updated data',
                  error: err.message
                });
              }

              res.status(200).json({
                success: true,
                message: `Successfully removed ${requestedQuantity} ${supply.unitOfMeasure} from ${supply.supplyName}`,
                data: {
                  transaction: {
                    transactionId,
                    supplyId,
                    quantity: requestedQuantity,
                    previousStock,
                    newStock,
                    reason: reason || 'Stock usage'
                  },
                  supply: updatedSupplyResults[0]
                }
              });
            });
          });
        });
      });
    });
  });
});

// GET all stock transactions
app.get('/api/stock/transactions', (req, res) => {
  const query = `
    SELECT st.*,
           u.supplyName, u.supplyType, u.location, u.department
    FROM stock_transactions st
    JOIN utilities u ON st.supplyId = u.supplyId
    ORDER BY st.createdAt DESC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching stock transactions:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching stock transactions',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET stock transactions for a specific supply
app.get('/api/stock/transactions/:supplyId', (req, res) => {
  const supplyId = req.params.supplyId;
  const query = `
    SELECT st.*,
           u.supplyName, u.supplyType, u.location, u.department
    FROM stock_transactions st
    JOIN utilities u ON st.supplyId = u.supplyId
    WHERE st.supplyId = ?
    ORDER BY st.createdAt DESC
  `;

  db.query(query, [supplyId], (err, results) => {
    if (err) {
      console.error('Error fetching supply transactions:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching supply transactions',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET expiring supplies (expiry notifications)
app.get('/api/supplies/expiring', (req, res) => {
  const daysAhead = req.query.days || 30; // Default to 30 days

  const query = `
    SELECT *,
           DATEDIFF(expiryDate, CURDATE()) as daysUntilExpiry
    FROM utilities
    WHERE expiryDate IS NOT NULL
      AND expiryDate <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
      AND expiryDate >= CURDATE()
    ORDER BY expiryDate ASC
  `;

  db.query(query, [daysAhead], (err, results) => {
    if (err) {
      console.error('Error fetching expiring supplies:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching expiring supplies',
        error: err.message
      });
    }

    // Categorize by urgency
    const categorized = {
      expired: results.filter(item => item.daysUntilExpiry < 0),
      critical: results.filter(item => item.daysUntilExpiry >= 0 && item.daysUntilExpiry <= 7),
      warning: results.filter(item => item.daysUntilExpiry > 7 && item.daysUntilExpiry <= 30),
      upcoming: results.filter(item => item.daysUntilExpiry > 30)
    };

    res.json({
      success: true,
      data: {
        all: results,
        categorized: categorized,
        counts: {
          total: results.length,
          expired: categorized.expired.length,
          critical: categorized.critical.length,
          warning: categorized.warning.length,
          upcoming: categorized.upcoming.length
        }
      }
    });
  });
});



// GET low stock supplies
app.get('/api/supplies/low-stock', (req, res) => {
  const query = `
    SELECT *,
           (currentStock / minimumStock * 100) as stockPercentage
    FROM utilities
    WHERE currentStock <= minimumStock
    ORDER BY stockPercentage ASC, supplyName ASC
  `;

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching low stock supplies:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching low stock supplies',
        error: err.message
      });
    }

    // Categorize by stock level
    const categorized = {
      outOfStock: results.filter(item => item.currentStock === 0),
      critical: results.filter(item => item.currentStock > 0 && item.stockPercentage <= 25),
      low: results.filter(item => item.stockPercentage > 25 && item.stockPercentage <= 50),
      moderate: results.filter(item => item.stockPercentage > 50 && item.stockPercentage <= 100)
    };

    res.json({
      success: true,
      data: {
        all: results,
        categorized: categorized,
        counts: {
          total: results.length,
          outOfStock: categorized.outOfStock.length,
          critical: categorized.critical.length,
          low: categorized.low.length,
          moderate: categorized.moderate.length
        }
      }
    });
  });
});

// Authentication API Routes

// POST login
app.post('/api/auth/login', (req, res) => {
  const { email, password, userType } = req.body;

  // Basic validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  // Query to find user
  const query = 'SELECT * FROM users WHERE email = ? AND isActive = TRUE';

  db.query(query, [email], (err, results) => {
    if (err) {
      console.error('Error during login:', err);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    const user = results[0];

    // Simple password check (in production, use bcrypt)
    if (user.password !== password) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check user type if specified
    if (userType && user.userType !== userType) {
      return res.status(401).json({
        success: false,
        message: `Access denied. This login is for ${userType} users only.`
      });
    }

    // Update last login
    const updateLoginQuery = 'UPDATE users SET lastLogin = NOW() WHERE id = ?';
    db.query(updateLoginQuery, [user.id], (updateErr) => {
      if (updateErr) {
        console.error('Error updating last login:', updateErr);
      }
    });

    // Create user response (exclude password)
    const userResponse = {
      id: user.id,
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      userType: user.userType,
      department: user.department,
      specialization: user.specialization,
      phone: user.phone
    };

    // Generate simple token (in production, use JWT)
    const token = `token_${user.id}_${Date.now()}`;

    res.json({
      success: true,
      message: 'Login successful',
      user: userResponse,
      token: token
    });
  });
});

// POST logout
app.post('/api/auth/logout', (req, res) => {
  // In a real application, you would invalidate the token here
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

// GET current user (verify token)
app.get('/api/auth/me', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }

  const token = authHeader.substring(7);

  // Simple token validation (in production, use JWT verification)
  if (!token.startsWith('token_')) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }

  // Extract user ID from token
  const tokenParts = token.split('_');
  if (tokenParts.length < 3) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token format'
    });
  }

  const userId = tokenParts[1];

  // Get user from database
  const query = 'SELECT * FROM users WHERE id = ? AND isActive = TRUE';

  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error('Error verifying token:', err);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }

    if (results.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    const user = results[0];

    // Create user response (exclude password)
    const userResponse = {
      id: user.id,
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      userType: user.userType,
      department: user.department,
      specialization: user.specialization,
      phone: user.phone
    };

    res.json({
      success: true,
      user: userResponse
    });
  });
});

// GET all users (admin only)
app.get('/api/users', (req, res) => {
  const query = 'SELECT id, email, firstName, lastName, role, userType, department, specialization, phone, isActive, lastLogin, createdAt FROM users ORDER BY firstName ASC, lastName ASC';

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching users:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching users',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// Messaging System API Routes

// GET messages for a user (inbox)
app.get('/api/messages/inbox/:userId', (req, res) => {
  const userId = req.params.userId;

  const query = `
    SELECT m.*,
           s.firstName as senderFirstName, s.lastName as senderLastName, s.role as senderRole, s.department as senderDepartment,
           r.firstName as receiverFirstName, r.lastName as receiverLastName, r.role as receiverRole
    FROM messages m
    JOIN users s ON m.senderId = s.id
    JOIN users r ON m.receiverId = r.id
    WHERE m.receiverId = ? AND m.isArchived = FALSE
    ORDER BY m.sentAt DESC
  `;

  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error('Error fetching inbox messages:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching inbox messages',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET sent messages for a user
app.get('/api/messages/sent/:userId', (req, res) => {
  const userId = req.params.userId;

  const query = `
    SELECT m.*,
           s.firstName as senderFirstName, s.lastName as senderLastName, s.role as senderRole,
           r.firstName as receiverFirstName, r.lastName as receiverLastName, r.role as receiverRole, r.department as receiverDepartment
    FROM messages m
    JOIN users s ON m.senderId = s.id
    JOIN users r ON m.receiverId = r.id
    WHERE m.senderId = ?
    ORDER BY m.sentAt DESC
  `;

  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error('Error fetching sent messages:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching sent messages',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET starred messages for a user
app.get('/api/messages/starred/:userId', (req, res) => {
  const userId = req.params.userId;

  const query = `
    SELECT m.*,
           s.firstName as senderFirstName, s.lastName as senderLastName, s.role as senderRole,
           r.firstName as receiverFirstName, r.lastName as receiverLastName, r.role as receiverRole
    FROM messages m
    JOIN users s ON m.senderId = s.id
    JOIN users r ON m.receiverId = r.id
    WHERE (m.senderId = ? OR m.receiverId = ?) AND m.isStarred = TRUE
    ORDER BY m.sentAt DESC
  `;

  db.query(query, [userId, userId], (err, results) => {
    if (err) {
      console.error('Error fetching starred messages:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching starred messages',
        error: err.message
      });
    }

    res.json({
      success: true,
      data: results,
      count: results.length
    });
  });
});

// GET unread message count for a user
app.get('/api/messages/unread-count/:userId', (req, res) => {
  const userId = req.params.userId;

  const query = 'SELECT COUNT(*) as count FROM messages WHERE receiverId = ? AND isRead = FALSE AND isArchived = FALSE';

  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error('Error fetching unread count:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching unread count',
        error: err.message
      });
    }

    res.json({
      success: true,
      count: results[0].count
    });
  });
});

// POST send new message
app.post('/api/messages', (req, res) => {
  const {
    senderId,
    receiverId,
    subject,
    content,
    messageType,
    priority,
    patientId,
    replyToId
  } = req.body;

  // Basic validation
  if (!senderId || !receiverId || !subject || !content) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: senderId, receiverId, subject, content'
    });
  }

  // Generate message ID
  const generateMessageId = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `MSG${timestamp}`;
  };

  const messageId = generateMessageId();

  // Check if sender and receiver exist
  const checkUsersQuery = 'SELECT id FROM users WHERE id IN (?, ?) AND isActive = TRUE';

  db.query(checkUsersQuery, [senderId, receiverId], (err, userResults) => {
    if (err) {
      console.error('Error checking users:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking users',
        error: err.message
      });
    }

    if (userResults.length !== 2) {
      return res.status(404).json({
        success: false,
        message: 'Sender or receiver not found or inactive'
      });
    }

    // Create message
    const insertQuery = `
      INSERT INTO messages
      (messageId, senderId, receiverId, subject, content, messageType, priority, patientId, replyToId)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      messageId,
      senderId,
      receiverId,
      subject,
      content,
      messageType || 'general',
      priority || 'normal',
      patientId || null,
      replyToId || null
    ];

    db.query(insertQuery, values, (err, result) => {
      if (err) {
        console.error('Error creating message:', err);
        return res.status(500).json({
          success: false,
          message: 'Error creating message',
          error: err.message
        });
      }

      // Get the created message with user info
      const getMessageQuery = `
        SELECT m.*,
               s.firstName as senderFirstName, s.lastName as senderLastName, s.role as senderRole,
               r.firstName as receiverFirstName, r.lastName as receiverLastName, r.role as receiverRole
        FROM messages m
        JOIN users s ON m.senderId = s.id
        JOIN users r ON m.receiverId = r.id
        WHERE m.id = ?
      `;

      db.query(getMessageQuery, [result.insertId], (err, messageResults) => {
        if (err) {
          console.error('Error fetching created message:', err);
          return res.status(500).json({
            success: false,
            message: 'Message created but error fetching data',
            error: err.message
          });
        }

        res.status(201).json({
          success: true,
          message: 'Message sent successfully',
          data: messageResults[0]
        });
      });
    });
  });
});

// PUT mark message as read
app.put('/api/messages/:messageId/read', (req, res) => {
  const messageId = req.params.messageId;
  const { userId } = req.body;

  const updateQuery = 'UPDATE messages SET isRead = TRUE, readAt = NOW() WHERE id = ? AND receiverId = ?';

  db.query(updateQuery, [messageId, userId], (err, result) => {
    if (err) {
      console.error('Error marking message as read:', err);
      return res.status(500).json({
        success: false,
        message: 'Error marking message as read',
        error: err.message
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or you are not the recipient'
      });
    }

    res.json({
      success: true,
      message: 'Message marked as read'
    });
  });
});

// PUT toggle star message
app.put('/api/messages/:messageId/star', (req, res) => {
  const messageId = req.params.messageId;
  const { userId, isStarred } = req.body;

  const updateQuery = 'UPDATE messages SET isStarred = ? WHERE id = ? AND (senderId = ? OR receiverId = ?)';

  db.query(updateQuery, [isStarred, messageId, userId, userId], (err, result) => {
    if (err) {
      console.error('Error updating message star status:', err);
      return res.status(500).json({
        success: false,
        message: 'Error updating message star status',
        error: err.message
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or you do not have access'
      });
    }

    res.json({
      success: true,
      message: isStarred ? 'Message starred' : 'Message unstarred'
    });
  });
});

// PUT archive message
app.put('/api/messages/:messageId/archive', (req, res) => {
  const messageId = req.params.messageId;
  const { userId } = req.body;

  const updateQuery = 'UPDATE messages SET isArchived = TRUE WHERE id = ? AND receiverId = ?';

  db.query(updateQuery, [messageId, userId], (err, result) => {
    if (err) {
      console.error('Error archiving message:', err);
      return res.status(500).json({
        success: false,
        message: 'Error archiving message',
        error: err.message
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or you are not the recipient'
      });
    }

    res.json({
      success: true,
      message: 'Message archived'
    });
  });
});

// DELETE message
app.delete('/api/messages/:messageId', (req, res) => {
  const messageId = req.params.messageId;
  const { userId } = req.body;

  // Check if user is sender or receiver
  const checkQuery = 'SELECT * FROM messages WHERE id = ? AND (senderId = ? OR receiverId = ?)';

  db.query(checkQuery, [messageId, userId, userId], (err, results) => {
    if (err) {
      console.error('Error checking message ownership:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking message ownership',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or you do not have access'
      });
    }

    // Delete the message
    const deleteQuery = 'DELETE FROM messages WHERE id = ?';

    db.query(deleteQuery, [messageId], (err, result) => {
      if (err) {
        console.error('Error deleting message:', err);
        return res.status(500).json({
          success: false,
          message: 'Error deleting message',
          error: err.message
        });
      }

      res.json({
        success: true,
        message: 'Message deleted successfully'
      });
    });
  });
});

// Role Permissions API Routes

// GET user permissions
app.get('/api/permissions/:userId', (req, res) => {
  const userId = req.params.userId;

  // First get user role
  const getUserQuery = 'SELECT role FROM users WHERE id = ? AND isActive = TRUE';

  db.query(getUserQuery, [userId], (err, userResults) => {
    if (err) {
      console.error('Error fetching user:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching user',
        error: err.message
      });
    }

    if (userResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    const userRole = userResults[0].role;

    // Get permissions for user role
    const getPermissionsQuery = 'SELECT resource, action, allowed FROM role_permissions WHERE role = ? AND allowed = TRUE';

    db.query(getPermissionsQuery, [userRole], (err, permissionResults) => {
      if (err) {
        console.error('Error fetching permissions:', err);
        return res.status(500).json({
          success: false,
          message: 'Error fetching permissions',
          error: err.message
        });
      }

      // Format permissions for easier frontend use
      const permissions = {};
      permissionResults.forEach(perm => {
        if (!permissions[perm.resource]) {
          permissions[perm.resource] = [];
        }
        permissions[perm.resource].push(perm.action);
      });

      res.json({
        success: true,
        role: userRole,
        permissions: permissions,
        count: permissionResults.length
      });
    });
  });
});

// Check specific permission
app.post('/api/permissions/check', (req, res) => {
  const { userId, resource, action } = req.body;

  if (!userId || !resource || !action) {
    return res.status(400).json({
      success: false,
      message: 'userId, resource, and action are required'
    });
  }

  // Get user role
  const getUserQuery = 'SELECT role FROM users WHERE id = ? AND isActive = TRUE';

  db.query(getUserQuery, [userId], (err, userResults) => {
    if (err) {
      console.error('Error fetching user:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching user',
        error: err.message
      });
    }

    if (userResults.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    const userRole = userResults[0].role;

    // Check specific permission
    const checkPermissionQuery = `
      SELECT allowed FROM role_permissions
      WHERE role = ? AND resource = ? AND (action = ? OR action = 'manage') AND allowed = TRUE
    `;

    db.query(checkPermissionQuery, [userRole, resource, action], (err, permissionResults) => {
      if (err) {
        console.error('Error checking permission:', err);
        return res.status(500).json({
          success: false,
          message: 'Error checking permission',
          error: err.message
        });
      }

      const hasPermission = permissionResults.length > 0;

      res.json({
        success: true,
        hasPermission: hasPermission,
        role: userRole,
        resource: resource,
        action: action
      });
    });
  });
});

// SMS API Routes

// Test SMS functionality
app.post('/api/sms/test', async (req, res) => {
  const { phoneNumber } = req.body;

  if (!phoneNumber) {
    return res.status(400).json({
      success: false,
      message: 'Phone number is required'
    });
  }

  try {
    const result = await smsService.testSMS(phoneNumber);
    res.json(result);
  } catch (error) {
    console.error('Error testing SMS:', error);
    res.status(500).json({
      success: false,
      message: 'Error testing SMS service',
      error: error.message
    });
  }
});

// Send custom SMS notification
app.post('/api/sms/send', async (req, res) => {
  const { phoneNumber, patientName, subject, message } = req.body;

  if (!phoneNumber || !patientName || !subject || !message) {
    return res.status(400).json({
      success: false,
      message: 'Required fields: phoneNumber, patientName, subject, message'
    });
  }

  try {
    const result = await smsService.sendGeneralNotification(phoneNumber, patientName, subject, message);
    res.json(result);
  } catch (error) {
    console.error('Error sending custom SMS:', error);
    res.status(500).json({
      success: false,
      message: 'Error sending SMS',
      error: error.message
    });
  }
});

// Send appointment reminder SMS
app.post('/api/sms/appointment-reminder', async (req, res) => {
  const { appointmentId } = req.body;

  if (!appointmentId) {
    return res.status(400).json({
      success: false,
      message: 'Appointment ID is required'
    });
  }

  try {
    // Get appointment details with patient and doctor info
    const getAppointmentQuery = `
      SELECT a.*,
             p.firstName as patientFirstName, p.lastName as patientLastName, p.phone as patientPhone,
             d.firstName as doctorFirstName, d.lastName as doctorLastName
      FROM appointments a
      JOIN patients p ON a.nationalId = p.nationalId
      JOIN doctors d ON a.doctorId = d.doctorId
      WHERE a.appointmentId = ?
    `;

    db.query(getAppointmentQuery, [appointmentId], async (err, results) => {
      if (err) {
        console.error('Error fetching appointment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error fetching appointment',
          error: err.message
        });
      }

      if (results.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      const appointment = results[0];
      const patientName = `${appointment.patientFirstName} ${appointment.patientLastName}`;
      const patientPhone = appointment.patientPhone;

      if (!patientPhone) {
        return res.status(400).json({
          success: false,
          message: 'Patient phone number not available'
        });
      }

      const smsResult = await smsService.sendAppointmentReminder(patientPhone, patientName, appointment);
      res.json(smsResult);
    });
  } catch (error) {
    console.error('Error sending appointment reminder:', error);
    res.status(500).json({
      success: false,
      message: 'Error sending appointment reminder',
      error: error.message
    });
  }
});

// Get SMS service status
app.get('/api/sms/status', (req, res) => {
  res.json({
    success: true,
    configured: smsService.isConfigured,
    message: smsService.isConfigured ? 'SMS service is configured and ready' : 'SMS service is not configured (demo mode)',
    timestamp: new Date().toISOString()
  });
});

// Billing Management API Routes

// Generate invoice number
const generateInvoiceNumber = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `INV-${year}${month}${day}-${random}`;
};

// GET all bills
app.get('/api/billing', (req, res) => {
  const query = 'SELECT * FROM billing ORDER BY createdAt DESC';

  db.query(query, (err, results) => {
    if (err) {
      console.error('Error fetching bills:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching bills',
        error: err.message
      });
    }

    // Parse JSON items for each bill
    const bills = results.map(bill => ({
      ...bill,
      items: JSON.parse(bill.items)
    }));

    res.json({
      success: true,
      data: bills,
      count: bills.length
    });
  });
});

// GET single bill by ID
app.get('/api/billing/:id', (req, res) => {
  const billId = parseInt(req.params.id);
  const query = 'SELECT * FROM billing WHERE id = ?';

  db.query(query, [billId], (err, results) => {
    if (err) {
      console.error('Error fetching bill:', err);
      return res.status(500).json({
        success: false,
        message: 'Error fetching bill',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Bill not found'
      });
    }

    const bill = {
      ...results[0],
      items: JSON.parse(results[0].items)
    };

    res.json({
      success: true,
      data: bill
    });
  });
});

// POST create new bill
app.post('/api/billing', (req, res) => {
  console.log('=== BILLING REQUEST RECEIVED ===');
  console.log('Request body:', JSON.stringify(req.body, null, 2));

  const {
    patientNationalId,
    patientName,
    patientEmail,
    patientPhone,
    patientAddress,
    consultationFees,
    examFees,
    items,
    subtotal,
    insurancePercentage,
    insuranceAmount,
    totalAmountToBePaid,
    amountPaid,
    paymentMethod,
    paymentStatus,
    notes
  } = req.body;

  console.log('Extracted fields:');
  console.log('- patientName:', patientName);
  console.log('- items:', items);
  console.log('- totalAmountToBePaid:', totalAmountToBePaid);
  console.log('- paymentMethod:', paymentMethod);

  // Basic validation
  if (!patientName || !items || !Array.isArray(items) || !totalAmountToBePaid) {
    console.log('Validation failed:');
    console.log('- patientName exists:', !!patientName);
    console.log('- items exists:', !!items);
    console.log('- items is array:', Array.isArray(items));
    console.log('- totalAmountToBePaid exists:', !!totalAmountToBePaid);

    return res.status(400).json({
      success: false,
      message: 'Required fields: patientName, items, totalAmountToBePaid',
      received: {
        patientName: !!patientName,
        items: !!items,
        itemsIsArray: Array.isArray(items),
        totalAmountToBePaid: !!totalAmountToBePaid
      }
    });
  }

  const invoiceNumber = generateInvoiceNumber();
  console.log('Generated invoice number:', invoiceNumber);

  const insertQuery = `
    INSERT INTO billing
    (invoiceNumber, patientNationalId, patientName, patientEmail, patientPhone, patientAddress,
     consultationFees, examFees, items, subtotal, insurancePercentage, insuranceAmount,
     totalAmountToBePaid, amountPaid, paymentMethod, paymentStatus, notes)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const values = [
    invoiceNumber,
    patientNationalId || null,
    patientName,
    patientEmail || null,
    patientPhone || null,
    patientAddress || null,
    consultationFees || 0,
    examFees || 0,
    JSON.stringify(items),
    subtotal || 0,
    insurancePercentage || 0,
    insuranceAmount || 0,
    totalAmountToBePaid,
    amountPaid || 0,
    paymentMethod || 'Cash',
    paymentStatus || 'Pending',
    notes || null
  ];

  console.log('SQL Query:', insertQuery);
  console.log('Values to insert:', values);

  db.query(insertQuery, values, (err, result) => {
    if (err) {
      console.error('=== DATABASE ERROR ===');
      console.error('Error creating bill:', err);
      console.error('Error code:', err.code);
      console.error('Error message:', err.message);
      console.error('SQL State:', err.sqlState);
      console.error('Values that caused error:', values);

      return res.status(500).json({
        success: false,
        message: 'Error creating bill',
        error: err.message,
        errorCode: err.code,
        sqlState: err.sqlState
      });
    }

    console.log('Bill created successfully with ID:', result.insertId);

    // Get the created bill
    const getQuery = 'SELECT * FROM billing WHERE id = ?';
    db.query(getQuery, [result.insertId], (err, billResults) => {
      if (err) {
        console.error('Error fetching created bill:', err);
        return res.status(500).json({
          success: false,
          message: 'Bill created but error fetching data',
          error: err.message
        });
      }

      const bill = {
        ...billResults[0],
        items: JSON.parse(billResults[0].items)
      };

      res.status(201).json({
        success: true,
        message: 'Bill created successfully',
        data: bill
      });
    });
  });
});

// PUT update bill payment
app.put('/api/billing/:id/payment', (req, res) => {
  const billId = parseInt(req.params.id);
  const {
    amountPaid,
    paymentStatus,
    paymentMethod,
    transactionId,
    flutterwaveRef,
    paymentType
  } = req.body;

  // Validate required fields
  if (!amountPaid || !paymentStatus || !paymentMethod) {
    return res.status(400).json({
      success: false,
      message: 'Amount paid, payment status, and payment method are required'
    });
  }

  // First check if bill exists
  const checkQuery = 'SELECT * FROM billing WHERE id = ?';
  db.query(checkQuery, [billId], (err, results) => {
    if (err) {
      console.error('Error checking bill:', err);
      return res.status(500).json({
        success: false,
        message: 'Error checking bill',
        error: err.message
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Bill not found'
      });
    }

    // Update the bill with payment information
    const updateQuery = `
      UPDATE billing
      SET
        amountPaid = ?,
        paymentStatus = ?,
        paymentMethod = ?,
        transactionId = ?,
        flutterwaveRef = ?,
        paymentType = ?,
        updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const values = [
      amountPaid,
      paymentStatus,
      paymentMethod,
      transactionId || null,
      flutterwaveRef || null,
      paymentType || null,
      billId
    ];

    db.query(updateQuery, values, (err, result) => {
      if (err) {
        console.error('Error updating bill payment:', err);
        return res.status(500).json({
          success: false,
          message: 'Error updating bill payment',
          error: err.message
        });
      }

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Bill not found or no changes made'
        });
      }

      // Get the updated bill
      const getQuery = 'SELECT * FROM billing WHERE id = ?';
      db.query(getQuery, [billId], (err, billResults) => {
        if (err) {
          console.error('Error fetching updated bill:', err);
          return res.status(500).json({
            success: false,
            message: 'Payment updated but error fetching data',
            error: err.message
          });
        }

        const bill = {
          ...billResults[0],
          items: JSON.parse(billResults[0].items)
        };

        res.json({
          success: true,
          message: 'Bill payment updated successfully',
          data: bill
        });
      });
    });
  });
});

// Test route
app.get('/api/test', (req, res) => {
  res.json({ message: 'Backend API is working!' });
});

// Test database and billing table
app.get('/api/test-billing', (req, res) => {
  // Test database connection
  db.query('SELECT 1', (err, result) => {
    if (err) {
      return res.status(500).json({
        success: false,
        message: 'Database connection failed',
        error: err.message
      });
    }

    // Test billing table
    db.query('DESCRIBE billing', (err, result) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: 'Billing table not found or accessible',
          error: err.message
        });
      }

      res.json({
        success: true,
        message: 'Database and billing table are working!',
        tableStructure: result
      });
    });
  });
});

// Health check route
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Start server
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
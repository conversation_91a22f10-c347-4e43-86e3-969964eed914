{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Healthcare\\\\frontend\\\\src\\\\context\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Session timeout (optional - 8 hours)\n  const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 hours in milliseconds\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        const loginTime = sessionStorage.getItem('loginTime');\n        if (token && userData && loginTime) {\n          const currentTime = new Date().getTime();\n          const sessionAge = currentTime - parseInt(loginTime);\n\n          // Check if session has expired (optional timeout check)\n          if (sessionAge > SESSION_TIMEOUT) {\n            console.log('Session expired, logging out...');\n            sessionStorage.clear();\n            setUser(null);\n          } else {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n          }\n        }\n      } catch (error) {\n        console.error('Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n        sessionStorage.removeItem('loginTime');\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n\n    // Add session management for browser close detection\n    const handleBeforeUnload = event => {\n      // This will clear session when browser/tab is closed\n      // Note: sessionStorage automatically clears on browser close,\n      // but this ensures immediate cleanup\n      if (event.type === 'beforeunload') {\n        sessionStorage.clear();\n      }\n    };\n\n    // Add event listener for browser close\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Cleanup event listener\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  // Login function\n  const login = userData => {\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    const loginTime = new Date().getTime();\n    sessionStorage.setItem('authToken', userData.token || 'authenticated');\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    sessionStorage.setItem('loginTime', loginTime.toString());\n    console.log('User logged in:', userData);\n  };\n\n  // Logout function\n  const logout = () => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    setUser(null);\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = role => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "SESSION_TIMEOUT", "checkAuth", "token", "sessionStorage", "getItem", "userData", "loginTime", "currentTime", "Date", "getTime", "sessionAge", "parseInt", "console", "log", "clear", "parsedUser", "JSON", "parse", "error", "removeItem", "handleBeforeUnload", "event", "type", "window", "addEventListener", "removeEventListener", "login", "setItem", "stringify", "toString", "logout", "isAuthenticated", "hasRole", "role", "isAdmin", "userType", "isStaff", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Healthcare/frontend/src/context/AuthContext.jsx"], "sourcesContent": ["import { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Session timeout (optional - 8 hours)\n  const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 hours in milliseconds\n\n  // Check for existing authentication on app load\n  useEffect(() => {\n    const checkAuth = () => {\n      try {\n        // Use sessionStorage for session-based authentication\n        // This will clear when browser/tab is closed but persist on refresh\n        const token = sessionStorage.getItem('authToken');\n        const userData = sessionStorage.getItem('userData');\n        const loginTime = sessionStorage.getItem('loginTime');\n\n        if (token && userData && loginTime) {\n          const currentTime = new Date().getTime();\n          const sessionAge = currentTime - parseInt(loginTime);\n\n          // Check if session has expired (optional timeout check)\n          if (sessionAge > SESSION_TIMEOUT) {\n            console.log('Session expired, logging out...');\n            sessionStorage.clear();\n            setUser(null);\n          } else {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n          }\n        }\n      } catch (error) {\n        console.error('Error checking authentication:', error);\n        // Clear invalid data\n        sessionStorage.removeItem('authToken');\n        sessionStorage.removeItem('userData');\n        sessionStorage.removeItem('loginTime');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n\n    // Add session management for browser close detection\n    const handleBeforeUnload = (event) => {\n      // This will clear session when browser/tab is closed\n      // Note: sessionStorage automatically clears on browser close,\n      // but this ensures immediate cleanup\n      if (event.type === 'beforeunload') {\n        sessionStorage.clear();\n      }\n    };\n\n    // Add event listener for browser close\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Cleanup event listener\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, []);\n\n  // Login function\n  const login = (userData) => {\n    setUser(userData);\n    // Store authentication data in sessionStorage for session-based auth\n    const loginTime = new Date().getTime();\n    sessionStorage.setItem('authToken', userData.token || 'authenticated');\n    sessionStorage.setItem('userData', JSON.stringify(userData));\n    sessionStorage.setItem('loginTime', loginTime.toString());\n    console.log('User logged in:', userData);\n  };\n\n  // Logout function\n  const logout = () => {\n    // Clear session storage for session-based authentication\n    sessionStorage.removeItem('authToken');\n    sessionStorage.removeItem('userData');\n    setUser(null);\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user;\n  };\n\n  // Check if user has specific role\n  const hasRole = (role) => {\n    return user && user.role === role;\n  };\n\n  // Check if user is admin\n  const isAdmin = () => {\n    return user && (user.role === 'admin' || user.userType === 'admin');\n  };\n\n  // Check if user is staff (doctor/nurse)\n  const isStaff = () => {\n    return user && (user.role === 'doctor' || user.role === 'nurse' || user.userType === 'staff');\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    isAuthenticated,\n    hasRole,\n    isAdmin,\n    isStaff,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMgB,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;EAE5C;EACAf,SAAS,CAAC,MAAM;IACd,MAAMgB,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF;QACA;QACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QACjD,MAAMC,QAAQ,GAAGF,cAAc,CAACC,OAAO,CAAC,UAAU,CAAC;QACnD,MAAME,SAAS,GAAGH,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;QAErD,IAAIF,KAAK,IAAIG,QAAQ,IAAIC,SAAS,EAAE;UAClC,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;UACxC,MAAMC,UAAU,GAAGH,WAAW,GAAGI,QAAQ,CAACL,SAAS,CAAC;;UAEpD;UACA,IAAII,UAAU,GAAGV,eAAe,EAAE;YAChCY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CV,cAAc,CAACW,KAAK,CAAC,CAAC;YACtBjB,OAAO,CAAC,IAAI,CAAC;UACf,CAAC,MAAM;YACL,MAAMkB,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACZ,QAAQ,CAAC;YACvCR,OAAO,CAACkB,UAAU,CAAC;UACrB;QACF;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACAf,cAAc,CAACgB,UAAU,CAAC,WAAW,CAAC;QACtChB,cAAc,CAACgB,UAAU,CAAC,UAAU,CAAC;QACrChB,cAAc,CAACgB,UAAU,CAAC,WAAW,CAAC;MACxC,CAAC,SAAS;QACRpB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMmB,kBAAkB,GAAIC,KAAK,IAAK;MACpC;MACA;MACA;MACA,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;QACjCnB,cAAc,CAACW,KAAK,CAAC,CAAC;MACxB;IACF,CAAC;;IAED;IACAS,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEJ,kBAAkB,CAAC;;IAE3D;IACA,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEL,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,KAAK,GAAIrB,QAAQ,IAAK;IAC1BR,OAAO,CAACQ,QAAQ,CAAC;IACjB;IACA,MAAMC,SAAS,GAAG,IAAIE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtCN,cAAc,CAACwB,OAAO,CAAC,WAAW,EAAEtB,QAAQ,CAACH,KAAK,IAAI,eAAe,CAAC;IACtEC,cAAc,CAACwB,OAAO,CAAC,UAAU,EAAEX,IAAI,CAACY,SAAS,CAACvB,QAAQ,CAAC,CAAC;IAC5DF,cAAc,CAACwB,OAAO,CAAC,WAAW,EAAErB,SAAS,CAACuB,QAAQ,CAAC,CAAC,CAAC;IACzDjB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAER,QAAQ,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMyB,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA3B,cAAc,CAACgB,UAAU,CAAC,WAAW,CAAC;IACtChB,cAAc,CAACgB,UAAU,CAAC,UAAU,CAAC;IACrCtB,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAACnC,IAAI;EACf,CAAC;;EAED;EACA,MAAMoC,OAAO,GAAIC,IAAI,IAAK;IACxB,OAAOrC,IAAI,IAAIA,IAAI,CAACqC,IAAI,KAAKA,IAAI;EACnC,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOtC,IAAI,KAAKA,IAAI,CAACqC,IAAI,KAAK,OAAO,IAAIrC,IAAI,CAACuC,QAAQ,KAAK,OAAO,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,OAAOxC,IAAI,KAAKA,IAAI,CAACqC,IAAI,KAAK,QAAQ,IAAIrC,IAAI,CAACqC,IAAI,KAAK,OAAO,IAAIrC,IAAI,CAACuC,QAAQ,KAAK,OAAO,CAAC;EAC/F,CAAC;EAED,MAAME,KAAK,GAAG;IACZzC,IAAI;IACJ8B,KAAK;IACLI,MAAM;IACNC,eAAe;IACfC,OAAO;IACPE,OAAO;IACPE,OAAO;IACPtC;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CAtHWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAwHzB,eAAeL,WAAW;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
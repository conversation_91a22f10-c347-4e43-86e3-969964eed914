import { useState, useEffect } from 'react';

const BillingForm = ({ onBillCreated }) => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    patientNationalId: '',
    patientName: '',
    patientEmail: '',
    patientPhone: '',
    patientAddress: '',
    consultationFees: 0,
    examFees: 0,
    items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],
    subtotal: 0,
    insurancePercentage: 0,
    insuranceAmount: 0,
    totalAmountToBePaid: 0,
    amountPaid: 0,
    paymentMethod: 'Cash',
    paymentStatus: 'Pending',
    notes: ''
  });

  const API_BASE_URL = 'http://localhost:5000/api';

  // Fetch patients on component mount
  useEffect(() => {
    fetchPatients();
  }, []);

  // Fetch all patients
  const fetchPatients = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/patients`);
      const data = await response.json();
      if (data.success) {
        setPatients(data.data);
      }
    } catch (error) {
      console.error('Error fetching patients:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle patient selection
  const handlePatientSelect = (e) => {
    const nationalId = e.target.value;
    const patient = patients.find(p => p.nationalId === nationalId);
    
    if (patient) {
      setFormData(prev => ({
        ...prev,
        patientNationalId: patient.nationalId,
        patientName: `${patient.firstName} ${patient.lastName}`,
        patientEmail: patient.email,
        patientPhone: patient.phone,
        patientAddress: patient.address
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        patientNationalId: '',
        patientName: '',
        patientEmail: '',
        patientPhone: '',
        patientAddress: ''
      }));
    }
  };

  // Handle item changes
  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items];
    newItems[index][field] = value;
    
    // Calculate item total
    if (field === 'quantity' || field === 'unitPrice') {
      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;
    }
    
    setFormData(prev => ({
      ...prev,
      items: newItems
    }));
    
    // Recalculate totals
    calculateTotals(newItems);
  };

  // Add new item
  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { description: '', quantity: 1, unitPrice: 0, total: 0 }]
    }));
  };

  // Remove item
  const removeItem = (index) => {
    const newItems = formData.items.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      items: newItems
    }));
    calculateTotals(newItems);
  };

  // Calculate totals
  const calculateTotals = (items, consultationFees = formData.consultationFees, examFees = formData.examFees, insurancePercentage = formData.insurancePercentage) => {
    const itemsTotal = items.reduce((sum, item) => sum + item.total, 0);
    const subtotal = itemsTotal + consultationFees + examFees;
    const insuranceAmount = (subtotal * insurancePercentage) / 100;
    const totalAmountToBePaid = subtotal - insuranceAmount;

    setFormData(prev => ({
      ...prev,
      subtotal: subtotal,
      insuranceAmount: insuranceAmount,
      totalAmountToBePaid: totalAmountToBePaid
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/billing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        alert('Bill created successfully!');
        resetForm();
        // Call the callback if provided
        if (onBillCreated) {
          onBillCreated();
        }
      } else {
        alert(`Error: ${data.message}`);
      }
    } catch (error) {
      console.error('Error creating bill:', error);
      alert('Error creating bill. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      patientNationalId: '',
      patientName: '',
      patientEmail: '',
      patientPhone: '',
      patientAddress: '',
      consultationFees: 0,
      examFees: 0,
      items: [{ description: '', quantity: 1, unitPrice: 0, total: 0 }],
      subtotal: 0,
      insurancePercentage: 0,
      insuranceAmount: 0,
      totalAmountToBePaid: 0,
      amountPaid: 0,
      paymentMethod: 'Cash',
      paymentStatus: 'Pending',
      notes: ''
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Patient Information */}
      <div className="bg-gray-50 rounded-xl p-4">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">👤 Patient Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Select Patient</label>
            <select
              value={formData.patientNationalId}
              onChange={handlePatientSelect}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a patient or enter manually</option>
              {patients.map((patient) => (
                <option key={patient.nationalId} value={patient.nationalId}>
                  {patient.firstName} {patient.lastName} - {patient.nationalId}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Patient Name *</label>
            <input
              type="text"
              name="patientName"
              value={formData.patientName}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              name="patientEmail"
              value={formData.patientEmail}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
            <input
              type="tel"
              name="patientPhone"
              value={formData.patientPhone}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <textarea
              name="patientAddress"
              value={formData.patientAddress}
              onChange={handleInputChange}
              rows="2"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Consultation & Exam Fees */}
      <div className="bg-green-50 rounded-xl p-4 border border-green-100">
        <h4 className="text-lg font-semibold text-green-900 mb-4">🩺 Consultation & Examination Fees</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Consultation Fees (RWF)</label>
            <input
              type="number"
              name="consultationFees"
              value={formData.consultationFees}
              onChange={(e) => {
                const fees = parseFloat(e.target.value) || 0;
                setFormData(prev => ({ ...prev, consultationFees: fees }));
                calculateTotals(formData.items, fees, formData.examFees, formData.insurancePercentage);
              }}
              placeholder="Enter consultation fees"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Examination Fees (RWF)</label>
            <input
              type="number"
              name="examFees"
              value={formData.examFees}
              onChange={(e) => {
                const fees = parseFloat(e.target.value) || 0;
                setFormData(prev => ({ ...prev, examFees: fees }));
                calculateTotals(formData.items, formData.consultationFees, fees, formData.insurancePercentage);
              }}
              placeholder="Enter examination fees"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Medical Services & Charges */}
      <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-blue-900">🏥 Medical Services & Charges</h4>
          <button
            type="button"
            onClick={addItem}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
            </svg>
            Add Service
          </button>
        </div>

        <div className="space-y-3">
          {formData.items.map((item, index) => (
            <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 bg-white p-3 rounded-lg">
              <div className="md:col-span-2">
                <input
                  type="text"
                  placeholder="Medical Service (e.g., Consultation, Lab Test, Surgery)"
                  value={item.description}
                  onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <input
                  type="number"
                  placeholder="Qty"
                  value={item.quantity}
                  onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <input
                  type="number"
                  placeholder="Rate (RWF)"
                  value={item.unitPrice}
                  onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  placeholder="Amount (RWF)"
                  value={item.total}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-blue-50 font-semibold"
                />
                {formData.items.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeItem(index)}
                    className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Payment & Insurance Details */}
      <div className="bg-purple-50 rounded-xl p-4 border border-purple-100">
        <h4 className="text-lg font-semibold text-purple-900 mb-4">💳 Payment & Insurance Information</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Insurance Coverage (%)</label>
            <input
              type="number"
              name="insurancePercentage"
              value={formData.insurancePercentage}
              onChange={(e) => {
                const percentage = parseFloat(e.target.value) || 0;
                setFormData(prev => ({ ...prev, insurancePercentage: percentage }));
                calculateTotals(formData.items, formData.consultationFees, formData.examFees, percentage);
              }}
              placeholder="Enter insurance coverage percentage"
              min="0"
              max="100"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">Enter percentage (0-100%) that insurance will cover</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
            <select
              name="paymentMethod"
              value={formData.paymentMethod}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="Cash">Cash Payment</option>
              <option value="Card">Credit/Debit Card</option>
              <option value="Insurance">Health Insurance</option>
              <option value="Mobile Money">Mobile Money (MTN/Airtel)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Amount Paid (RWF)</label>
            <input
              type="number"
              name="amountPaid"
              value={formData.amountPaid}
              onChange={(e) => {
                const paid = parseFloat(e.target.value) || 0;
                setFormData(prev => ({
                  ...prev,
                  amountPaid: paid,
                  paymentStatus: paid >= prev.totalAmountToBePaid ? 'Paid' : paid > 0 ? 'Partial' : 'Pending'
                }));
              }}
              placeholder="Enter amount paid"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
            <select
              name="paymentStatus"
              value={formData.paymentStatus}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="Pending">Pending Payment</option>
              <option value="Paid">Fully Paid</option>
              <option value="Partial">Partially Paid</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Medical Notes</label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows="3"
              placeholder="Additional medical or billing notes..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Bill Summary */}
      <div className="bg-yellow-50 rounded-xl p-4 border border-yellow-100">
        <h4 className="text-lg font-semibold text-yellow-900 mb-4">💰 Medical Bill Summary</h4>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Consultation Fees:</span>
            <span className="font-semibold">{formData.consultationFees.toFixed(2)} RWF</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Examination Fees:</span>
            <span className="font-semibold">{formData.examFees.toFixed(2)} RWF</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Other Services:</span>
            <span className="font-semibold">{formData.items.reduce((sum, item) => sum + item.total, 0).toFixed(2)} RWF</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between text-lg font-bold">
              <span>Subtotal:</span>
              <span className="text-blue-700">{formData.subtotal.toFixed(2)} RWF</span>
            </div>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Insurance Coverage ({formData.insurancePercentage}%):</span>
            <span className="font-semibold text-green-600">-{formData.insuranceAmount.toFixed(2)} RWF</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between text-xl font-bold">
              <span>Total Amount to be Paid:</span>
              <span className="text-red-600">{formData.totalAmountToBePaid.toFixed(2)} RWF</span>
            </div>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Amount Paid:</span>
            <span className="font-semibold text-green-600">{formData.amountPaid.toFixed(2)} RWF</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between text-lg font-bold">
              <span>Balance Due:</span>
              <span className={`${(formData.totalAmountToBePaid - formData.amountPaid) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {(formData.totalAmountToBePaid - formData.amountPaid).toFixed(2)} RWF
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={loading}
          className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 flex items-center gap-2"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {loading ? 'Generating Bill...' : 'Generate Medical Bill'}
        </button>
      </div>
    </form>
  );
};

export default BillingForm;
